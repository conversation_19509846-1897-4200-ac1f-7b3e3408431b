<template>
  <div class="product-settings-wrapper">
    <!-- Layout em 2 colunas: Selects à esquerda e Switchers à direita -->
    <div class="settings-layout">
      <!-- Coluna da esquerda - Selects -->
      <div class="selects-column">
        <IluriaSelectButton 
          :label="t('product.type')" 
          id="type" 
          :model-value="type"
          @update:model-value="$emit('update:type', $event)"
          :options="typeOptions" 
          option-label="label" 
          option-value="value" 
          :formContext="props.formContext?.type"
        />

        <IluriaSelectButton 
          :label="t('product.status')" 
          id="status" 
          :model-value="status"
          @update:model-value="$emit('update:status', $event)"
          :options="statusOptions"
          option-label="label" 
          option-value="value"
        />
      </div>

      <!-- Coluna da direita - Switchers -->
      <div class="switchers-column">
        <IluriaToggleSwitch 
          :model-value="highlight"
          @update:model-value="$emit('update:highlight', $event)"
          :label="t('product.highlight')"
        />

        <IluriaToggleSwitch 
          :model-value="newTag"
          @update:model-value="$emit('update:newTag', $event)"
          :label="t('product.newTag')" 
        />

        <IluriaToggleSwitch 
          :model-value="hasVariation"
          @update:model-value="$emit('update:hasVariation', $event)"
          :label="t('product.hasVariation')"
          v-if="type !== 'GIFT'"
        />
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, watch, computed, nextTick, onMounted } from 'vue';
import { useI18n } from 'vue-i18n';
import IluriaToggleSwitch from '@/components/iluria/IluriaToggleSwitch.vue';
import IluriaSelectButton from '../iluria/form/IluriaSelectButton.vue';

const { t } = useI18n();

// Define props
const props = defineProps({
  type: {
    type: String,
    default: 'PHYSICAL'
  },
  hasVariation: {
    type: Boolean,
    default: false
  },
  status: {
    type: String,
    default: 'ACTIVE'
  },
  highlight: {
    type: Boolean,
    default: false
  },
  newTag: {
    type: Boolean,
    default: false
  },
  formContext: {
    type: Object,
    default: () => ({})
  }
});

// Define emits
const emit = defineEmits([
  'update:type',
  'update:hasVariation', 
  'update:status',
  'update:highlight',
  'update:newTag'
]);

// Type options
const typeOptions = ref([
  { label: t('product.typePhysical'), value: 'PHYSICAL' },
  { label: t('product.typeDigital'), value: 'DIGITAL' },
  { label: t('product.typeGift'), value: 'GIFT' },
]);

// Status options
const statusOptions = ref([
  { label: t('product.statusActive'), value: 'ACTIVE' },
  { label: t('product.statusDraft'), value: 'DRAFT' }
]);



// Define validation rules
const validationRules = {
  status: {},
  type: {},
  typeTest: {},
  typeTest1: {}
};

// Expose validation rules
defineExpose({ validationRules });
</script>

<style scoped>
.product-settings-wrapper {
  display: flex;
  flex-direction: column;
  gap: 24px;
  align-items: center;
  width: 100%;
}

/* Layout principal em 2 colunas */
.settings-layout {
  display: flex;
  width: 100%;
  gap: 60px;
  justify-content: space-between;
  align-items: flex-start;
  max-width: 800px;
  margin: 0 auto;
}

/* Coluna da esquerda - Selects */
.selects-column {
  display: flex;
  flex-direction: column;
  gap: 24px;
  min-width: 280px;
  flex: 1;
  max-width: 350px;
}

/* Coluna da direita - Switchers */
.switchers-column {
  display: flex;
  flex-direction: column;
  gap: 20px;
  min-width: 280px;
  flex: 1;
  max-width: 350px;
  align-items: flex-start;
}

/* Ajustes para o IluriaToggleSwitch dentro das fileiras */
.switches-row :deep(.custom-toggle-switch) {
  margin: 0 auto;
}

/* Classes antigas mantidas para compatibilidade */
.switch-group {
  display: flex;
  flex-direction: column;
  gap: 8px;
  align-items: center;
  min-width: 120px;
}

.switch-label {
  font-size: 14px;
  font-weight: 500;
  color: var(--iluria-color-text-primary);
  margin-bottom: 4px;
  text-align: center;
}

.switch-container {
  display: flex;
  align-items: center;
  gap: 12px;
  justify-content: center;
}

.switch-state-text {
  font-size: 14px;
  color: var(--iluria-color-text-secondary);
  font-weight: 500;
  transition: color 0.3s ease;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .settings-layout {
    gap: 32px;
    max-width: 600px;
  }
  
  .selects-column,
  .switchers-column {
    min-width: 240px;
  }
  
  .switchers-column {
    gap: 16px;
  }
}

@media (max-width: 640px) {
  .settings-layout {
    flex-direction: column;
    gap: 32px;
    align-items: stretch;
    max-width: 400px;
  }
  
  .selects-column,
  .switchers-column {
    width: 100%;
    max-width: none;
    min-width: auto;
  }
  
  .switchers-column {
    align-items: stretch;
  }
  
  .product-settings-wrapper {
    gap: 24px;
  }
}
</style>

