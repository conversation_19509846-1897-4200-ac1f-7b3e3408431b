<template>
  <div class="page-container">
    <!-- Page Header -->
    <IluriaHeader
      :title="pageTitle"
      :subtitle="pageSubtitle"
      :customButtons="getHeaderButtons()"
      @custom-click="handleCustomButtonClick"
    />

    <!-- Main Content -->
    <div v-if="currentView === 'main'" key="mainView" class="main-content">
      <!-- Categories Filter -->
      <div class="categories-filter mb-6" v-if="categories.length && initialDataLoaded && hasAnyGroups">
        <h2 class="text-lg font-semibold mb-4">{{ $t('community.management.categories') }}</h2>
        <div class="flex flex-wrap gap-3">
          <button
            v-for="cat in categories"
            :key="cat.id"
            @click="toggleCategory(cat)"
            class="px-4 py-2 rounded-full text-sm transition-colors"
            :class="cat.id === selectedCategory?.id ? 'bg-[var(--iluria-color-text-primary)] text-white' : 'bg-[var(--iluria-color-container-bg)] text-[var(--iluria-color-text-primary)]'"
          >
            {{ cat.name }}
          </button>
        </div>
      </div>

      <!-- Search and Filter -->
      <div class="search-filter" v-if="initialDataLoaded && hasAnyGroups">
        <div class="search-container">
          <div class="search-input-wrapper">
            <svg class="search-icon" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
            </svg>
            <input
              v-model="searchQuery"
              type="text"
              :placeholder="$t('community.management.searchPlaceholder')"
              class="search-input"
            />
          </div>
        </div>
        <div class="filter-container">
          <select
            v-model="sortBy"
            class="sort-select"
          >
            <option value="trending">{{ $t('community.management.sort.trending') }}</option>
            <option value="newest">{{ $t('community.management.sort.newest') }}</option>
            <option value="members">{{ $t('community.management.sort.members') }}</option>
            <option value="active">{{ $t('community.management.sort.active') }}</option>
          </select>
        </div>
      </div>

      <!-- Groups List -->
      <div v-if="!loading && groups.length > 0" class="groups-grid">
        <div
          v-for="group in groups"
          :key="group.id"
          class="group-card"
        >
          <!-- Group Image -->
          <div class="group-image-container">
            <img
              v-if="group.imageUrl"
              :src="group.imageUrl"
              :alt="group.name"
              class="group-image"
              @click="handleEdit(group)"
            />
            <div v-else class="group-image-placeholder" @click="handleEdit(group)">
              <span>{{ group.name.charAt(0).toUpperCase() }}</span>
            </div>
          </div>

          <!-- Group Info -->
          <div class="group-info">
            <h3 class="group-title">{{ group.name }}</h3>
            <p class="group-description">{{ group.description }}</p>
          </div>

          <!-- Group Stats -->
          <div class="group-stats">
            <span class="stat-item">
              <span class="stat-icon">👥</span>
              {{ group.memberCount }}
            </span>
            <span class="stat-item">
              <span class="stat-icon">💬</span>
              {{ group.postCount }}
            </span>
          </div>

          <!-- Action Buttons -->
          <div class="action-buttons">
            <IluriaButton
              variant="ghost"
              color="primary"
              size="small"
              :hugeIcon="Edit01Icon"
              @click="handleEdit(group)"
            >            
            </IluriaButton>
            <IluriaButton
              variant="ghost"
              color="danger"
              size="small"
              :hugeIcon="Delete01Icon"
              @click="showDeleteConfirmation(group)"
              aria-label="Excluir"
            />
          </div>
        </div>
      </div>

      <!-- Loading State -->
      <div v-else-if="loading" class="loading-container">
        <div class="loading-spinner"></div>
      </div>

      <!-- No results found -->
      <div
        v-else-if="!loading && groups.length === 0 && allGroups.length > 0"
        class="empty-state-container"
      >
        <div class="empty-state-content">
          <div class="empty-state-icon">
            <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="1.5" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
            </svg>
          </div>
          <h3 class="empty-state-title">{{ $t('community.management.noGroups') }}</h3>
          <p class="empty-state-description">{{ $t('community.management.noGroupsDescription') }}</p>
        </div>
      </div>

      <!-- Empty State - No groups at all -->
      <div
        v-else-if="!loading && allGroups.length === 0 && initialDataLoaded"
        class="empty-state-container"
      >
        <div class="empty-state-content">
          <div class="empty-state-icon">
            <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="1.5" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />
            </svg>
          </div>
          <h3 class="empty-state-title">{{ $t('community.management.noGroupsRegistered') }}</h3>
          <p class="empty-state-description">{{ $t('community.management.noGroupsRegisteredDescription') }}</p>
          <IluriaButton
            color="primary"
            @click="openGroups"
            class="empty-state-button"
          >
            <span class="button-content">
              <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="none" viewBox="0 0 24 24" stroke="currentColor" class="plus-icon">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4" />
              </svg>
              {{ $t('community.management.createGroupButton') }}
            </span>
          </IluriaButton>
        </div>
      </div>
    </div>

    <!-- Category Management View -->
    <CategoryManagement
      v-if="currentView === 'categories'"
      key="categoriesView"
      @close="currentView = 'main'"
      @success="handleCategorySuccess"
    />

    <!-- Group Management View -->
    <GroupManagement
      v-else-if="currentView === 'groups'"
      ref="groupManagementRef"
      key="groupsView"
      :group-to-edit="selectedGroup"
      @close="currentView = 'main'"
      @saved="handleGroupSaved"
    />

    <!-- Delete Confirmation Modal -->
    <IluriaConfirmationModal
      :isVisible="showDeleteModal"
      :title="$t('community.management.deleteModal.title')"
      :message="$t('community.management.deleteModal.message')"
      @confirm="confirmDelete"
      @cancel="cancelDelete"
    />
  </div>
</template>

<script setup>
import { ref, onMounted, watch, nextTick, computed } from 'vue';
import { useI18n } from 'vue-i18n';
import { useRouter } from 'vue-router';
import { useToastStore } from '@/stores/toast.store';
import { useTheme } from '@/composables/useTheme';
import IluriaButton from '@/components/iluria/IluriaButton.vue';
import IluriaHeader from '@/components/iluria/IluriaHeader.vue';
import { Edit01Icon, Delete01Icon } from '@hugeicons-pro/core-bulk-rounded';
import CategoryManagement from './CategoryManagement.vue';
import GroupManagement from './GroupManagement.vue';
import IluriaConfirmationModal from '@/components/iluria/modal/IluriaConfirmationModal.vue';
import communityService from '@/services/community.service';

const { t } = useI18n();
const toast = useToastStore();
const { initTheme } = useTheme();
const currentView = ref('main');
const selectedCategory = ref(null);
const searchQuery = ref('');
const sortBy = ref('trending');
const showDeleteModal = ref(false);
const groupToDelete = ref(null);
const selectedGroup = ref(null);
const loading = ref(false);
const error = ref(null);
const initialDataLoaded = ref(false);

const categories = ref([]);
const groups = ref([]);
const allGroups = ref([]);
const groupManagementRef = ref(null);

// Computed properties for dynamic title and subtitle
const pageTitle = computed(() => {
  switch (currentView.value) {
    case 'categories':
      return t('community.management.categoriesTitle');
    case 'groups':
      return t('community.management.groupsTitle');
    default:
      return t('community.management.title');
  }
});

const pageSubtitle = computed(() => {
  switch (currentView.value) {
    case 'categories':
      return t('community.management.categoriesSubtitle');
    case 'groups':
      return selectedGroup.value ? t('community.management.groupsSubtitleEdit') : t('community.management.groupsSubtitleCreate');
    default:
      return t('community.management.subtitle');
  }
});

// Header button functions
const getHeaderButtons = () => {
  const buttons = []

  if (currentView.value === 'categories') {
    // Botão Voltar para Comunidade
    buttons.push({
      text: '← Voltar para Comunidade',
      color: 'secondary',
      variant: 'outline',
      onClick: () => currentView.value = 'main'
    })
  } else if (currentView.value === 'main') {
    // Botão Gerenciar Categorias (apenas na view principal)
    buttons.push({
      text: t('community.management.manageCategories'),
      color: 'secondary',
      variant: 'outline',
      onClick: openCategories
    })

    // Botão Criar Grupo (apenas na view principal)
    buttons.push({
      text: t('community.management.createGroup'),
      color: 'primary',
      onClick: openGroups
    })
  } else if (currentView.value === 'groups') {
    // Botões para a view de grupos
    buttons.push({
      text: t('common.cancel'),
      color: 'secondary',
      variant: 'outline',
      onClick: () => currentView.value = 'main'
    })

    buttons.push({
      text: t('common.save'),
      color: 'primary',
      onClick: handleSaveGroup
    })
  }

  return buttons
}

const handleCustomButtonClick = (index, button) => {
  if (button.onClick) {
    button.onClick()
  }
}

// Computed para verificar se há grupos (independente de filtros)
const hasAnyGroups = computed(() => {
  return allGroups.value.length > 0;
});

// Handle save group from header button
const handleSaveGroup = () => {
  // Trigger save on the GroupManagement component
  // We'll use a ref to call the submit method
  if (groupManagementRef.value) {
    groupManagementRef.value.handleSubmit();
  }
};

// Load groups with optional filters
const loadGroups = async () => {
  try {
    loading.value = true;
    error.value = null;
    
    const params = {
      categoryId: selectedCategory.value?.id,
      search: searchQuery.value,
      page: 0,
      size: 20
    };

    const response = await communityService.listGroups(params);
    const groupsData = Array.isArray(response) ? response : (response?.content || []);
    groups.value = groupsData.map(group => ({
      ...group,
      memberCount: (group.memberCount ?? 0).toLocaleString(),
      postCount: (group.postCount ?? 0).toLocaleString()
    }));
    
    // Store initial data for empty state detection and filter visibility
    if (!params.categoryId && !params.search) {
      allGroups.value = [...groups.value];
      if (!initialDataLoaded.value) {
        initialDataLoaded.value = true;
      }
    }
  } catch (err) {
    console.error('Error loading groups:', err);
    error.value = t('community.management.errors.loadGroups');
    toast.addToast({
      type: 'error',
      message: t('community.management.errors.loadGroups')
    });
  } finally {
    loading.value = false;
  }
};

// Load categories
const loadCategories = async () => {
  try {
    loading.value = true;
    error.value = null;
    const response = await communityService.listCategories();
    categories.value = response;
  } catch (err) {
    console.error('Error loading categories:', err);
    error.value = t('community.management.errors.loadCategories');
    toast.addToast({
      type: 'error',
      message: t('community.management.errors.loadCategories')
    });
  } finally {
    loading.value = false;
  }
};

// Watch for changes in filters
watch([selectedCategory, searchQuery, sortBy], () => {
  if (initialDataLoaded.value) {
    loadGroups();
  }
});

// Handle group deletion
const showDeleteConfirmation = (group) => {
  if (!group?.id) {
    toast.addToast({
      type: 'error',
      message: t('community.management.errors.invalidGroup')
    });
    return;
  }
  groupToDelete.value = group;
  showDeleteModal.value = true;
};

const confirmDelete = async () => {
  if (!groupToDelete.value?.id) {
    toast.addToast({
      type: 'error',
      message: t('community.management.errors.groupIdNotFound')
    });
    return;
  }

  try {
    loading.value = true;
    await communityService.deleteGroup(groupToDelete.value.id);
    toast.addToast({
      type: 'success',
      message: t('community.management.success.groupDeleted')
    });

    // Recarregar categorias e grupos para atualizar filtros
    await loadCategories();
    await loadGroups();
  } catch (err) {
    console.error('Error deleting group:', err);
    toast.addToast({
      type: 'error',
      message: t('community.management.errors.deleteGroup')
    });
  } finally {
    loading.value = false;
    showDeleteModal.value = false;
    groupToDelete.value = null;
  }
};

const cancelDelete = () => {
  showDeleteModal.value = false;
  groupToDelete.value = null;
};

// Handle group editing
const handleEdit = (group) => {
  selectedGroup.value = group;
  currentView.value = 'groups';
};

const openCategories = () => {
  currentView.value = 'categories';
};

const openGroups = () => {
  selectedGroup.value = null;
  currentView.value = 'groups';
};

// Handle after group saved
const handleGroupSaved = async () => {
  // Voltar para a view principal
  currentView.value = 'main';
  selectedGroup.value = null;

  // Recarregar dados para atualizar filtros
  await loadCategories();
  await loadGroups();
};

// Toggle category selection
const toggleCategory = (cat) => {
  if (selectedCategory.value?.id === cat.id) {
    selectedCategory.value = null;
  } else {
    selectedCategory.value = cat;
  }
};



// Handle successful category operations
const handleCategorySuccess = async () => {
  try {
    // Aguarda um tick para garantir que estamos na view principal
    await nextTick();
    // Recarrega a lista de categorias após criação/edição bem-sucedida
    await loadCategories();
    // Força a reatividade limpando e recarregando grupos se necessário
    if (selectedCategory.value) {
      await loadGroups();
    }
  } catch (err) {
    console.error('Error handling category success:', err);
    toast.addToast({
      type: 'error',
      message: t('community.management.errors.updateCategories')
    });
  }
};

// Load initial data
onMounted(async () => {
  initTheme();
  await loadCategories();
  await loadGroups();
});
</script>

<style scoped>
/* Page Container */
.page-container {
  padding: 24px;
}



.btn {
  padding: 8px 16px;
  border-radius: 8px;
  font-weight: 500;
  transition: background-color 0.2s;
}

/* Removed hardcoded button styles - using IluriaButton theme variables instead */

.main-content {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.search-filter {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.search-container {
  width: 50%;
}

.search-input-wrapper {
  position: relative;
  width: 100%;
  display: flex;
  align-items: center;
}

.search-icon {
  position: absolute;
  left: 12px;
  width: 18px;
  height: 18px;
  color: var(--iluria-color-text-secondary);
  z-index: 1;
  pointer-events: none;
  stroke-width: 1.5;
}

.search-input {
  width: 100%;
  padding: 8px 16px 8px 40px;
  border: 1px solid var(--iluria-color-border);
  border-radius: 8px;
  background-color: var(--iluria-color-input-bg);
  color: var(--iluria-color-input-text);
}

.sort-select {
  width: 100%;
  padding: 8px 16px;
  border: 1px solid var(--iluria-color-border);
  border-radius: 8px;
  background-color: var(--iluria-color-input-bg);
  color: var(--iluria-color-input-text);
}

.search-input::placeholder {
  color: var(--iluria-color-input-placeholder);
}

.search-input:focus,
.sort-select:focus {
  outline: none;
  border-color: var(--iluria-color-input-border-focus);
  box-shadow: 0 0 0 2px rgba(37, 99, 235, 0.2);
}

.groups-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(220px, 1fr));
  gap: 20px;
}

.group-card {
  background-color: var(--iluria-color-container-bg);
  border-radius: 12px;
  padding: 20px 16px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
  gap: 12px;
  transition: transform 0.2s, box-shadow 0.2s;
}

.group-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
}

.group-image-container {
  width: 100px;
  height: 100px;
  margin-bottom: 6px;
}

.group-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
  border-radius: 50%;
  cursor: pointer;
  transition: opacity 0.2s;
}

.group-image:hover {
  opacity: 0.8;
}

.group-image-placeholder {
  width: 100%;
  height: 100%;
  border-radius: 50%;
  background-color: var(--iluria-color-border);
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: opacity 0.2s;
}

.group-image-placeholder span {
  font-size: 40px;
  font-weight: 600;
  color: var(--iluria-color-text-secondary);
}

.group-image-placeholder:hover {
  opacity: 0.8;
}

.group-info {
  flex: 1;
  width: 100%;
}

.group-title {
  font-size: 18px;
  font-weight: 700;
  margin-bottom: 6px;
  color: var(--iluria-color-text-primary);
}

.group-description {
  color: var(--iluria-color-text-secondary);
  font-size: 13px;
  line-height: 1.3;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;
  margin-bottom: 12px;
  height: 2.6em; /* Garante altura fixa para 2 linhas */
}

.group-stats {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 16px;
  margin-bottom: 6px;
}

.stat-item {
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 12px;
  color: var(--iluria-color-text-secondary);
  font-weight: 500;
}

.stat-item span {
  color: var(--iluria-color-text-secondary);
}

.stat-icon {
  width: 14px;
  height: 14px;
  color: var(--iluria-color-text-secondary);
}

.action-buttons {
  display: flex;
  gap: 8px;
  width: 100%;
  justify-content: center;
  margin-top: 4px;
}

.loading-container {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 48px 0;
}

.loading-spinner {
  width: 48px;
  height: 48px;
  border: 2px solid #e5e7eb;
  border-bottom-color: #1f2937;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}



.empty-state-container {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 400px;
  padding: 48px 0;
}

.empty-state-content {
  text-align: center;
  max-width: 400px;
}

.empty-state-icon {
  width: 80px;
  height: 80px;
  margin: 0 auto 24px;
  background-color: var(--iluria-color-container-bg);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  border: 2px solid var(--iluria-color-border);
}

.empty-state-icon svg {
  width: 40px;
  height: 40px;
  color: var(--iluria-color-text-secondary);
}

.empty-state-title {
  font-size: 24px;
  font-weight: 700;
  color: var(--iluria-color-text-primary);
  margin-bottom: 12px;
}

.empty-state-description {
  font-size: 16px;
  color: var(--iluria-color-text-secondary);
  margin-bottom: 32px;
  line-height: 1.5;
}

.empty-state-button {
  display: inline-flex;
  align-items: center;
}

.button-content {
  display: flex;
  align-items: center;
  gap: 6px;
}

.plus-icon {
  flex-shrink: 0;
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

@media (max-width: 768px) {
  .page-container {
    padding: 16px;
  }

  .page-header {
    flex-direction: column;
    align-items: stretch;
    gap: 16px;
    margin-bottom: 24px;
    padding-bottom: 16px;
  }

  .page-title {
    font-size: 24px;
  }

  .page-subtitle {
    font-size: 15px;
    margin: 6px 0 0 0;
  }

  .header-actions {
    flex-direction: column;
    align-items: stretch;
    gap: 12px;
  }

  .search-filter {
    flex-direction: column;
    gap: 16px;
  }

  .search-container {
    width: 100%;
  }
}

.categories-filter button {
  border: 1px solid var(--iluria-color-border);
}
.categories-filter button:hover {
  background-color: var(--iluria-color-border-hover);
}
</style> 