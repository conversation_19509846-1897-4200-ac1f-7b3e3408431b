import axios from 'axios'
import { API_URLS } from '../config/api.config'

export const themeCategoryService = {
  /**
   * Lista todas as categorias de tema
   * @param {boolean} activeOnly - Se deve retornar apenas categorias ativas
   * @returns {Promise<Array>} Lista de categorias
   */
  async getCategories(activeOnly = true) {
    try {
      const response = await axios.get(API_URLS.THEME_CATEGORY_BASE_URL, {
        params: { activeOnly }
      })
      return response.data
    } catch (error) {
      console.error('Erro ao buscar categorias de tema:', error)
      throw error
    }
  },

  /**
   * Busca uma categoria por ID
   * @param {string} id - ID da categoria
   * @returns {Promise<Object>} Categoria encontrada
   */
  async getCategoryById(id) {
    try {
      const response = await axios.get(`${API_URLS.THEME_CATEGORY_BASE_URL}/${id}`)
      return response.data
    } catch (error) {
      console.error('Erro ao buscar categoria por ID:', error)
      throw error
    }
  },

  /**
   * Busca uma categoria por slug
   * @param {string} slug - Slug da categoria
   * @returns {Promise<Object>} Categoria encontrada
   */
  async getCategoryBySlug(slug) {
    try {
      const response = await axios.get(`${API_URLS.THEME_CATEGORY_BASE_URL}/slug/${slug}`)
      return response.data
    } catch (error) {
      console.error('Erro ao buscar categoria por slug:', error)
      throw error
    }
  },

  /**
   * Cria uma nova categoria
   * @param {Object} categoryData - Dados da categoria
   * @returns {Promise<Object>} Categoria criada
   */
  async createCategory(categoryData) {
    try {
      const response = await axios.post(API_URLS.THEME_CATEGORY_BASE_URL, categoryData)
      return response.data
    } catch (error) {
      console.error('Erro ao criar categoria:', error)
      throw error
    }
  },

  /**
   * Atualiza uma categoria existente
   * @param {string} id - ID da categoria
   * @param {Object} categoryData - Dados atualizados da categoria
   * @returns {Promise<Object>} Categoria atualizada
   */
  async updateCategory(id, categoryData) {
    try {
      const response = await axios.put(`${API_URLS.THEME_CATEGORY_BASE_URL}/${id}`, categoryData)
      return response.data
    } catch (error) {
      console.error('Erro ao atualizar categoria:', error)
      throw error
    }
  },

  /**
   * Ativa uma categoria
   * @param {string} id - ID da categoria
   * @returns {Promise<Object>} Categoria ativada
   */
  async activateCategory(id) {
    try {
      const response = await axios.patch(`${API_URLS.THEME_CATEGORY_BASE_URL}/${id}/activate`)
      return response.data
    } catch (error) {
      console.error('Erro ao ativar categoria:', error)
      throw error
    }
  },

  /**
   * Desativa uma categoria
   * @param {string} id - ID da categoria
   * @returns {Promise<Object>} Categoria desativada
   */
  async deactivateCategory(id) {
    try {
      const response = await axios.patch(`${API_URLS.THEME_CATEGORY_BASE_URL}/${id}/deactivate`)
      return response.data
    } catch (error) {
      console.error('Erro ao desativar categoria:', error)
      throw error
    }
  },

  /**
   * Exclui uma categoria
   * @param {string} id - ID da categoria
   * @returns {Promise<void>}
   */
  async deleteCategory(id) {
    try {
      await axios.delete(`${API_URLS.THEME_CATEGORY_BASE_URL}/${id}`)
    } catch (error) {
      console.error('Erro ao excluir categoria:', error)
      throw error
    }
  },

  /**
   * Busca categorias por nome
   * @param {string} name - Nome para buscar
   * @returns {Promise<Array>} Lista de categorias encontradas
   */
  async searchCategories(name) {
    try {
      const response = await axios.get(`${API_URLS.THEME_CATEGORY_BASE_URL}/search`, {
        params: { name }
      })
      return response.data
    } catch (error) {
      console.error('Erro ao buscar categorias:', error)
      throw error
    }
  },

  /**
   * Inicializa as categorias padrão do sistema
   * @returns {Promise<void>}
   */
  async initializeDefaultCategories() {
    try {
      await axios.post(`${API_URLS.THEME_CATEGORY_BASE_URL}/initialize-defaults`)
    } catch (error) {
      console.error('Erro ao inicializar categorias padrão:', error)
      throw error
    }
  },

  /**
   * Mapeia categorias para opções de select
   * @param {Array} categories - Lista de categorias
   * @returns {Array} Opções formatadas para select
   */
  mapCategoriesToSelectOptions(categories) {
    return categories.map(category => ({
      value: category.id,
      label: category.name,
      description: category.description,
      color: category.color,
      icon: category.icon
    }))
  },

  /**
   * Agrupa categorias por tipo/cor
   * @param {Array} categories - Lista de categorias
   * @returns {Object} Categorias agrupadas
   */
  groupCategoriesByType(categories) {
    return categories.reduce((groups, category) => {
      const type = category.color || 'default'
      if (!groups[type]) {
        groups[type] = []
      }
      groups[type].push(category)
      return groups
    }, {})
  },

  /**
   * Filtra categorias por critérios
   * @param {Array} categories - Lista de categorias
   * @param {Object} filters - Filtros a aplicar
   * @returns {Array} Categorias filtradas
   */
  filterCategories(categories, filters = {}) {
    return categories.filter(category => {
      if (filters.active !== undefined && category.isActive !== filters.active) {
        return false
      }
      if (filters.name && !category.name.toLowerCase().includes(filters.name.toLowerCase())) {
        return false
      }
      if (filters.slug && category.slug !== filters.slug) {
        return false
      }
      return true
    })
  }
}
