{"bellAriaLabel": "Abrir notificações", "title": "Notificações", "markAllAsRead": "Marcar todas como lidas", "markAllRead": "Marcar todas como lidas", "settings": "Configurações de notificação", "loading": "Carregando notificações...", "loadMore": "<PERSON><PERSON><PERSON> mais", "allMarkedAsRead": "Todas as notificações marcadas como lidas", "markAsRead": "Marcar como lida", "delete": "Excluir notificação", "markedAsRead": "Notificação marcada como lida", "deleted": "Notificação excluída", "empty": {"title": "Nenhuma notificação", "message": "Você está em dia! Novas notificações aparecerão aqui."}, "error": {"loadFailed": "Falha ao carregar notificações", "loadMoreFailed": "Falha ao carregar mais notificações", "markReadFailed": "Falha ao marcar notificações como lidas", "deleteFailed": "Falha ao excluir notificação", "navigationFailed": "Falha ao navegar para notificação"}, "time": {"now": "agora", "minutes": "min", "hours": "h", "days": "d", "weeks": "sem", "months": "mês", "years": "ano"}, "mock": {"newSale": {"title": "Nova venda realizada", "content": "Pedido #12345 foi finalizado com sucesso."}, "productReview": {"title": "Nova avaliação", "content": "Seu produto recebeu uma avaliação de 5 estrelas."}, "securityAlert": {"title": "Alerta de segurança", "content": "Nova tentativa de login detectada."}, "featureAnnouncement": {"title": "Nova funcionalidade", "content": "Descubra as novidades da plataforma."}}, "byEmail": "Por email", "comments": "Comentários", "commentsDescription": "Receba notificações quando alguém comentar em uma postagem.", "candidates": "<PERSON><PERSON><PERSON><PERSON>", "candidatesDescription": "Receba notificações quando um candidato se inscrever em uma vaga.", "offers": "Ofertas de emprego", "offersDescription": "Receba notificações quando um candidato aceitar ou rejeitar uma oferta.", "pushNotifications": "Notificaçõ<PERSON>", "pushNotificationsDescription": "<PERSON><PERSON>s s<PERSON> entregues via SMS para o seu celular.", "pushEverything": "<PERSON><PERSON>", "pushSameAsEmail": "Igual ao email", "pushNothing": "Sem notificações push", "emailNotifications": "Notificações por Email", "emailNotificationsDescription": "Receba notificações por email."}