<template>

  <!-- Sidebar -->
  <div 
    class="user-sidebar"
    :class="{ 'mobile-open': mobileMenuOpen }"
  >
    <nav class="sidebar-nav">
      <!-- Account Section -->
      <div class="nav-section">
        <button
          @click="toggleSection('account')"
          class="section-header"
        >
          <div class="section-header-content">
            <HugeiconsIcon :icon="UserSettings01Icon" size="16" :strokeWidth="1.5" />
            <span class="section-title">{{ t('userSettings.sections.account.title') }}</span>
          </div>
          <ChevronDown
            class="section-chevron"
            :class="{ 'rotated': collapsedSections.account }"
            size="16"
            :strokeWidth="1.5"
          />
        </button>
        <Transition name="collapse">
          <ul v-show="!collapsedSections.account" class="nav-items">
            <li>
              <button
                @click="setActiveSection('profile')"
                :class="['nav-item', { 'active': activeSection === 'profile' }]"
              >
                <HugeiconsIcon :icon="UserAccountIcon" size="18" :strokeWidth="1.5" />
                <span>{{ t('userSettings.sections.account.myProfile') }}</span>
                <ChevronRight class="item-arrow" size="16" :strokeWidth="1.5" />
              </button>
            </li>
            <li>
              <button
                @click="setActiveSection('account')"
                :class="['nav-item', { 'active': activeSection === 'account' }]"
              >
                <HugeiconsIcon :icon="UserSettingsIcon" size="18" :strokeWidth="1.5" />
                <span>{{ t('userSettings.sections.account.settings') }}</span>
                <ChevronRight class="item-arrow" size="16" :strokeWidth="1.5" />
              </button>
            </li>
            <li>
              <button
                @click="setActiveSection('preferences')"
                :class="['nav-item', { 'active': activeSection === 'preferences' }]"
              >
                <HugeiconsIcon :icon="Settings02Icon" size="18" :strokeWidth="1.5" />
                <span>{{ t('userSettings.sections.account.preferences') }}</span>
                <ChevronRight class="item-arrow" size="16" :strokeWidth="1.5" />
              </button>
            </li>
            <li>
              <button
                @click="setActiveSection('contacts')"
                :class="['nav-item', { 'active': activeSection === 'contacts' }]"
              >
                <HugeiconsIcon :icon="MailAtSign01Icon" size="18" :strokeWidth="1.5" />
                <span>{{ t('userSettings.sections.account.contacts') }}</span>
                <ChevronRight class="item-arrow" size="16" :strokeWidth="1.5" />
              </button>
            </li>
          </ul>
        </Transition>
      </div>

      <!-- Lojas Section -->
      <div class="nav-section">
        <button
          @click="toggleSection('collaboration')"
          class="section-header"
        >
          <div class="section-header-content">
            <HugeiconsIcon :icon="Store01Icon" size="16" :strokeWidth="1.5" />
            <span class="section-title">Lojas</span>
          </div>
          <ChevronDown
            class="section-chevron"
            :class="{ 'rotated': collapsedSections.collaboration }"
            size="16"
            :strokeWidth="1.5"
          />
        </button>
        <Transition name="collapse">
          <ul v-show="!collapsedSections.collaboration" class="nav-items">
            <li>
              <button
                @click="setActiveSection('myStores')"
                :class="['nav-item', { 'active': activeSection === 'myStores' }]"
              >
                <HugeiconsIcon :icon="Store01Icon" size="18" :strokeWidth="1.5" />
                <span>Minhas Lojas</span>
                <ChevronRight class="item-arrow" size="16" :strokeWidth="1.5" />
              </button>
            </li>
            <li>
              <button
                @click="setActiveSection('invites')"
                :class="['nav-item', { 'active': activeSection === 'invites' }]"
              >
                <HugeiconsIcon :icon="Mail01Icon" size="18" :strokeWidth="1.5" />
                <span>Convites</span>
                <ChevronRight class="item-arrow" size="16" :strokeWidth="1.5" />
              </button>
            </li>
          </ul>
        </Transition>
      </div>

      <!-- Security Section -->
      <div class="nav-section">
        <button 
          @click="toggleSection('security')"
          class="section-header"
        >
          <div class="section-header-content">
            <HugeiconsIcon :icon="SecurityCheckIcon" size="16" :strokeWidth="1.5" />
            <span class="section-title">{{ t('userSettings.sections.security.title') }}</span>
          </div>
          <ChevronDown 
            class="section-chevron" 
            :class="{ 'rotated': collapsedSections.security }"
            size="16" 
            :strokeWidth="1.5" 
          />
        </button>
        <Transition name="collapse">
          <ul v-show="!collapsedSections.security" class="nav-items">
            <li>
              <button
                @click="setActiveSection('sessions')"
                :class="['nav-item', { 'active': activeSection === 'sessions' }]"
              >
                <HugeiconsIcon :icon="ComputerIcon" size="18" :strokeWidth="1.5" />
                <span>{{ t('userSettings.sections.security.sessionsAndLogs') }}</span>
                <ChevronRight class="item-arrow" size="16" :strokeWidth="1.5" />
              </button>
            </li>
            <li>
              <button
                @click="setActiveSection('twoFactor')"
                :class="['nav-item', { 'active': activeSection === 'twoFactor' }]"
              >
                <HugeiconsIcon :icon="ShieldKeyIcon" size="18" :strokeWidth="1.5" />
                <span>{{ t('userSettings.sections.security.twoFactorAuth') }}</span>
                <ChevronRight class="item-arrow" size="16" :strokeWidth="1.5" />
              </button>
            </li>
            <li>
              <button
                @click="setActiveSection('password')"
                :class="['nav-item', { 'active': activeSection === 'password' }]"
              >
                <HugeiconsIcon :icon="SquareUnlock02Icon" size="18" :strokeWidth="1.5" />
                <span>{{ t('userSettings.sections.security.changePassword') }}</span>
                <ChevronRight class="item-arrow" size="16" :strokeWidth="1.5" />
              </button>
            </li>
          </ul>
        </Transition>
      </div>


      <!-- Billing Section -->
      <div class="nav-section">
        <button
          @click="toggleSection('billing')"
          class="section-header"
        >
          <div class="section-header-content">
            <HugeiconsIcon :icon="CreditCardIcon" size="16" :strokeWidth="1.5" />
            <span class="section-title">{{ t('userSettings.sections.billing.title') }}</span>
          </div>
          <ChevronDown
            class="section-chevron"
            :class="{ 'rotated': collapsedSections.billing }"
            size="16"
            :strokeWidth="1.5"
          />
        </button>
        <Transition name="collapse">
          <ul v-show="!collapsedSections.billing" class="nav-items">
            <li>
              <button
                @click="setActiveSection('paymentMethods')"
                :class="['nav-item', { 'active': activeSection === 'paymentMethods' }]"
              >
                <HugeiconsIcon :icon="CreditCardIcon" size="18" :strokeWidth="1.5" />
                <span>{{ t('userSettings.sections.billing.paymentMethods') }}</span>
                <ChevronRight class="item-arrow" size="16" :strokeWidth="1.5" />
              </button>
            </li>
            <li>
              <button
                @click="setActiveSection('subscriptions')"
                :class="['nav-item', { 'active': activeSection === 'subscriptions' }]"
              >
                <HugeiconsIcon :icon="MoneyExchange02Icon" size="18" :strokeWidth="1.5" />
                <span>{{ t('userSettings.sections.billing.subscriptions') }}</span>
                <ChevronRight class="item-arrow" size="16" :strokeWidth="1.5" />
              </button>
            </li>
          </ul>
        </Transition>
      </div>

      <!-- Notifications Section -->
      <div class="nav-section">
        <button
          @click="toggleSection('notifications')"
          class="section-header"
        >
          <div class="section-header-content">
            <HugeiconsIcon :icon="Notification03Icon" size="16" :strokeWidth="1.5" />
            <span class="section-title">{{ t('userSettings.sections.notifications.title') }}</span>
          </div>
          <ChevronDown
            class="section-chevron"
            :class="{ 'rotated': collapsedSections.notifications }"
            size="16"
            :strokeWidth="1.5"
          />
        </button>
        <Transition name="collapse">
          <ul v-show="!collapsedSections.notifications" class="nav-items">
            <li>
              <button
                @click="setActiveSection('notifications')"
                :class="['nav-item', { 'active': activeSection === 'notifications' }]"
              >
                <HugeiconsIcon :icon="Mail01Icon" size="18" :strokeWidth="1.5" />
                <span>{{ t('userSettings.sections.notifications.email') }}</span>
                <ChevronRight class="item-arrow" size="16" :strokeWidth="1.5" />
              </button>
            </li>
          </ul>
        </Transition>
      </div>
    </nav>
  </div>
</template>

<script setup>
import { ref } from 'vue'
import { ChevronRight, ChevronDown } from 'lucide-vue-next'
import { HugeiconsIcon } from '@hugeicons/vue'
import {
  UserIcon,
  UserAccountIcon,
  SecurityCheckIcon,
  ShieldKeyIcon,
  SquareUnlock02Icon,
  Notification03Icon,
  Mail01Icon,
  MailAtSign01Icon,
  Settings02Icon,
  UserSettingsIcon,
  UserSettings01Icon,
  ComputerIcon,
  CreditCardIcon,
  MoneyExchange02Icon,
  UserGroup02Icon,
  Store01Icon
} from '@hugeicons-pro/core-stroke-standard'
import { useI18n } from 'vue-i18n'
import { useMobileUserMenu } from '@/composables/useMobileUserMenu'
import { onMounted, onUnmounted } from 'vue'

// Composables
const { t } = useI18n()
const { mobileMenuOpen, closeMobileMenu } = useMobileUserMenu()

// Props
const props = defineProps({
  activeSection: {
    type: String,
    default: 'profile'
  }
})

// Emits
const emit = defineEmits(['section-change'])

// State for collapsed sections
const collapsedSections = ref({
  profile: false,
  security: false,
  billing: false,
  notifications: false,
  account: false,
  collaboration: false
})

// Methods
const setActiveSection = (section) => {
  emit('section-change', section)
  // Fechar menu mobile após seleção no mobile
  closeMobileMenu()
}

const toggleSection = (section) => {
  collapsedSections.value[section] = !collapsedSections.value[section]
}

// Fechar menu ao clicar fora (apenas no mobile)
const handleClickOutside = (event) => {
  // Só processar se o menu estiver aberto
  if (!mobileMenuOpen.value || window.innerWidth >= 768) {
    return
  }
  
  const sidebar = event.target.closest('.user-sidebar')
  const menuButton = event.target.closest('.mobile-menu-button')
  const userNavbar = event.target.closest('.user-navbar')
  
  // Se clicou fora da sidebar e não foi no botão do menu
  if (!sidebar && !menuButton && !userNavbar) {
    closeMobileMenu()
  }
}

// Lifecycle hooks
onMounted(() => {
  // Usar mousedown para evitar conflitos com click
  document.addEventListener('mousedown', handleClickOutside)
})

onUnmounted(() => {
  document.removeEventListener('mousedown', handleClickOutside)
})
</script>

<style scoped>
.user-sidebar {
  width: 280px;
  min-width: 280px;
  height: 100vh;
  background: var(--iluria-color-container-bg);
  border-right: 1px solid var(--iluria-color-border);
  display: flex;
  flex-direction: column;
  overflow-y: auto;
  position: sticky;
  top: 0;
}


.sidebar-nav {
  flex: 1;
  padding: 20px 16px;
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.nav-section {
  display: flex;
  flex-direction: column;
}

.section-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
  padding: 12px 16px;
  background: transparent;
  border: none;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.2s ease;
  margin-bottom: 4px;
}

.section-header:hover {
  background: var(--iluria-color-hover);
}

.section-header-content {
  display: flex;
  align-items: center;
  gap: 12px;
}

.section-title {
  font-size: 14px;
  font-weight: 600;
  color: var(--iluria-color-text-primary);
  margin: 0;
  flex: 1;
}

.section-chevron {
  color: var(--iluria-color-text-muted);
  transition: transform 0.2s ease;
}

.section-chevron.rotated {
  transform: rotate(-90deg);
}

.nav-items {
  list-style: none;
  margin: 0;
  padding: 0 0 0 16px;
  display: flex;
  flex-direction: column;
  gap: 2px;
  overflow: hidden;
}

.nav-item {
  display: flex;
  align-items: center;
  gap: 12px;
  width: 100%;
  padding: 10px 16px;
  border: none;
  border-radius: 6px;
  background: transparent;
  color: var(--iluria-color-text-secondary);
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  text-align: left;
  font-size: 13px;
  font-weight: 500;
  outline: none;
  position: relative;
}

.nav-item:hover {
  background: var(--iluria-color-hover);
  color: var(--iluria-color-text-primary);
}

.nav-item:not(.active):hover::before {
  content: '';
  position: absolute;
  left: 0;
  top: 0;
  bottom: 0;
  width: 1px;
  background: var(--iluria-color-border-hover);
  transition: width 0.2s ease;
}

.nav-item.active {
  background: var(--iluria-color-primary-light);
  color: var(--iluria-color-primary);
  font-weight: 600;
  position: relative;
}

.nav-item.active::before {
  content: '';
  position: absolute;
  left: 0;
  top: 0;
  bottom: 0;
  width: 2px;
  background: var(--iluria-color-primary);
  transition: width 0.2s ease;
}

.nav-item.active:hover {
  background: var(--iluria-color-primary-light);
}

.nav-item.active:hover::before {
  width: 3px;
}

.nav-item span {
  flex: 1;
}

.item-arrow {
  color: var(--iluria-color-text-muted);
  transition: all 0.2s ease;
  opacity: 1;
  margin-left: auto;
}

.nav-item:hover .item-arrow {
  color: var(--iluria-color-text-primary);
  transform: translateX(2px);
}

.nav-item.active .item-arrow {
  color: var(--iluria-color-primary);
}

/* Collapse animations */
.collapse-enter-active,
.collapse-leave-active {
  transition: all 0.3s ease;
  max-height: 200px;
  opacity: 1;
}

.collapse-enter-from,
.collapse-leave-to {
  max-height: 0;
  opacity: 0;
  padding-top: 0;
  padding-bottom: 0;
}

/* Responsive */
@media (max-width: 1024px) {
  .user-sidebar {
    width: 240px;
    min-width: 240px;
  }
  
  .sidebar-header {
    padding: 20px 16px;
  }
  
  .sidebar-nav {
    padding: 12px 8px;
    gap: 20px;
  }
}

@media (max-width: 768px) {
  .user-sidebar {
    position: fixed;
    top: 80px; /* Altura da UserNavBar */
    left: 0;
    z-index: 50;
    width: 320px;
    max-width: 85vw;
    height: calc(100vh - 80px); /* Subtrair altura da navbar */
    transform: translateX(-100%);
    transition: transform 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    box-shadow: var(--iluria-shadow-xl);
    border-right: none;
  }
  
  .user-sidebar.mobile-open {
    transform: translateX(0);
  }
  
  .sidebar-nav {
    padding: 1rem;
    height: 100%;
    overflow-y: auto;
  }
  
  /* Mobile menu adjustments */
  .nav-section {
    margin-bottom: 0.5rem;
  }
  
  .section-header {
    padding: 14px 16px;
    border-radius: 10px;
    font-weight: 600;
  }
  
  .nav-items {
    padding-left: 12px;
  }
  
  .nav-item {
    padding: 12px 16px;
    border-radius: 8px;
    font-size: 14px;
  }
  
  /* Melhorar área de toque no mobile */
  .section-header,
  .nav-item {
    min-height: 44px;
    display: flex;
    align-items: center;
  }
  
  /* Scrollbar otimizada para mobile */
  .user-sidebar::-webkit-scrollbar {
    width: 3px;
  }
  
  .user-sidebar::-webkit-scrollbar-thumb {
    background: var(--iluria-color-border-hover);
    border-radius: 4px;
  }
}

/* Scroll styling */
.user-sidebar::-webkit-scrollbar {
  width: 4px;
}

.user-sidebar::-webkit-scrollbar-track {
  background: transparent;
}

.user-sidebar::-webkit-scrollbar-thumb {
  background: var(--iluria-color-border);
  border-radius: 2px;
}

.user-sidebar::-webkit-scrollbar-thumb:hover {
  background: var(--iluria-color-border-hover);
}
</style>