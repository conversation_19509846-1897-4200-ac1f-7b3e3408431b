import axios from 'axios';
import { API_URLS } from '../config/api.config';
import deviceDetection from '@/utils/deviceDetection';

class AuthService {
    #username;
    #password;
    #rememberMe;
    #baseEndpoint = API_URLS.AUTH_BASE_URL;
    #loginEndpoint = this.#baseEndpoint + "/authenticate";
    #verifyMfaEndpoint = this.#baseEndpoint + "/verify-mfa"; 
    #resendMfaEndpoint = this.#baseEndpoint + "/resend-mfa";
    #passwordRecoveryEndpoint = this.#baseEndpoint + "/request-password-recovery";
    #requestChangePasswordEndpoint = this.#baseEndpoint + "/change-password-request";
    #changePasswordEndpoint = this.#baseEndpoint + "/change-password";
    #signupEndpoint = this.#baseEndpoint + "/signup";
    #verifySignupEndpoint = this.#baseEndpoint + "/verify-signup";
    #refreshTokenEndpoint = this.#baseEndpoint + "/refresh-token";
    #authenticateStoreEndpoint = this.#baseEndpoint + "/authenticate-store";


    constructor() {
        this.#username = '';
        this.#password = '';
        this.#rememberMe = false;
    }

    setCredentials(username, password, rememberMe = false) {
        this.#username = username;
        this.#password = password;
        this.#rememberMe = rememberMe;
    }

    async login() {
        try {
            // Generate device fingerprint for session management
            const deviceFingerprint = deviceDetection.generateDeviceFingerprint();
            const sessionInfo = deviceDetection.getSessionIdentificationInfo();

            const response = await axios.post(this.#loginEndpoint, {}, {
                headers: {
                    'Authorization': 'Basic ' + btoa(this.#username + ':' + this.#password),
                    'Content-Type': 'application/json',
                    'rememberMe': this.#rememberMe,
                    'X-Device-Fingerprint': deviceFingerprint,
                    'X-Device-Info': sessionInfo.deviceInfo,
                    'X-User-Agent': sessionInfo.userAgent,
                    'X-Screen-Resolution': sessionInfo.screenResolution,
                    'X-Timezone': sessionInfo.timezone
                }
            });
            return response.data;
        } catch (error) {
            console.error('Login error:', error);
            throw error;
        }
    }

    async verifyMfaCode(mfaCode, email) {
        try {
            // Generate device fingerprint for session management
            const deviceFingerprint = deviceDetection.generateDeviceFingerprint();
            const sessionInfo = deviceDetection.getSessionIdentificationInfo();

            const response = await axios.post(this.#verifyMfaEndpoint, {
                code: mfaCode,
                email: email
            }, {
                headers: {
                    'Content-Type': 'application/json',
                    'X-Device-Fingerprint': deviceFingerprint,
                    'X-Device-Info': sessionInfo.deviceInfo,
                    'X-User-Agent': sessionInfo.userAgent,
                    'X-Screen-Resolution': sessionInfo.screenResolution,
                    'X-Timezone': sessionInfo.timezone
                }
            });

            return response.data;
        } catch (error) {
            throw error;
        }
    }

    async resendMfaCode(email) {
        try {
            const response = await axios.post(this.#resendMfaEndpoint, null, {
                params: { email: email },
                headers: { 'Content-Type': 'application/json' }
            });

            return response.status === 200;
        } catch (error) {
            throw error;
        }
    }
    async requestPasswordRecovery(email) {
        try {
            const response = await axios.post(this.#passwordRecoveryEndpoint, { email: email }, {
                headers: {
                    'Content-Type': 'application/json'
                }
            });

            if (response.status === 200) {
                return { success: true };  
            } else {
                return { success: false };  
            }
        } catch (error) {
            console.error('Erro ao solicitar recuperação de senha:', error);
            return { success: false };  
        }
    }
    
    async RequestedchangePassword(recoveryId, email, newPassword) {
        try {
            const requestBody = {
                recoveryId,
                email,
                newPassword
            };
    
            const response = await axios.post(this.#requestChangePasswordEndpoint, requestBody, {
                headers: {
                    'Content-Type': 'application/json'
                }
            });
            if (response.status === 200) {
                return { success: true };
            } else {
                return { success: false, message: 'Erro ao alterar a senha' };
            }
        } catch (error) {
            return { success: false, message: 'Falha ao tentar alterar a senha.' };
        }
    } 
    
    async changePassword(requestBody) {
        try {
            const { email, currentPassword, newPassword } = requestBody;
    
            const response = await axios.post(this.#changePasswordEndpoint, {
                email, 
                currentPassword,
                newPassword
            }, {
                headers: {
                    'Content-Type': 'application/json'
                }
            });
    
            if (response.status === 200) {
                return { success: true };
            } else {
                return { success: false, message: 'Erro ao alterar a senha' };
            }
        } catch (error) {
            if (error.response) {
                const errors = error.response.data.errors || [];
                if (errors.length > 0) {
                    const errorCode = errors[0]?.code;
                    const errorMessage = errors[0]?.message;
                    return { success: false, code: errorCode, message: errorMessage };
                }
            }
            console.error('Erro inesperado:', error);
            return { success: false, message: 'Falha ao tentar alterar a senha.' };
        }
    }    
    async signupRequest(email) {
        try {
            const response = await axios.post(this.#signupEndpoint, { email }, {
                headers: {
                    'Content-Type': 'application/json'
                }
            });
    
            if (response.status === 200) {
                return { success: true };
            } else {
                throw new Error(response.data.message);
            }
        } catch (error) {
            if (error.response && error.response.data && error.response.data.errors && error.response.data.errors.length > 0) {
                const backendError = error.response.data.errors[0]; 
                return {
                    success: false,
                    code: backendError.code,
                    message: backendError.message  
                };
            } else {
                return { success: false, message: error.message};
            }
        }
    }
    async verifySignup(signUpCode, email, password, firstName, lastName) {
        try {
          const requestBody = {
            signUpCode,
            email,
            password,
            firstName,
            lastName
          };
      
          const response = await axios.post(this.#verifySignupEndpoint, requestBody, {
            headers: {
              'Content-Type': 'application/json'
            }
          });
      
          if (response.status === 200) {
            return { success: true };
          } else {
            return { success: false };
          }
        } catch (error) {
          if (error.response && error.response.data && error.response.data.errors && error.response.data.errors.length > 0) {
            const backendError = error.response.data.errors[0];
            return {
              success: false,
              code: backendError.code,
              message: backendError.message
            };
          } else {
            return { success: false, message: error.message};
          }
        }
      }

    async refreshToken(refreshToken) {
        try {
            if (!refreshToken || refreshToken.trim() === '') {
                throw new Error('Refresh token é obrigatório');
            }

            const response = await axios.post(this.#refreshTokenEndpoint, {
                token: refreshToken // Usa contrato correto do backend
            }, {
                headers: {
                    'Content-Type': 'application/json'
                }
            });

            if (response.status === 200 && response.data.jwt) {
                return {
                    success: true,
                    jwt: response.data.jwt,
                    refreshToken: response.data.refreshToken // Novo refresh token se fornecido
                };
            } else {
                throw new Error('Resposta inválida do servidor');
            }
        } catch (error) {
            // IMPROVED ERROR HANDLING: Standardized error classification
            if (error.response?.status === 401) {
                // Token expirado ou inválido - força logout
                throw new Error('REFRESH_TOKEN_EXPIRED');
            } else if (error.response?.status === 400) {
                // Bad request - token malformado
                throw new Error('REFRESH_TOKEN_INVALID');
            } else if (error.response?.status >= 500) {
                // Server error - tenta novamente
                throw new Error('SERVER_ERROR');
            } else if (error.response?.data?.errors?.length > 0) {
                // Backend specific error
                const backendError = error.response.data.errors[0];
                throw new Error(`BACKEND_ERROR: ${backendError.message}`);
            } else if (error.code === 'NETWORK_ERROR' || !error.response) {
                // Network issue
                throw new Error('NETWORK_ERROR');
            } else {
                // Generic error
                throw new Error('REFRESH_TOKEN_FAILED');
            }
        }
    }      

    // TOTP Methods
    async getTotpStatus() {
        try {
            const response = await axios.get(this.#baseEndpoint + "/totp/status");
            return response.data;
        } catch (error) {
            console.error('TOTP status error:', error);
            throw error;
        }
    }

    async enableTotp() {
        try {
            const response = await axios.post(this.#baseEndpoint + "/totp/enable");
            return response.data;
        } catch (error) {
            console.error('Enable TOTP error:', error);
            throw error;
        }
    }

    async verifyTotpSetup(code, secret) {
        try {
            const response = await axios.post(this.#baseEndpoint + "/totp/verify-setup", {
                code,
                secret
            });
            return { success: true };
        } catch (error) {
            console.error('Verify TOTP setup error:', error);
            throw error;
        }
    }

    async disableTotp(password) {
        try {
            const response = await axios.post(this.#baseEndpoint + "/totp/disable", {
                password
            });
            return { success: true };
        } catch (error) {
            console.error('Disable TOTP error:', error);
            throw error;
        }
    }
}

export default new AuthService();