<template>
  <div class="profile-settings-container">
    <!-- Header Section -->
    <IluriaHeader
      title="Meu Perfil"
      subtitle="Gerencie suas informações pessoais e foto do perfil"
    />

    <!-- Main Content -->
    <ViewContainer
      title="Informações do Perfil"
      subtitle="Gerencie suas informações pessoais e foto do perfil"
      :icon="UserAccountIcon"
      iconColor="blue"
    >
      <div class="profile-container">
        <!-- Left Side - Form Fields -->
        <div class="form-section">
          <div class="form-field">
            <IluriaInputText
              v-model="profileData.firstName"
              label="Nome"
              placeholder="Digite seu nome"
            />
          </div>
          
          <div class="form-field">
            <IluriaInputText
              v-model="profileData.lastName"
              label="Sobrenome"
              placeholder="Digite seu sobrenome"
            />
          </div>
        </div>

        <!-- Right Side - Profile Picture -->
        <div class="avatar-section">
          <div class="avatar-content">
            <div class="avatar-container" @click="editProfilePicture">
              <div class="avatar">
                <img
                  v-if="profileData.profilePictureUrl"
                  :src="profileData.profilePictureUrl"
                  alt="Foto do perfil"
                  class="profile-image"
                />
                <HugeiconsIcon v-else :icon="UserIcon" size="72" :strokeWidth="1.5" />
              </div>
              <div class="edit-button">
                <HugeiconsIcon :icon="UserEditIcon" size="16" :strokeWidth="1.5" />
                Edit
              </div>
            </div>
            <h4 class="avatar-title">Avatar do perfil</h4>
          </div>

          <!-- Botão de salvar abaixo do avatar -->
          <div class="avatar-save-button">
            <IluriaButton
              @click="saveProfile"
              color="primary"
              :disabled="isSaving"
              :loading="isSaving"
              :hugeIcon="FloppyDiskIcon"
            >
              {{ isSaving ? 'Salvando...' : 'Salvar Alterações' }}
            </IluriaButton>
          </div>
        </div>
      </div>
    </ViewContainer>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, onUnmounted, watch } from 'vue'
import { useI18n } from 'vue-i18n'
import { useToast } from '@/services/toast.service'
import { useAuthStore } from '@/stores/auth.store'
import userProfileService from '@/services/userProfile.service'
import ViewContainer from '@/components/layout/ViewContainer.vue'
import IluriaButton from '@/components/iluria/IluriaButton.vue'
import IluriaInputText from '@/components/iluria/form/IluriaInputText.vue'

import { HugeiconsIcon } from '@hugeicons/vue'
import {
  UserAccountIcon,
  UserIcon,
  UserEditIcon,
  FloppyDiskIcon
} from '@hugeicons-pro/core-stroke-standard'

// Composables
const { t } = useI18n()
const toast = useToast()
const authStore = useAuthStore()

// State
const isSaving = ref(false)
const selectedImageFile = ref(null)

const profileData = reactive({
  firstName: '',
  lastName: '',
  profilePictureUrl: null
})

// Backup dos dados originais para cancelar edição
const originalData = reactive({
  firstName: '',
  lastName: '',
  profilePictureUrl: null
})



// Methods
const saveProfile = async () => {
  isSaving.value = true

  try {
    // 1. Salva os dados do perfil usando o novo serviço
    await userProfileService.updateUserProfileSettings({
      firstName: profileData.firstName,
      lastName: profileData.lastName,
      profilePictureUrl: profileData.profilePictureUrl,
      active: true
    })

    // 2. Se há uma nova imagem selecionada, faz o upload
    if (selectedImageFile.value) {
      const formData = new FormData()
      formData.append('file', selectedImageFile.value)

      const serverImageUrl = await userProfileService.updateProfilePicture(formData)

      // Limpa a URL temporária se existir
      if (profileData.profilePictureUrl && profileData.profilePictureUrl.startsWith('blob:')) {
        URL.revokeObjectURL(profileData.profilePictureUrl)
      }

      // Adiciona cache-busting parameter para forçar atualização da imagem
      const urlWithCacheBusting = serverImageUrl + '?t=' + Date.now()
      profileData.profilePictureUrl = urlWithCacheBusting

      // Limpa o arquivo selecionado
      selectedImageFile.value = null
    }

    // 3. Atualiza os dados originais após salvar com sucesso
    originalData.firstName = profileData.firstName
    originalData.lastName = profileData.lastName
    originalData.profilePictureUrl = profileData.profilePictureUrl

    // 4. Atualiza o auth store com os dados salvos
    authStore.updateUserProfile({
      firstName: profileData.firstName,
      lastName: profileData.lastName,
      profilePictureUrl: profileData.profilePictureUrl
    })

    toast.addToast(t('userSettings.sections.profile.profileUpdated'), 'success')
  } catch (error) {
    console.error('Erro ao salvar perfil:', error)
    toast.addToast(t('userSettings.sections.profile.profileUpdateError'), 'error')
  } finally {
    isSaving.value = false
  }
}

const editProfilePicture = () => {
  // Cria um input file temporário
  const input = document.createElement('input')
  input.type = 'file'
  input.accept = 'image/*'
  input.onchange = handleImageUpload
  input.click()
}

// Método para limpar cache do localStorage (útil para debug)
const clearLocalStorageCache = () => {
  localStorage.removeItem('user_profile_settings_fallback')
  loadProfile()
}

const handleImageUpload = (event) => {
  const file = event.target.files[0]
  if (!file) return

  // Revoga URL anterior se existir para evitar vazamentos de memória
  if (profileData.profilePictureUrl && profileData.profilePictureUrl.startsWith('blob:')) {
    URL.revokeObjectURL(profileData.profilePictureUrl)
  }

  // Cria URL local temporária para preview imediato
  const tempImageUrl = URL.createObjectURL(file)
  profileData.profilePictureUrl = tempImageUrl

  // Armazena o arquivo para upload posterior
  selectedImageFile.value = file
}

const loadProfile = async () => {
  try {
    // Carrega os dados do perfil do usuário usando o novo serviço
    const userProfile = await userProfileService.getUserProfileSettings()

    // Atualiza os dados do formulário
    profileData.firstName = userProfile.firstName || ''
    profileData.lastName = userProfile.lastName || ''
    profileData.profilePictureUrl = userProfile.profilePictureUrl || null

    // Se há URL de imagem, adiciona cache-busting para garantir atualização
    if (userProfile.profilePictureUrl) {
      const separator = userProfile.profilePictureUrl.includes('?') ? '&' : '?'
      profileData.profilePictureUrl = userProfile.profilePictureUrl + separator + 't=' + Date.now()
    } else {
      profileData.profilePictureUrl = null
    }

    // Salva os dados originais
    originalData.firstName = profileData.firstName
    originalData.lastName = profileData.lastName
    originalData.profilePictureUrl = profileData.profilePictureUrl

    // Atualiza o auth store com os dados carregados
    authStore.updateUserProfile({
      firstName: profileData.firstName,
      lastName: profileData.lastName,
      profilePictureUrl: profileData.profilePictureUrl
    })

  } catch (error) {
    console.error('Erro ao carregar perfil:', error)

    // Se der erro, usa dados do usuário logado como fallback
    const userName = authStore.userName || ''
    const nameParts = userName.split(' ')
    profileData.firstName = nameParts[0] || ''
    profileData.lastName = nameParts.slice(1).join(' ') || ''
    profileData.profilePictureUrl = null
    originalData.firstName = profileData.firstName
    originalData.lastName = profileData.lastName
    originalData.profilePictureUrl = profileData.profilePictureUrl

    // Atualiza o auth store com os dados de fallback
    authStore.updateUserProfile({
      firstName: profileData.firstName,
      lastName: profileData.lastName,
      profilePictureUrl: profileData.profilePictureUrl
    })

    toast.addToast(t('userSettings.sections.profile.profileLoadError'), 'error')
  }
}

// Watchers para atualizar o auth store em tempo real
watch(() => profileData.firstName, (newValue) => {
  authStore.updateUserProfile({ firstName: newValue })
})

watch(() => profileData.lastName, (newValue) => {
  authStore.updateUserProfile({ lastName: newValue })
})

watch(() => profileData.profilePictureUrl, (newValue) => {
  authStore.updateUserProfile({ profilePictureUrl: newValue })
})

// Lifecycle
onMounted(() => {
  loadProfile()

  // Expõe método para debug no console
  if (typeof window !== 'undefined') {
    window.clearProfileCache = clearLocalStorageCache
  }
})

// Limpa URLs de blob quando o componente é desmontado
onUnmounted(() => {
  if (profileData.profilePictureUrl && profileData.profilePictureUrl.startsWith('blob:')) {
    URL.revokeObjectURL(profileData.profilePictureUrl)
  }
})
</script>

<style scoped>
.profile-container {
  position: relative;
  display: flex;
  align-items: center;
  gap: 6rem; /* Aumenta o gap para empurrar o avatar mais para a direita */
  width: 100%;
  min-height: 500px;
  padding-left: 19%; /* Adiciona margem à esquerda do conteúdo */
}

.form-section {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
  max-width: 600px; /* Aumenta a largura máxima para esticar os inputs */
  width: 100%;
}

.section-header {
  margin-bottom: 1.5rem;
}

.section-title {
  font-size: 18px;
  font-weight: 600;
  color: var(--iluria-color-text-primary);
  margin: 0;
}

.form-field {
  width: 100%;
}

.form-actions {
  margin-top: 2rem;
  display: flex;
  justify-content: flex-start;
  justify-content: flex-end;
  padding-top: 1.5rem;
}

.edit-actions {
  display: flex;
  gap: 0.75rem;
  align-items: center;
}

.avatar-section {
  flex-shrink: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  min-width: 200px;
}

.avatar-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 1rem;
  padding: 1rem;
  border-radius: 12px;
}

.avatar-container {
  position: relative;
  cursor: pointer;
  transition: all 0.2s ease;
  border-radius: 12px;
  padding: 0.5rem;
}

.avatar-container:hover {
  background: rgba(var(--iluria-color-primary-rgb), 0.05);
}

.avatar-container:hover .edit-button {
  background: rgba(0, 0, 0, 0.8);
  transform: scale(1.05);
}

.avatar-container:hover .avatar {
  transform: scale(1.02);
}

.avatar-title {
  font-size: 16px;
  font-weight: 600;
  color: var(--iluria-color-text-primary);
  margin: 1rem 0 0 0; /* Adiciona margem superior para separar da imagem */
  text-align: center;
}

.avatar {
  width: 210px; /* Aumenta 50% do tamanho original (140px * 1.5) */
  height: 210px;
  border-radius: 50%;
  background: var(--iluria-color-primary);
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  border: 4px solid var(--iluria-color-container-bg);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  transition: all 0.2s ease;
  overflow: hidden;
  position: relative;
}

.profile-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
  border-radius: 50%;
}

.edit-button {
  position: absolute;
  bottom: 8px;
  left: 8px;
  display: flex;
  align-items: center;
  gap: 0.375rem;
  padding: 0.375rem 0.75rem;
  background: rgba(0, 0, 0, 0.7);
  border: none;
  border-radius: 20px;
  color: white;
  font-size: 0.75rem;
  font-weight: 500;
  pointer-events: none; /* Remove a interação direta do botão */
  transition: all 0.2s ease;
  backdrop-filter: blur(4px);
}

.edit-button.loading {
  opacity: 0.7;
  cursor: not-allowed;
}

.loading-spinner {
  width: 16px;
  height: 16px;
  border: 2px solid transparent;
  border-top: 2px solid currentColor;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-right: 4px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.upload-status {
  font-size: 12px;
  color: var(--iluria-color-text-secondary);
  text-align: center;
  margin-top: 0.5rem;
  font-style: italic;
}

/* Responsive */
@media (max-width: 768px) {
  .profile-container {
    flex-direction: column-reverse;
    gap: 2rem;
    align-items: center;
    min-height: auto;
    padding-left: 0; /* Remove padding em dispositivos móveis */
  }

  .form-section {
    max-width: 100%;
    width: 100%;
  }

  .avatar-section {
    width: 100%;
    align-items: center;
    min-width: auto;
  }

  .avatar {
    width: 160px; /* Tamanho menor para mobile */
    height: 160px;
  }

  .avatar-content {
    padding: 0.5rem;
  }

  .avatar-container {
    padding: 0.25rem;
  }

  .edit-button {
    position: static;
    margin-top: 1rem;
    background: var(--iluria-color-container-bg);
    color: var(--iluria-color-text-primary);
    border: 1px solid var(--iluria-color-border);
    border-radius: 6px;
    padding: 0.5rem 1rem;
    font-size: 0.875rem;
    pointer-events: auto; /* Reativa a interação em mobile */
  }

  .avatar-container:hover .edit-button {
    background: var(--iluria-color-hover);
    border-color: var(--iluria-color-border-hover);
    transform: none;
  }

  .form-actions {
    justify-content: center;
    margin-top: 1.5rem;
  }

  .edit-actions {
    flex-direction: column;
    gap: 0.5rem;
    width: 100%;
  }

  .edit-actions .iluria-button,
  .form-actions .iluria-button {
    width: 100%;
  }

  .section-title {
    font-size: 16px;
  }
}

/* Botão de salvar na seção do avatar */
.avatar-save-button {
  margin-top: 1.5rem;
  width: 100%;
  display: flex;
  justify-content: center;
}
</style>

