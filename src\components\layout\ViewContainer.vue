<template>
    <div class="view-container">
        <div v-if="title" class="container-header" :style="backgroundColor ? { backgroundColor } : {}">
            <div class="header-content">
                <div class="flex items-center">
                    <div v-if="icon" class="icon-container">
                        <HugeiconsIcon :icon="icon" :size="24" :class="iconColorClass" />
                    </div>
                    <div>
                        <h2 class="header-title">{{ title }}</h2>
                        <p v-if="subtitle" class="header-subtitle">{{ subtitle }}</p>
                    </div>
                </div>
            </div>
            <div v-if="$slots.rightHeader" class="header-actions">
                <slot name="rightHeader"></slot>
            </div>
        </div>
        <div class="container-content" 
             :class="{ 
                 'no-header': !title,
                 'center-content': centerContent,
                 'center-horizontal': centerHorizontal,
                 'center-vertical': centerVertical
             }">
            <slot></slot>
        </div>
    </div>
</template>

<script setup>
import { computed } from 'vue';
import { HugeiconsIcon } from '@hugeicons/vue';

const props = defineProps({
  title: {
      type: String,
      default: undefined
  },
  subtitle: {
      type: String,
      default: undefined
  },
  backgroundColor: {
      type: String,
      default: undefined
  },
  icon: {
      type: [Object, Function],
      default: undefined
  },
  iconColor: {
      type: String,
      default: 'blue'
  },
  centerContent: {
      type: Boolean,
      default: false
  },
  centerHorizontal: {
      type: Boolean,
      default: false
  },
  centerVertical: {
      type: Boolean,
      default: false
  }
});

const iconColorClass = computed(() => {
  const colors = {
    'blue': 'icon-color-blue',
    'green': 'icon-color-green',
    'purple': 'icon-color-purple',
    'red': 'icon-color-red',
    'yellow': 'icon-color-yellow',
    'gray': 'icon-color-gray',
    'indigo': 'icon-color-indigo',
    'pink': 'icon-color-pink',
    'orange': 'icon-color-orange',
  };
  return colors[props.iconColor] || 'icon-color-blue';
});
</script>

<style scoped>
.view-container {
    background: var(--iluria-color-container-bg);
    border-radius: 12px;
    border: 1px solid var(--iluria-color-border);
    overflow: visible;
    box-shadow: var(--iluria-shadow-sm);
    transition: all 0.2s ease;
}

.view-container:hover {
    box-shadow: var(--iluria-shadow-md);
}

.container-header {
    padding: 20px 24px;
    border-bottom: 1px solid var(--iluria-color-border);
    background: var(--iluria-color-sidebar-bg);
    display: flex;
    justify-content: space-between;
    align-items: center;
    min-height: 72px;
    overflow: visible;
}

.header-content {
    flex: 1;
}

.header-title {
    font-size: 18px;
    font-weight: 600;
    color: var(--iluria-color-text-primary) !important;
    margin: 0;
    line-height: 1.5;
}

.header-subtitle {
    font-size: 14px;
    color: var(--iluria-color-text-secondary) !important;
    margin: 0px;
    line-height: 1.4;
}

.header-actions {
    display: flex;
    align-items: center;
    gap: 12px;
}

.container-content {
    padding: 16px;
    overflow: visible;
}

.container-content.no-header {
    padding: 20px 16px;
}

/* Responsive */
@media (max-width: 768px) {
    .container-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 16px;
        padding: 16px 20px;
    }
    
    .header-actions {
        width: 100%;
        justify-content: flex-end;
    }
    
    .container-content {
        padding: 16px;
    }
    
    .container-content.no-header {
        padding: 16px;
    }
}

/* Icon container theming */
.icon-container {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 2.5rem;
    height: 2.5rem;
    background: var(--iluria-color-container-bg, #ffffff);
    border: 1px solid var(--iluria-color-border);
    border-radius: 0.5rem;
    margin-right: 1rem;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.icon-container:hover {
    background: var(--iluria-color-sidebar-bg, #f8fafc);
    border-color: var(--iluria-color-border-hover, #d1d5db);
}

/* Icon color themes */
.icon-color-theme-primary { color: var(--iluria-color-primary); }
.icon-color-theme-secondary { color: var(--iluria-color-secondary); }

.icon-color-white { color: #ffffff; }
.icon-color-blue { color: #3b82f6; }
.icon-color-green { color: #059669; }
.icon-color-purple { color: #7c3aed; }
.icon-color-red { color: #dc2626; }
.icon-color-yellow { color: #fbff00; }
.icon-color-gray { color: #6b7280; }
.icon-color-indigo { color: #4f46e5; }
.icon-color-pink { color: #ec4899; }
.icon-color-orange { color: #ea580c; }

/* Classes de alinhamento */
.container-content.center-content {
    display: flex;
    justify-content: center;
    align-items: center;
    min-height: 400px;
}

.container-content.center-horizontal {
    display: flex;
    justify-content: center;
}

.container-content.center-vertical {
    display: flex;
    align-items: center;
    min-height: 400px;
}
</style>
