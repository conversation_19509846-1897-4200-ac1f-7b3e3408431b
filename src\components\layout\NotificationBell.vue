<template>
  <div class="notification-bell">
    <IluriaButton
      class="notification-button"
      @click="toggleNotifications"
      :aria-label="$t('notifications.bellAriaLabel')"
    >
      <HugeiconsIcon 
        :icon="Notification01Icon"
        :size="20" 
        :class="{ 'has-notifications': hasUnreadNotifications }"
      />
      <span 
        v-if="unreadCount > 0" 
        class="notification-badge"
        :class="{ 'pulse': hasNewNotifications }"
      >
        {{ unreadCount > 99 ? '99+' : unreadCount }}
      </span>
    </IluriaButton>

    <!-- Notification Dropdown -->
    <div 
      v-if="showNotifications" 
      class="notification-dropdown"
      @click.stop
    >
      <div class="notification-header">
        <h3 class="notification-title">{{ $t('notifications.title') }}</h3>
        <div class="notification-actions">
          <IluriaButton
            v-if="unreadCount > 0"
            class="mark-all-read-btn"
            @click="markAllAsRead"
            :disabled="markingAllAsRead"
          >
            <HugeiconsIcon :icon="CheckmarkCircle02Icon" :size="16" />
          </IluriaButton>
          <IluriaButton
            variant="ghost"
            size="small"
            class="settings-btn"
            @click="goToSettings"
            :aria-label="$t('notifications.settings')"
          >
            <HugeiconsIcon :icon="Settings02Icon" :size="16" />
          </IluriaButton>
        </div>
      </div>

      <div class="notification-content">
        <!-- Loading State -->
        <div v-if="loading" class="notification-loading">
          <div class="loading-spinner"></div>
          <p>{{ $t('notifications.loading') }}</p>
        </div>

        <!-- Empty State -->
        <div v-else-if="notifications.length === 0" class="notification-empty">
          <HugeiconsIcon :icon="Notification01Icon" :size="48" class="empty-icon" />
          <h4>{{ $t('notifications.empty.title') }}</h4>
          <p>{{ $t('notifications.empty.message') }}</p>
        </div>

        <!-- Notifications List -->
        <div v-else class="notification-list">
          <div
            v-for="notification in notifications"
            :key="notification.id"
            class="notification-item"
            :class="{ 
              'unread': !notification.isRead,
              'critical': notification.notificationType === 'SECURITY_ALERTS'
            }"
            @click="handleNotificationClick(notification)"
          >
            <div class="notification-icon">
              <HugeiconsIcon 
                :icon="getNotificationIcon(notification.notificationType)" 
                :size="20" 
                :class="getNotificationIconClass(notification.notificationType)"
              />
            </div>
            <div class="notification-body">
              <h5 class="notification-subject">{{ notification.title }}</h5>
              <p class="notification-message">{{ notification.content }}</p>
              <span class="notification-time">{{ formatTime(notification.createdAt) }}</span>
            </div>
            
            <!-- Individual Action Buttons -->
            <div class="notification-actions-individual">    
              <!-- Delete Button -->
              <IluriaButton
                variant="ghost"
                size="small"
                class="action-btn delete-btn"
                @click.stop="deleteNotificationForUser(notification)"
                :disabled="notification.processing"
                :aria-label="$t('notifications.delete')"
              >
                <HugeiconsIcon :icon="Delete01Icon" :size="14" />
              </IluriaButton>
            </div>
            
            <div v-if="!notification.isRead" class="unread-indicator"></div>
          </div>
        </div>

        <!-- Load More -->
        <div v-if="hasMoreNotifications" class="notification-footer">
          <IluriaButton
            variant="ghost"
            size="small"
            @click="loadMoreNotifications"
            :loading="loadingMore"
            class="load-more-btn"
          >
            {{ $t('notifications.loadMore') }}
          </IluriaButton>
        </div>
      </div>
    </div>

    <!-- Overlay -->
    <div 
      v-if="showNotifications" 
      class="notification-overlay"
      @click="closeNotifications"
    ></div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, onUnmounted, watch, getCurrentInstance } from 'vue'
import { useRouter } from 'vue-router'
import { useTheme } from '@/composables/useTheme'
import { useToast } from '@/services/toast.service'
import { useNotificationWebSocket } from '@/composables/useNotificationWebSocket'
import { useAuthStore } from '@/stores/auth.store'
import notificationService from '@/services/notification.service'
import IluriaButton from '@/components/iluria/IluriaButton.vue'
import { HugeiconsIcon } from '@hugeicons/vue'
import {
  Notification01Icon,
  Settings02Icon,
  ShoppingBag01Icon,
  SecurityCheckIcon,
  SystemUpdateIcon,
  StarIcon,
  HelpCircleIcon,
  Mail01Icon,
  UserAdd01Icon,
  UserGroupIcon,
  Comment01Icon,
  ChartUpIcon,
  CheckmarkCircle01Icon,
  CheckmarkCircle02Icon,
  Time04Icon,
  UserGroup03Icon,
  AddTeamIcon,
  DollarSquareIcon,
  Chart02Icon,
  Megaphone02Icon,
  Delete01Icon,
  MailOpen01Icon
} from '@hugeicons-pro/core-stroke-rounded'

// Props
const props = defineProps({
  type: {
    type: String,
    default: 'user', // 'user' | 'store'
    validator: (value) => ['user', 'store'].includes(value)
  },
  storeId: {
    type: String,
    default: null
  }
})


// Composables
const router = useRouter()
const { themeMode } = useTheme()
const { showToast } = useToast()
const authStore = useAuthStore()
const instance = getCurrentInstance()
const $t = instance?.appContext.config.globalProperties.$t

// WebSocket para notificações em tempo real
const {
  isConnected: wsConnected,
  newNotification,
  startListening: startWebSocketListening,
  disconnect: disconnectWebSocket,
  markNotificationAsProcessed
} = useNotificationWebSocket()

// State
const showNotifications = ref(false)
const notifications = ref([])
const unreadCount = ref(0)
const loading = ref(false)
const loadingMore = ref(false)
const markingAllAsRead = ref(false)
const hasMoreNotifications = ref(false)
const hasNewNotifications = ref(false)
const currentPage = ref(1)
const pageSize = 10

// Local cache system removed - now using backend-only filtering

// Computed
const hasUnreadNotifications = computed(() => unreadCount.value > 0)

// Notification icon mapping
const getNotificationIcon = (type) => {
  const iconMap = {
    // Segurança e Conta
    'SECURITY_ALERTS': SecurityCheckIcon,
    'ACCOUNT_CHANGES': UserAdd01Icon,
    'LOGIN_NOTIFICATIONS': SecurityCheckIcon,
    
    // Sistema e Atualizações
    'SYSTEM_UPDATES': SystemUpdateIcon,
    'MAINTENANCE_NOTIFICATIONS': SystemUpdateIcon,
    'FEATURE_ANNOUNCEMENTS': Megaphone02Icon,
    
    // Marketing e Email
    'PROMOTIONAL_EMAILS': Mail01Icon,
    'NEWSLETTER': Mail01Icon,
    'NEWSLETTER_SUBSCRIPTIONS': Mail01Icon,
    'PRODUCT_RECOMMENDATIONS': ChartUpIcon,
    
    // Interações Sociais
    'MENTIONS': Comment01Icon,
    'COMMENTS': Comment01Icon,
    'FOLLOW_NOTIFICATIONS': UserGroupIcon,
    
    // Relatórios e Analytics
    'WEEKLY_SUMMARY': Chart02Icon,
    'USAGE_REPORTS': ChartUpIcon,
    
    // Vendas e Comércio
    'NEW_SALES': DollarSquareIcon,
    'PRODUCT_REVIEWS': StarIcon,
    'PRODUCT_QUESTIONS': HelpCircleIcon,
    'NEW_CUSTOMER_REGISTRATIONS': UserAdd01Icon,
    
    // Colaboração e Equipe
    'COLLAB_INVITES': AddTeamIcon,
    'COLLAB_UPDATES': UserGroupIcon,
    'ROLE_CHANGES': UserAdd01Icon
  }
  return iconMap[type] || Notification01Icon
}

const getNotificationIconClass = (type) => {
  const classMap = {
    // Segurança e Conta - Vermelho/Laranja para alertas críticos
    'SECURITY_ALERTS': 'text-red-500',
    'ACCOUNT_CHANGES': 'text-orange-500',
    'LOGIN_NOTIFICATIONS': 'text-yellow-500',
    
    // Sistema e Atualizações - Azul para informações
    'SYSTEM_UPDATES': 'text-blue-500',
    'MAINTENANCE_NOTIFICATIONS': 'text-blue-600',
    'FEATURE_ANNOUNCEMENTS': 'text-indigo-500',
    
    // Marketing e Email - Azul claro e ciano
    'PROMOTIONAL_EMAILS': 'text-cyan-500',
    'NEWSLETTER': 'text-sky-500',
    'NEWSLETTER_SUBSCRIPTIONS': 'text-blue-400',
    'PRODUCT_RECOMMENDATIONS': 'text-purple-500',
    
    // Interações Sociais - Verde para interações positivas
    'MENTIONS': 'text-green-500',
    'COMMENTS': 'text-emerald-500',
    'FOLLOW_NOTIFICATIONS': 'text-teal-500',
    
    // Relatórios e Analytics - Roxo para dados
    'WEEKLY_SUMMARY': 'text-violet-500',
    'USAGE_REPORTS': 'text-purple-600',
    
    // Vendas e Comércio - Verde para dinheiro/vendas
    'NEW_SALES': 'text-green-600',
    'PRODUCT_REVIEWS': 'text-amber-500',
    'PRODUCT_QUESTIONS': 'text-blue-500',
    'NEW_CUSTOMER_REGISTRATIONS': 'text-emerald-500',
    
    // Colaboração e Equipe - Azul/roxo para equipe
    'COLLAB_INVITES': 'text-blue-600',
    'COLLAB_UPDATES': 'text-indigo-500',
    'ROLE_CHANGES': 'text-amber-600'
  }
  return classMap[type] || 'text-gray-500'
}

// Badge count is now managed entirely by backend - no sync needed

// Methods
const toggleNotifications = () => {
  showNotifications.value = !showNotifications.value
  if (showNotifications.value && notifications.value.length === 0) {
    loadNotifications()
  }
}

const closeNotifications = () => {
  showNotifications.value = false
}

const loadNotifications = async () => {
  loading.value = true
  try {
    const notificationType = props.type
    const storeId = props.type === 'store' ? props.storeId : null
    
    const response = await notificationService.getUnreadNotifications(notificationType, storeId)
    
    // Reset current page when reloading notifications
    currentPage.value = 1
    
    // Backend provides consistent filtered results
    notifications.value = response.notifications || []
    hasMoreNotifications.value = response.hasMore || false
    unreadCount.value = response.unreadCount || 0
    
  } catch (error) {
    console.error('Error loading notifications:', error)
    console.error('Request details:', {
      type: props.type,
      storeId: props.type === 'store' ? props.storeId : null,
      error: error.response?.data || error.message
    })
    
    // Reset to safe state on error
    notifications.value = []
    unreadCount.value = 0
    hasMoreNotifications.value = false
    showToast($t('notifications.error.loadFailed'), 'error')
  } finally {
    loading.value = false
  }
}

const loadMoreNotifications = async () => {
  loadingMore.value = true
  try {
    currentPage.value++
    
    const notificationType = props.type
    const storeId = props.type === 'store' ? props.storeId : null
    
    const response = await notificationService.getUnreadNotifications(
      notificationType, 
      storeId,
      currentPage.value,
      pageSize
    )
    
    // Backend already filters deleted notifications
    const newNotifications = response.notifications || []
    
    notifications.value = [...notifications.value, ...newNotifications]
    hasMoreNotifications.value = response.hasMore
    
  } catch (error) {
    currentPage.value--
    console.error('Error loading more notifications:', error)
    showToast($t('notifications.error.loadMoreFailed'), 'error')
  } finally {
    loadingMore.value = false
  }
}

const markAllAsRead = async () => {
  if (markingAllAsRead.value) return
  
  markingAllAsRead.value = true
  try {
    const notificationType = props.type
    const storeId = notificationType === 'store' ? props.storeId : null
    
    // 1. FIRST call server and wait for response
    const response = await notificationService.markAllAsReadWithoutRemoving(notificationType, storeId)
    
    // 2. ONLY update local state after server confirms success
    if (response && (response.success || response.affectedCount >= 0)) {
      notifications.value.forEach(notification => {
        if (!notification.isRead) {
          notification.isRead = true
        }
      })
      
      // Use server response count (backend is now consistent)
      unreadCount.value = response.remainingCount !== undefined ? response.remainingCount : 0
      
    } else {
      // Server response indicates failure
      throw new Error('Server did not confirm success')
    }
    
  } catch (error) {
    console.error('Error marking notifications as read:', error)
    
    // 3. Refresh from server to get accurate state (don't trust local state)
    await refreshUnreadCount()
    
    // 4. Optionally reload notifications to show correct read states
    if (showNotifications.value) {
      await loadNotifications()
    }
    
    showToast($t('notifications.error.markReadFailed'), 'error')
  } finally {
    markingAllAsRead.value = false
  }
}

const handleNotificationClick = async (notification) => {
  try {
    // 1. Mark notification as read when clicked (unless already read)
    if (!notification.isRead && !notification.processing) {
      await markNotificationAsRead(notification)
    }
    
    // 2. Navigate if actionUrl exists
    if (notification.actionUrl) {
      router.push(notification.actionUrl)
      closeNotifications()
    }
  } catch (error) {
    console.error('Error handling notification click:', error)
    showToast($t('notifications.error.navigationFailed'), 'error')
  }
}

// NEW METHOD: Mark individual notification as read without removing
const markNotificationAsRead = async (notification) => {
  if (notification.processing || notification.isRead) return
  
  notification.processing = true
  try {
    // 1. FIRST call server and wait for response
    const response = await notificationService.markNotificationAsReadWithoutRemoving(notification.id)
    
    // 2. ONLY update local state after server confirms success
    if (response && (response.success || response.affectedCount >= 0)) {
      notification.isRead = true
      
      // Use server response count (backend is now consistent)
      unreadCount.value = response.remainingCount !== undefined ? response.remainingCount : 0
      
    } else {
      // Server response indicates failure
      throw new Error('Server did not confirm success')
    }
    
  } catch (error) {
    console.error('Error marking notification as read:', error)
    
    // 3. Refresh accurate state from server (don't trust local state)
    await refreshUnreadCount()
    
    showToast($t('notifications.error.markReadFailed'), 'error')
  } finally {
    notification.processing = false
  }
}

// NEW METHOD: Delete notification for current user only
const deleteNotificationForUser = async (notification) => {
  if (notification.processing) return
  
  notification.processing = true
  try {
    const response = await notificationService.deleteNotificationForUser(notification.id)
    
    // Server handles deletion, remove from local list immediately
    if (response && (response.success || response.affectedCount > 0)) {
      const index = notifications.value.findIndex(n => n.id === notification.id)
      if (index !== -1) {
        notifications.value.splice(index, 1)
      }
      
      // Update unread count from server response or refresh
      if (response.remainingCount !== undefined) {
        unreadCount.value = response.remainingCount
      } else {
        await refreshUnreadCount()
      }
      
      showToast($t('notifications.deleted'), 'success')
    } else {
      // If server response indicates failure, show error
      showToast($t('notifications.error.deleteFailed'), 'error')
    }
  } catch (error) {
    console.error('Error deleting notification:', error)
    
    // Show specific error message based on the response
    const errorMessage = error.response?.data?.message || $t('notifications.error.deleteFailed')
    showToast(errorMessage, 'error')
  } finally {
    notification.processing = false
  }
}

const goToSettings = () => {
  // Different routes based on notification type
  if (props.type === 'store') {
    // For store notifications (NavBar), go to store email notifications
    const settingsRoute = `/settings/email-notifications`
    router.push(settingsRoute)
  } else {
    // For user notifications (UserNavBar), go to user settings notifications section
    const settingsRoute = `/user/settings?section=notifications`
    router.push(settingsRoute)
  }
  closeNotifications()
}

const formatTime = (timestamp) => {
  const now = new Date()
  const time = new Date(timestamp)
  const diffMs = now - time
  const diffMins = Math.floor(diffMs / 60000)
  const diffHours = Math.floor(diffMs / 3600000)
  const diffDays = Math.floor(diffMs / 86400000)

  if (diffMins < 1) return $t('notifications.time.now')
  if (diffMins < 60) return `${diffMins}${$t('notifications.time.minutes')}`
  if (diffHours < 24) return `${diffHours}${$t('notifications.time.hours')}`
  if (diffDays < 7) return `${diffDays}${$t('notifications.time.days')}`
  return time.toLocaleDateString()
}

// Handle new WebSocket notifications
const handleNewWebSocketNotification = (notification) => {
  // Adicionar à lista se não existir
  const existingIndex = notifications.value.findIndex(n => n.id === notification.id)
  
  if (existingIndex === -1) {
    notifications.value.unshift(notification)
    unreadCount.value++
    hasNewNotifications.value = true
    
    // Mostrar toast para notificação
    showToast(notification.title, 'info')
  }
  
  markNotificationAsProcessed()
}

// Start real-time updates
const startRealtimeUpdates = () => {
  // Conectar WebSocket apenas se estiver autenticado
  // O watch no useNotificationWebSocket já cuida da conexão automática
  if (authStore?.userLoggedIn) {
    startWebSocketListening()
  }
}

// Click outside handler
const handleClickOutside = (event) => {
  if (!event.target.closest('.notification-bell')) {
    closeNotifications()
  }
}

// Load initial unread count by getting actual notifications (more accurate)
const loadUnreadCount = async () => {
  try {
    const notificationType = props.type
    const storeId = props.type === 'store' ? props.storeId : null
    
    const response = await notificationService.getUnreadNotifications(notificationType, storeId, 0, 100)
    
    // Backend provides consistent count
    unreadCount.value = response.unreadCount || 0
    
  } catch (error) {
    console.error('Error loading initial unread count:', error)
    unreadCount.value = 0
  }
}

// Refresh unread count (useful for updates after operations)
const refreshUnreadCount = async () => {
  try {
    const notificationType = props.type
    const storeId = props.type === 'store' ? props.storeId : null
    
    const response = await notificationService.getUnreadNotifications(notificationType, storeId, 0, 100)
    
    // Backend provides consistent count
    unreadCount.value = response.unreadCount || 0
    
  } catch (error) {
    console.error('Error refreshing unread count:', error)
  }
}

// Lifecycle
onMounted(async () => {
  document.addEventListener('click', handleClickOutside)
  
  // Load initial unread count immediately to show badge
  await loadUnreadCount()
  
  // Conectar WebSocket se já estiver autenticado
  startRealtimeUpdates()
})

onUnmounted(() => {
  document.removeEventListener('click', handleClickOutside)
  disconnectWebSocket()
})

// Watch for new WebSocket notifications (ÚNICO listener)
watch(newNotification, (notification) => {
  if (notification) {
    handleNewWebSocketNotification(notification)
  }
})

// Watch for new notifications animation
watch(hasNewNotifications, (newVal) => {
  if (newVal) {
    setTimeout(() => {
      hasNewNotifications.value = false
    }, 3000) // Stop pulsing after 3 seconds
  }
})

// Watch for prop changes to reload count
watch(() => [props.type, props.storeId], async () => {
  // Reset state when context changes
  notifications.value = []
  
  // Load count for new context
  await loadUnreadCount()
}, { immediate: false })
</script>

<style scoped>
.notification-bell {
  position: relative;
}

.notification-button {
    background: transparent;
    border: transparent;
    padding: 8px;
    border-radius: 6px;
    color: var(--iluria-color-navbar-fg);
    cursor: pointer;
    transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
    display: flex;
    align-items: center;
    justify-content: center;
}

.notification-button:hover {
  transform: scale(1.05);
}

.has-notifications {
  color: var(--iluria-color-primary);
  animation: subtle-bounce 2s infinite;
}

.notification-badge {
  position: absolute;
  top: -4px;
  right: -4px;
  background: var(--iluria-color-danger);
  color: rgb(255, 255, 255);
  font-size: 10px;
  font-weight: 600;
  min-width: 18px;
  height: 18px;
  border-radius: 9px;
  display: flex;
  align-items: center;
  justify-content: center;
  line-height: 1;
  padding: 0 4px;
  border: 0px solid var(--iluria-color-container-bg);
}

.notification-badge.pulse {
  animation: pulse 1.5s infinite;
}

.notification-dropdown {
  position: absolute;
  top: calc(100% + 8px);
  right: 0;
  width: 380px;
  max-width: 90vw;
  background: var(--iluria-color-container-bg);
  border: 1px solid var(--iluria-color-border);
  border-radius: 12px;
  box-shadow: var(--iluria-shadow-lg);
  z-index: 1000;
  overflow: hidden;
  max-height: 500px;
  display: flex;
  flex-direction: column;
}

.notification-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 20px;
  border-bottom: 1px solid var(--iluria-color-border);
  background: var(--iluria-color-surface);
}

.notification-title {
  font-size: 16px;
  font-weight: 600;
  color: var(--iluria-color-text-primary);
  margin: 0;
}

.notification-actions {
  display: flex;
  gap: 8px;
  align-items: center;
}

.mark-all-read-btn {
  border: none !important;
  border-radius: 6px !important;
  font-size: 10px !important;
  font-weight: 400 !important;
  padding: 2px 6px !important;
  gap: 2px !important;
  transition: all 0.2s ease !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  cursor: pointer !important;
  min-height: 30px !important;
  outline: none !important;
}

.mark-all-read-btn:active:not(:disabled) {
  transform: translateY(0) !important;
  box-shadow: 0 1px 4px rgba(59, 130, 246, 0.2) !important;
}

.mark-all-read-btn:disabled {
  opacity: 0.6 !important;
  cursor: not-allowed !important;
  transform: none !important;
}

.mark-all-read-btn:focus {
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.3) !important;
}

.settings-btn {
  border-radius: 6px !important;
  transition: all 0.2s ease !important;
}

.settings-btn:hover {
  background: var(--iluria-color-background-hover) !important;
  transform: translateY(-1px) !important;
}

.notification-content {
  flex: 1;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

.notification-loading,
.notification-empty {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px 20px;
  text-align: center;
}

.loading-spinner {
  width: 24px;
  height: 24px;
  border: 2px solid var(--iluria-color-border);
  border-top: 2px solid var(--iluria-color-primary);
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 12px;
}

.empty-icon {
  color: var(--iluria-color-text-secondary);
  margin-bottom: 16px;
}

.notification-list {
  flex: 1;
  overflow-y: auto;
  max-height: 360px;
}

.notification-item {
  display: flex;
  padding: 16px 20px;
  border-bottom: 1px solid var(--iluria-color-border);
  cursor: pointer;
  transition: background-color 0.2s ease;
  position: relative;
  align-items: flex-start;
}

.notification-item:hover {
  background: var(--iluria-color-surface);
}

.notification-item:last-child {
  border-bottom: none;
}

.notification-item.unread {
  background: var(--iluria-color-primary-light);
}

.notification-item.critical {
  border-left: 3px solid var(--iluria-color-danger);
}

.notification-icon {
  margin-right: 12px;
  flex-shrink: 0;
  padding-top: 2px;
}

.notification-body {
  flex: 1;
  min-width: 0;
}

.notification-subject {
  font-size: 14px;
  font-weight: 600;
  color: var(--iluria-color-text-primary);
  margin: 0 0 4px 0;
  line-height: 1.3;
}

.notification-message {
  font-size: 13px;
  color: var(--iluria-color-text-secondary);
  margin: 0 0 6px 0;
  line-height: 1.4;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.notification-time {
  font-size: 11px;
  color: var(--iluria-color-text-tertiary);
}

.notification-actions-individual {
  display: flex;
  flex-direction: column;
  gap: 4px;
  margin-left: 8px;
  flex-shrink: 0;
  opacity: 0;
  transition: opacity 0.2s ease;
}

.notification-item:hover .notification-actions-individual {
  opacity: 1;
}

.action-btn {
  padding: 4px !important;
  min-height: 24px !important;
  width: 24px !important;
  border-radius: 4px !important;
  transition: all 0.2s ease !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
}

.action-btn:hover {
  transform: scale(1.1) !important;
}

.mark-read-btn {
  color: var(--iluria-color-success) !important;
}

.mark-read-btn:hover {
  background: var(--iluria-color-success-light) !important;
}

.delete-btn {
  color: var(--iluria-color-danger) !important;
}

.delete-btn:hover {
  background: var(--iluria-color-danger-light) !important;
}

.action-btn:disabled {
  opacity: 0.5 !important;
  cursor: not-allowed !important;
  transform: none !important;
}

.unread-indicator {
  position: absolute;
  right: 16px;
  top: 50%;
  transform: translateY(-50%);
  width: 8px;
  height: 8px;
  background: var(--iluria-color-primary);
  border-radius: 50%;
  flex-shrink: 0;
}

.notification-footer {
  padding: 12px 20px;
  border-top: 1px solid var(--iluria-color-border);
  text-align: center;
}

.load-more-btn {
  width: 100%;
}

.notification-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 999;
  background: transparent;
}

/* Animations */
@keyframes subtle-bounce {
  0%, 20%, 50%, 80%, 100% {
    transform: translateY(0);
  }
  40% {
    transform: translateY(-2px);
  }
  60% {
    transform: translateY(-1px);
  }
}

@keyframes pulse {
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.1);
  }
  100% {
    transform: scale(1);
  }
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Responsive adjustments */
@media (max-width: 640px) {
  .notification-dropdown {
    width: 320px;
    right: -40px;
  }
  
  .notification-item {
    padding: 12px 16px;
  }
  
  .notification-header {
    padding: 12px 16px;
  }
}
</style>