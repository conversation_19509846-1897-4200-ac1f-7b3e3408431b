<template>
  <div class="seo-settings-container">
    <!-- Header Section -->
    <IluriaHeader
      :title="$t('storeSeo.title')"
      subtitle="Configure as informações de SEO da sua loja para melhorar o posicionamento nos mecanismos de busca"
      :showSave="true"
      :saveText="$t('storeSeo.saveButton')"
      @save-click="saveSettings"
    />

    <!-- Main Content -->
    <Form 
      v-if="dataLoaded" 
      v-slot="$form" 
      @submit="saveSettings" 
      :resolver="resolver" 
      :validate-on-submit="true" 
      :validate-on-blur="true" 
      :validate-on-value-update="false" 
      :initial-values="formData" 
      class="form-container"
    >
      
      <!-- Configuração de SEO da Página Principal -->
      <ViewContainer 
        title="Configuração de SEO da Página Principal"
        subtitle="Configure título, descrição e imagem de destaque para otimização nos motores de busca"
        :icon="Search01Icon"
        iconColor="green"
      >
        <div class="seo-grid">
          <!-- Coluna da imagem -->
          <div class="image-section">
            <div class="form-field">
              <label class="field-label">{{ $t('storeSeo.featuredImage') }}</label>
              <IluriaSimpleImageUpload
                v-model="formData.featuredImageUrl"
                accept="image/*"
                :add-button-text="$t('storeSeo.addImage')"
                :change-button-text="$t('storeSeo.changeImage')"
                :remove-button-text="$t('storeSeo.removeImage')"
                :format-hint="$t('storeSeo.imageFormats')"
                :prevent-cache="true"
                @change="onImageChange"
                class="image-upload"
              />
            </div>
          </div>

          <!-- Coluna dos campos de texto -->
          <div class="content-section">
            <!-- Título da Página -->
            <div class="form-field">
              <IluriaInputText 
                id="metaTitle"
                :label="$t('storeSeo.pageTitle')"
                v-model="formData.metaTitle" 
                :placeholder="$t('storeSeo.pageTitlePlaceholder')"
                :formContext="$form.metaTitle"
              />
              <p class="field-hint">{{ $t('storeSeo.titleMaxLength') }}</p>
            </div>

            <!-- Descrição da Página -->
            <div class="form-field">
              <IluriaInputText
                id="metaDescription"
                :label="$t('storeSeo.pageDescription')"
                v-model="formData.metaDescription" 
                :placeholder="$t('storeSeo.pageDescriptionPlaceholder')"
                :formContext="$form.metaDescription"
                type="textarea"
                rows="3"
              />
              <p class="field-hint">{{ $t('storeSeo.descriptionMaxLength') }}</p>
            </div>


            </div>
          </div>

      </ViewContainer>


    </Form>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, getCurrentInstance } from 'vue';
import { useI18n } from 'vue-i18n';
import { z } from 'zod';
import { zodResolver } from '@primevue/forms/resolvers/zod';
import { Form } from '@primevue/forms';
import ViewContainer from '@/components/layout/ViewContainer.vue';
import IluriaInputText from '@/components/iluria/form/IluriaInputText.vue';

import IluriaSimpleImageUpload from '@/components/iluria/form/IluriaSimpleImageUpload.vue';
import IluriaButton from '@/components/iluria/IluriaButton.vue';
import IluriaHeader from '@/components/iluria/IluriaHeader.vue';
import { useToast } from '@/services/toast.service';
import seoService from '@/services/seo.service';
import { requiredText } from '@/services/validation.service';
import { HugeiconsIcon } from '@hugeicons/vue'
import { 
  Search01Icon,
  FloppyDiskIcon
} from '@hugeicons-pro/core-stroke-rounded'

const { t } = useI18n();
const toast = useToast();
const { proxy } = getCurrentInstance();
const _uid = proxy._.uid;
const dataLoaded = ref(false);
const componentMounted = ref(false);
const isUploading = ref(false);
const selectedImageFile = ref(null);

const formData = reactive({
  metaTitle: '',
  metaDescription: '',
  featuredImage: '',
  featuredImageUrl: ''
});

const onImageChange = async (file) => {
  if (!file) {
    await handleImageRemoval();
    return;
  }
  
  const MAX_SIZE_MB = 10;
  const MAX_SIZE_BYTES = MAX_SIZE_MB * 1024 * 1024;
  
  if (file.size > MAX_SIZE_BYTES) {
    toast.showError(t('storeSeo.imageTooLarge') || `A imagem não pode ser maior que ${MAX_SIZE_MB}MB`);
    return;
  }
  
  if (!file.type || !file.type.startsWith('image/')) {
    toast.showError(t('storeSeo.imageFormatError') || 'Formato de imagem inválido');
    return;
  }
  
  selectedImageFile.value = file;
  
  const reader = new FileReader();
  reader.onload = (e) => {
    formData.featuredImageUrl = e.target.result;
  };
  reader.readAsDataURL(file);
};

const handleImageRemoval = async () => {
  if (formData.featuredImage) {
    try {
      await seoService.deleteImage(formData.featuredImage);
    } catch (error) {
      console.error('Erro ao remover a imagem:', error);
      toast.showError(t('storeSeo.imageDeleteError') || 'Erro ao remover a imagem');
    }
  }
  
  formData.featuredImage = '';
  formData.featuredImageUrl = '';
  selectedImageFile.value = null;
};

const onImageUploadError = (erro) => {
  if (componentMounted.value) {
    toast.showError(t('storeSeo.imageUploadError'));
  }
};

const resolver = zodResolver(
  z.object({
    metaTitle: requiredText(t('storeSeo.pageTitle')),
    metaDescription: requiredText(t('storeSeo.pageDescription')),
  })
);

const handleImageUpload = async (data) => {
  if (!selectedImageFile.value) return;
  
  try {
    const result = await seoService.uploadImage(selectedImageFile.value, formData);
    if (result?.featuredImage) {
      formData.featuredImage = result.featuredImage;
      formData.featuredImageUrl = result.featuredImage;
      
      await seoService.updateStoreSeoSettings({
        ...data,
        featuredImage: result.featuredImage
      });
      
    }
  } catch (uploadError) {
    console.error('Erro ao fazer upload da imagem:', uploadError);
    handleUploadError(uploadError);
  }
};

const handleUploadError = (error) => {
  if (!error.message) {
    toast.showError(t('storeSeo.imageUploadError') || 'Erro ao fazer upload da imagem');
    return;
  }
  
  if (error.message.includes('muito grande') || error.message.includes('tamanho máximo')) {
    toast.showError(t('storeSeo.imageTooLarge') || 'A imagem não pode ser maior que 10MB');
  } else if (error.message.includes('formato')) {
    toast.showError(t('storeSeo.imageFormatError') || 'Formato de imagem inválido ou dados corrompidos');
  } else if (error.message.includes('servidor')) {
    toast.showError(t('storeSeo.imageServerError') || 'Erro no servidor ao processar a imagem');
  } else if (error.message.includes('conexão') || error.code === 'ERR_NETWORK' || 
            (error.message && error.message.includes('Network Error'))) {
    toast.showError(t('storeSeo.networkError') || 'Erro de conexão com o servidor');
  } else {
    toast.showError(t('storeSeo.imageUploadError') || 'Erro ao fazer upload da imagem');
  }
};

const prepareSettingsData = () => {
  return {
    metaTitle: formData.metaTitle,
    metaDescription: formData.metaDescription,
    featuredImage: formData.featuredImage,
    enableSEOIndexation: formData.enableSEOIndexation
  };
};

const saveSettings = async () => {
  if (!componentMounted.value) return;
  
  if (!formData.metaTitle || !formData.metaDescription) {
    toast.showError('Título e descrição são obrigatórios');
    return;
  }
  
  isUploading.value = true;
  
  try {
    const data = prepareSettingsData();
    await seoService.updateStoreSeoSettings(data);
    
    if (selectedImageFile.value) {
      await handleImageUpload(data);
    }
    
    toast.showSuccess(t('storeSeo.saveSuccess') || 'Configurações salvas com sucesso');
    selectedImageFile.value = null;
    
    await loadData();
  } catch (error) {
    console.error('Erro ao salvar configurações SEO:', error);
    toast.showError(t('storeSeo.saveError') || 'Erro ao salvar configurações');
  } finally {
    isUploading.value = false;
  }
};

const loadData = async () => {
  try {
    const response = await seoService.getStoreSeoSettings();
    if (response) {
      formData.metaTitle = response.metaTitle || '';
      formData.metaDescription = response.metaDescription || '';
      formData.featuredImage = response.featuredImage || '';
      formData.featuredImageUrl = response.featuredImage || '';
      formData.enableSEOIndexation = !!response.enableSEOIndexation;
      
      dataLoaded.value = true;
    }
  } catch (erro) {
    console.error('Erro ao carregar configurações SEO:', erro);
    if (componentMounted.value) {
      toast.showError(t('storeSeo.loadError') || 'Erro ao carregar configurações');
    }
  }
};

onMounted(async () => {
  componentMounted.value = true;
  loadData();
});
</script>

<style scoped>
.seo-settings-container {
  max-width: 1400px;
  margin: 0 auto;
  padding: 24px;
  background: var(--iluria-color-bg);
  color: var(--iluria-color-text-primary);
  min-height: 100vh;
}



/* Form Container */
.form-container {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

/* SEO Grid */
.seo-grid {
  display: grid;
  grid-template-columns: 300px 1fr;
  gap: 32px;
  margin-bottom: 24px;
}

.image-section {
  display: flex;
  flex-direction: column;
}

.content-section {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

/* Form Fields */
.form-field {
  display: flex;
  flex-direction: column;
  gap: 6px;
}

.field-label {
  font-size: 14px;
  font-weight: 500;
  color: var(--iluria-color-text-primary);
}

.field-hint {
  font-size: 12px;
  color: var(--iluria-color-text-secondary);
  margin: 0;
}

.image-upload {
  width: 100%;
}



/* Responsive */
@media (max-width: 1024px) {
  .seo-grid {
    grid-template-columns: 1fr;
    gap: 24px;
  }
  
  .image-section {
    order: 2;
  }
  
  .content-section {
    order: 1;
  }
}

@media (max-width: 768px) {
  .seo-settings-container {
    padding: 16px;
  }
  

  
  .form-container {
    gap: 20px;
  }
  
  .seo-grid {
    gap: 20px;
  }
  
  .content-section {
    gap: 16px;
  }
}

@media (max-width: 640px) {

}

@media (max-width: 480px) {
  .seo-settings-container {
    padding: 12px;
  }
  

  
  .form-container {
    gap: 16px;
  }
  
  .seo-grid {
    gap: 16px;
  }
  
  .content-section {
    gap: 14px;
  }
}

/* Form validation styles */
:deep(.p-form-invalid) {
  border-color: var(--iluria-color-error) !important;
}

:deep(.p-form-error) {
  color: var(--iluria-color-error);
  font-size: 12px;
  margin-top: 4px;
}
</style>
