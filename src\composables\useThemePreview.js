import { ref } from 'vue'
import { AUTH_CONFIG } from '@/config/auth'
import { useAuthStore } from '@/stores/auth.store'
import tokenManager from '@/services/tokenManager.service'

// Estado global do preview
const previewDevice = ref('desktop')
const isPreviewLoading = ref(false)

export function useThemePreview() {
  
  const getThemePreviewUrl = (theme) => {
    if (!theme) {
      console.warn('🚨 getThemePreviewUrl: No theme provided')
      return null
    }

    const authStore = useAuthStore()
    const currentToken = authStore.getCurrentToken()
    const userInfo = tokenManager.extractUserInfo(currentToken)

    if (!userInfo?.storeId) {
      console.warn('🚨 getThemePreviewUrl: No storeId available in userInfo')
      return null
    }

    // Se o tema tem template_s3_path, usar o S3 diretamente
    if (theme.templateS3Path || theme.template_s3_path) {
      const s3Path = theme.templateS3Path || theme.template_s3_path

      // Usar o mesmo formato que o backend (DevEnvFilter)
      const s3BaseUrl = 'https://iluria-bucket-dev.s3.us-east-1.amazonaws.com'

      // Garantir que o caminho termine com index.html
      let fullPath = s3Path
      if (!fullPath.endsWith('/')) {
        fullPath += '/'
      }
      fullPath += 'index.html'

      // Remover barras duplas e garantir formato correto
      const cleanPath = fullPath.replace(/\/+/g, '/').replace(/^\//, '')
      const finalUrl = `${s3BaseUrl}/${cleanPath}?preview=true&device=${previewDevice.value}&t=${Date.now()}`

      return finalUrl
    }

    // Fallback para o comportamento anterior (dev-env da loja)
    const baseUrl = AUTH_CONFIG.ENDPOINTS.DEV_ENV

    let themeFile = 'index.html'

    if (theme.filename) {
      themeFile = theme.filename
      if (!themeFile.endsWith('.html')) {
        themeFile += '.html'
      }
    } else {
      themeFile = 'index.html'
    }

    const previewUrl = `${baseUrl}/${userInfo.storeId}/${themeFile}?preview=true&device=${previewDevice.value}&t=${Date.now()}`

    return previewUrl
  }

  const getDevicePreviewUrl = (theme, device) => {
    if (!theme) {
      return null
    }

    const authStore = useAuthStore()
    const currentToken = authStore.getCurrentToken()
    const userInfo = tokenManager.extractUserInfo(currentToken)

    if (!userInfo?.storeId) {
      return null
    }

    // Se o tema tem template_s3_path, usar o S3 diretamente
    if (theme.templateS3Path || theme.template_s3_path) {
      const s3Path = theme.templateS3Path || theme.template_s3_path
      const s3BaseUrl = 'https://iluria-bucket-dev.s3.us-east-1.amazonaws.com'

      // Garantir que o caminho termine com index.html
      let fullPath = s3Path
      if (!fullPath.endsWith('/')) {
        fullPath += '/'
      }
      fullPath += 'index.html'

      // Remover barras duplas e garantir formato correto
      const cleanPath = fullPath.replace(/\/+/g, '/').replace(/^\//, '')
      return `${s3BaseUrl}/${cleanPath}?preview=true&device=${device}&t=${Date.now()}`
    }

    // Fallback para o comportamento anterior (dev-env da loja)
    const baseUrl = AUTH_CONFIG.ENDPOINTS.DEV_ENV

    let themeFile = 'index.html'

    if (theme.filename) {
      themeFile = theme.filename
      if (!themeFile.endsWith('.html')) {
        themeFile += '.html'
      }
    } else {
      themeFile = 'index.html'
    }

    const previewUrl = `${baseUrl}/${userInfo.storeId}/${themeFile}?preview=true&device=${device}&t=${Date.now()}`

    return previewUrl
  }

  const changePreviewDevice = (device) => {
    if (['desktop', 'tablet', 'mobile'].includes(device)) {
      previewDevice.value = device
    }
  }

  const getPreviewDimensions = (device = previewDevice.value) => {
    const dimensions = {
      desktop: { width: '100%', height: '600px' },
      tablet: { width: '768px', height: '1024px' },
      mobile: { width: '375px', height: '812px' }
    }
    
    return dimensions[device] || dimensions.desktop
  }

  const getPreviewContainerClass = (device = previewDevice.value) => {
    const classes = {
      desktop: 'preview-desktop',
      tablet: 'preview-tablet', 
      mobile: 'preview-mobile'
    }
    
    return classes[device] || classes.desktop
  }

  const loadPreview = async (theme, device = previewDevice.value) => {
    isPreviewLoading.value = true
    
    try {
      await new Promise(resolve => setTimeout(resolve, 1000))
      
      const url = getDevicePreviewUrl(theme, device)
      return url
      
    } catch (error) {
      console.error('Erro ao carregar preview:', error)
      throw error
    } finally {
      isPreviewLoading.value = false
    }
  }

  const getMultiDevicePreview = (theme) => {
    return {
      desktop: getDevicePreviewUrl(theme, 'desktop'),
      tablet: getDevicePreviewUrl(theme, 'tablet'),
      mobile: getDevicePreviewUrl(theme, 'mobile')
    }
  }

  const isPreviewAvailable = (theme) => {
    if (!theme) return false

    return !!(theme.id || theme.name)
  }

  const getThumbnailUrl = (theme) => {
    if (!theme) return null

    if (theme.previewImage) {
      return theme.previewImage
    }

    const authStore = useAuthStore()
    const currentToken = authStore.getCurrentToken()
    const userInfo = tokenManager.extractUserInfo(currentToken)

    if (!userInfo?.storeId) {
      console.warn('StoreId not found in token for thumbnail')
      return null
    }

    // Se o tema tem template_s3_path, usar o S3 diretamente para thumbnail
    if (theme.templateS3Path || theme.template_s3_path) {
      const s3Path = theme.templateS3Path || theme.template_s3_path

      // Usar o mesmo formato que o backend (DevEnvFilter)
      const s3BaseUrl = 'https://iluria-bucket-dev.s3.us-east-1.amazonaws.com'

      // Garantir que o caminho termine com index.html
      let fullPath = s3Path
      if (!fullPath.endsWith('/')) {
        fullPath += '/'
      }
      fullPath += 'index.html'

      // Remover barras duplas e garantir formato correto
      const cleanPath = fullPath.replace(/\/+/g, '/').replace(/^\//, '')
      const finalUrl = `${s3BaseUrl}/${cleanPath}?thumbnail=true&w=400&h=300&t=${Date.now()}`

      return finalUrl
    }

    // Fallback para o comportamento anterior
    return `${AUTH_CONFIG.ENDPOINTS.DEV_ENV}/${userInfo.storeId}/${theme.filename}?thumbnail=true&w=400&h=300`
  }

  const generateScreenshot = async (theme, device = 'desktop') => {
    try {
      return getDevicePreviewUrl(theme, device)
      
    } catch (error) {
      console.error('Erro ao gerar screenshot:', error)
      return null
    }
  }

  const applyDeviceStyles = (device) => {
    const styles = {
      desktop: {
        width: '100%',
        height: '600px',
        border: 'none',
        borderRadius: '0'
      },
      tablet: {
        width: '768px',
        height: '1024px', 
        border: '8px solid #333',
        borderRadius: '20px',
        backgroundColor: '#333'
      },
      mobile: {
        width: '375px',
        height: '812px',
        border: '8px solid #333', 
        borderRadius: '25px',
        backgroundColor: '#333'
      }
    }
    
    return styles[device] || styles.desktop
  }

  const previewCache = ref(new Map())
  
  const getCachedPreview = (theme, device) => {
    const key = `${theme.id}-${device}`
    return previewCache.value.get(key)
  }
  
  const setCachedPreview = (theme, device, url) => {
    const key = `${theme.id}-${device}`
    previewCache.value.set(key, {
      url,
      timestamp: Date.now()
    })
  }
  
  const clearPreviewCache = () => {
    previewCache.value.clear()
  }

  const getResponsivePreviewConfig = () => {
    return {
      devices: [
        {
          name: 'desktop',
          label: 'Desktop',
          width: '100%',
          height: '600px',
          icon: 'desktop'
        },
        {
          name: 'tablet', 
          label: 'Tablet',
          width: '768px',
          height: '1024px',
          icon: 'tablet'
        },
        {
          name: 'mobile',
          label: 'Mobile', 
          width: '375px',
          height: '812px',
          icon: 'mobile'
        }
      ],
      defaultDevice: 'desktop'
    }
  }

  return {
    previewDevice,
    isPreviewLoading,
    
    getThemePreviewUrl,
    getDevicePreviewUrl,
    getMultiDevicePreview,
    getThumbnailUrl,
    
    changePreviewDevice,
    getPreviewDimensions,
    getPreviewContainerClass,
    applyDeviceStyles,
    
    loadPreview,
    generateScreenshot,
    isPreviewAvailable,
    
    getCachedPreview,
    setCachedPreview,
    clearPreviewCache,
    
    getResponsivePreviewConfig
  }
}