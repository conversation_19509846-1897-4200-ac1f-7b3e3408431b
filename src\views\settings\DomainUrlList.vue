<template>
  <div class="domain-list-container">
    <!-- Header Section -->
    <IluriaHeader
      :title="t('domainUrl.title')"
      :subtitle="t('domainUrl.subtitle')"
      :showAdd="true"
      :addText="t('domainUrl.new')"
      @add-click="newDomainUrl"
    />

    <!-- Alert para domínio principal -->
    <div v-if="hasDomainUrl() && !hasDomainUrlMain()" class="alert-warning">
      <div class="alert-content">
        <div class="alert-icon">
          <Alert02Icon />
        </div>
        <div class="alert-text">
          <h3 class="alert-title">{{ t('domainUrl.mainWarning') }}</h3>
          <p class="alert-description">{{ t('domainUrl.mainWarningDescription') }}</p>
        </div>
      </div>
    </div>

    <!-- Main Content -->
    <div class="table-wrapper">
      <IluriaDataTable
        v-if="isTableReady && tableColumns.length > 0 && !isLoading && Array.isArray(domainUrlList)"
        :key="tableKey"
        :value="domainUrlList"
        :columns="tableColumns"
        :loading="false"
        :showHeaderAllTime="false"
        class="domain-table"
      >
        <!-- Header Templates -->
        <template #header-name>
          <span class="column-header">{{ t('domainUrl.name') }}</span>
        </template>
        <template #header-status>
          <span class="column-header">{{ t('domainUrl.status') }}</span>
        </template>
        <template #header-sslStatus>
          <span class="column-header">{{ t('domainUrl.sslStatus') }}</span>
        </template>
        <template #header-actions>
          <span class="column-header">{{ t('domainUrl.actions') }}</span>
        </template>

        <!-- Domain Name Column -->
        <template #column-name="{ data }">
          <div v-if="data && data.name" class="domain-info">
            <div class="domain-header">
              <h3 class="domain-name">{{ data.name }}</h3>
              <div class="domain-url">
                <Globe02Icon />
                <span>https://{{ data.name }}</span>
              </div>
            </div>
            <div class="domain-badges">
              <span v-if="data.domainUrlType === 'MAIN'" class="badge badge-primary">
                <Crown03Icon />
                {{ t('domainUrl.main') }}
              </span>
              <span v-else class="badge badge-secondary">
                <ArrowRight01Icon />
                {{ t('domainUrl.redirection') }}
              </span>
            </div>
          </div>
          <div v-else class="empty-cell">-</div>
        </template>
        
        <!-- Status Column -->
        <template #column-status="{ data }">
          <div v-if="data && data.hasOwnProperty('status')" class="status-container">
            <span class="status-badge" :class="getStatusClass(data.status)">
              <div class="status-indicator"></div>
              {{ data.status === 'CONNECTED' ? t('domainUrl.connected') : t('domainUrl.notConnected') }}
            </span>
          </div>
          <div v-else class="empty-cell">-</div>
        </template>

        <!-- SSL Status Column -->
        <template #column-sslStatus="{ data }">
          <div v-if="data && data.hasOwnProperty('sslStatus')" class="ssl-container">
            <span class="ssl-badge" :class="getSslStatusClass(data.sslStatus)">
              <Loading03Icon 
                v-if="(data.sslStatus || '').toLowerCase() === 'pending'" 
                class="ssl-icon ssl-spinner-icon" 
              />
              <SecurityCheckIcon 
                v-else-if="(data.sslStatus || '').toLowerCase() === 'active'" 
                class="ssl-icon" 
              />
              <Cancel01Icon 
                v-else-if="(data.sslStatus || '').toLowerCase() === 'failed'" 
                class="ssl-icon" 
              />
              <Clock01Icon 
                v-else 
                class="ssl-icon" 
              />
              <span class="ssl-text">{{ getSslStatusText(data.sslStatus) }}</span>
            </span>
          </div>
          <div v-else class="empty-cell">-</div>
        </template>
        
        <!-- Actions Column -->
        <template #column-actions="{ data }">
          <div v-if="data && data.id" class="action-buttons">
            <IluriaButton 
              color="text-green-600" 
              size="small" 
              :hugeIcon="CheckmarkCircle02Icon" 
              @click.prevent="validateDomainUrl(data)"
              :title="t('domainUrl.validate')"
              class="btn-action btn-validate"
            />
            <IluriaButton 
              color="text-primary" 
              size="small" 
              :hugeIcon="PencilEdit01Icon" 
              @click.prevent="editDomainUrl(data)"
              :title="t('edit')"
              class="btn-action btn-edit"
            />
            <IluriaButton 
              color="text-danger" 
              size="small" 
              :hugeIcon="Delete01Icon" 
              @click.prevent="confirmDelete(data)"
              :title="t('delete')"
              class="btn-action btn-delete"
            />
          </div>
          <div v-else class="empty-cell">-</div>
        </template>
        
        <!-- Empty State -->
        <template #empty>
          <div class="empty-state">
            <div class="empty-icon">
              <WebDesignIcon />
            </div>
            <h3 class="empty-title">{{ t('domainUrl.empty') }}</h3>
            <p class="empty-description">{{ t('domainUrl.emptyDescription') }}</p>
            <IluriaButton @click="newDomainUrl" :hugeIcon="PlusSignSquareIcon" color="dark" class="mt-4">
              {{ t('domainUrl.new') }}
            </IluriaButton>
          </div>
        </template>

        <!-- Loading State -->
        <template #loading>
          <div class="loading-state">
            <div class="loading-spinner"></div>
            <span>{{ t('domainUrl.loadingDomains') }}</span>
          </div>
        </template>
      </IluriaDataTable>
      
      <!-- Loading while initializing table -->
      <div v-else-if="isLoading || !isTableReady" class="table-initializing">
        <div class="loading-state">
          <div class="loading-spinner"></div>
          <span>{{ t('domainUrl.loadingDomains') }}</span>
        </div>
      </div>
    </div>

    <!-- Confirmation Dialog -->
    <IluriaConfirmationModal
      :is-visible="showConfirmModal"
      :title="confirmModalTitle"
      :message="confirmModalMessage"
      :confirm-text="confirmModalConfirmText"
      :cancel-text="confirmModalCancelText"
      :type="confirmModalType"
      @confirm="handleConfirm"
      @cancel="handleCancel"
    />
  </div>
</template>

<script setup>
import { useI18n } from 'vue-i18n'
import { useRouter } from 'vue-router'
import { ref, onMounted, computed, watch, onUnmounted, nextTick } from 'vue'
import { useToast } from '@/services/toast.service'
import domainsService from '@/services/domain.service'
import IluriaButton from '@/components/iluria/IluriaButton.vue'
import IluriaHeader from '@/components/iluria/IluriaHeader.vue'
import IluriaConfirmationModal from '@/components/iluria/modal/IluriaConfirmationModal.vue'
import IluriaDataTable from '@/components/iluria/IluriaDataTable.vue'
import { 
  Delete01Icon, 
  PencilEdit01Icon, 
  CheckmarkCircle02Icon,
  PlusSignSquareIcon,
  Alert02Icon,
  WebDesignIcon,
  Loading03Icon,
  Globe02Icon,
  Crown03Icon,
  ArrowRight01Icon,
  SecurityCheckIcon,
  Cancel01Icon,
  Clock01Icon
} from '@hugeicons-pro/core-stroke-rounded'

const router = useRouter()
const toast = useToast()
const { t } = useI18n()

const domainUrlList = ref([])

// Modal state
const showConfirmModal = ref(false)
const confirmModalTitle = ref('')
const confirmModalMessage = ref('')
const confirmModalConfirmText = ref('Confirmar')
const confirmModalCancelText = ref('Cancelar')
const confirmModalType = ref('info')
const confirmCallback = ref(null)
const isLoading = ref(true)
const autoRefreshInterval = ref(null)
const isTableReady = ref(false)
const tableKey = ref(0)

// Computed stats
const connectedDomainsCount = computed(() => {
  if (!Array.isArray(domainUrlList.value)) return 0;
  return domainUrlList.value.filter(d => 
    d && typeof d === 'object' && d.status === 'CONNECTED'
  ).length;
})

const sslActiveCount = computed(() => {
  if (!Array.isArray(domainUrlList.value)) return 0;
  return domainUrlList.value.filter(d => 
    d && typeof d === 'object' && d.sslStatus === 'ACTIVE'
  ).length;
})

const sslPendingCount = computed(() => {
  if (!Array.isArray(domainUrlList.value)) return 0;
  return domainUrlList.value.filter(d => 
    d && typeof d === 'object' && d.sslStatus === 'PENDING'
  ).length;
})

// Watch para detectar SSL pendente e mostrar toast
watch(sslPendingCount, (newCount, oldCount) => {
  // Verificar se os valores são válidos e não undefined
  if (typeof newCount !== 'number' || typeof oldCount !== 'number') {
    return;
  }
  
  if (newCount > oldCount && newCount > 0) {
    const message = newCount > 1 
      ? `🔐 ${newCount} ${t('domainUrl.ssl.processingMultipleMessage')}...`
      : `🔐 ${newCount} ${t('domainUrl.ssl.processingMessage')}...`
    
    toast.showInfo(message, t('domainUrl.ssl.processing'))
  }
  
  // Auto-refresh quando há certificados pendentes
  if (newCount > 0 && !autoRefreshInterval.value) {
    startAutoRefresh()
  } else if (newCount === 0 && autoRefreshInterval.value) {
    stopAutoRefresh()
  }
})

// Watch para detectar mudanças nos domínios e mostrar toasts específicos para SSL
watch(domainUrlList, (newList, oldList) => {
  // Verificar se as listas são válidas antes de processar
  if (!Array.isArray(newList) || !Array.isArray(oldList) || 
      oldList.length === 0 || newList.length === 0) {
    return;
  }
  
  if (oldList && oldList.length > 0 && newList && newList.length > 0) {
    // Detectar novos certificados SSL sendo processados
    const newPendingDomains = newList.filter(domain => 
      domain && (domain.sslStatus || '').toLowerCase() === 'pending' &&
      !oldList.find(oldDomain => 
        oldDomain && oldDomain.id === domain.id && 
        (oldDomain.sslStatus || '').toLowerCase() === 'pending'
      )
    )

    if (newPendingDomains.length > 0) {
      newPendingDomains.forEach(domain => {
        toast.showInfo(
          `🔐 ${t('domainUrl.ssl.generatingMessage')} ${domain.name}...`,
          t('domainUrl.ssl.certificate'),
          8000
        )
      })
    }

    // Detectar certificados SSL completados
    const completedDomains = newList.filter(domain => 
      domain && (domain.sslStatus || '').toLowerCase() === 'active' &&
      oldList.find(oldDomain => 
        oldDomain && oldDomain.id === domain.id && 
        (oldDomain.sslStatus || '').toLowerCase() === 'pending'
      )
    )

    if (completedDomains.length > 0) {
      completedDomains.forEach(domain => {
        toast.showSuccess(
          `✅ ${t('domainUrl.ssl.successMessage')} ${domain.name}`,
          t('domainUrl.ssl.active'),
          5000
        )
      })
    }

    // Detectar certificados SSL com falha
    const failedDomains = newList.filter(domain => 
      domain && (domain.sslStatus || '').toLowerCase() === 'failed' &&
      oldList.find(oldDomain => 
        oldDomain && oldDomain.id === domain.id && 
        (oldDomain.sslStatus || '').toLowerCase() === 'pending'
      )
    )

    if (failedDomains.length > 0) {
      failedDomains.forEach(domain => {
        toast.showError(
          `❌ ${t('domainUrl.ssl.failedMessage')} ${domain.name}`,
          t('domainUrl.ssl.error'),
          10000
        )
      })
    }
  }
}, { deep: true })

const tableColumns = ref([])

const initializeTableColumns = () => {
  if (tableColumns.value.length > 0) return; // Evita reinicialização desnecessária
  
  try {
    tableColumns.value = [
      {
        field: 'name',
        sortable: false,
        headerClass: 'col-name',
        class: 'col-name',
        headerStyle: 'width: 40%; min-width: 320px; text-align: left; font-weight: 600; padding-left: 70px;',
        bodyStyle: 'width: 40%; min-width: 320px; text-align: left; padding: 20px 16px;'
      },
      {
        field: 'status',
        sortable: false,
        headerClass: 'col-status',
        class: 'col-status',
        headerStyle: 'width: 20%; min-width: 160px; text-align: center; font-weight: 600; padding: 12px 90px;',
        bodyStyle: 'width: 20%; min-width: 160px; text-align: center; padding: 20px 16px;'
      },
      {
        field: 'sslStatus',
        sortable: false,
        headerClass: 'col-ssl',
        class: 'col-ssl',
        headerStyle: 'width: 20%; min-width: 180px; text-align: center; font-weight: 600; padding: 12px 70px;',
        bodyStyle: 'width: 20%; min-width: 180px; text-align: center; padding: 20px 16px;'
      },
      {
        field: 'actions',
        sortable: false,
        headerClass: 'col-actions',
        class: 'col-actions',
        headerStyle: 'width: 20%; min-width: 200px; text-align: center; font-weight: 600; padding: 12px 90px;',
        bodyStyle: 'width: 20%; min-width: 200px; text-align: center; padding: 20px 16px;'
      }
    ]

    nextTick(() => {
      isTableReady.value = true
    })
  } catch (error) {
    console.error('Error initializing table columns:', error);
    isTableReady.value = false;
  }
}

function editDomainUrl(domainUrl) {
  if (!domainUrl || !domainUrl.id) return;
  router.push(`/settings/domain-manager/${domainUrl.id}`)
}

function newDomainUrl() {
  router.push('/settings/domain-manager/form')
}

// Modal control functions
const showConfirmDanger = (message, title, onConfirm) => {
  confirmModalMessage.value = message
  confirmModalTitle.value = title
  confirmModalConfirmText.value = t('delete')
  confirmModalCancelText.value = t('cancel')
  confirmModalType.value = 'error'
  confirmCallback.value = onConfirm
  showConfirmModal.value = true
}

const handleConfirm = () => {
  if (confirmCallback.value) {
    confirmCallback.value()
  }
  showConfirmModal.value = false
}

const handleCancel = () => {
  showConfirmModal.value = false
}

function confirmDelete(domainUrl) {
  if (!domainUrl || !domainUrl.id || !domainUrl.name) return;
  showConfirmDanger(
    `${t('domainUrl.confirmDeleteMessage')} ${domainUrl.name}?`,
    t('domainUrl.confirmDeleteTitle'),
    () => deleteDomainUrl(domainUrl.id)
  );
}

function deleteDomainUrl(id) {
  domainsService.deleteDomainUrl(id)
    .then(() => {
      loadDomainUrlList();
      toast.showSuccess(t('domainUrl.deleteSuccess'));
    })
    .catch((error) => {
      toast.showError(t('domainUrl.deleteError'));
    });
}

function validateDomainUrl(domainUrl) {
  if (!domainUrl || !domainUrl.id) return;
  router.push(`/settings/domain-manager/validate/${domainUrl.id}`);
}

function hasDomainUrl() {
  return Array.isArray(domainUrlList.value) && domainUrlList.value.length > 0;
}

function hasDomainUrlMain() {
  if (!Array.isArray(domainUrlList.value)) {
    return false;
  }
  let mainDomainUrl = domainUrlList.value.find(domainUrl => 
    domainUrl && typeof domainUrl === 'object' && domainUrl.domainUrlType === 'MAIN'
  );
  return !!mainDomainUrl;
}

function getStatusClass(status) {
  if (!status || typeof status !== 'string') {
    return { 'status-disconnected': true };
  }
  return {
    'status-connected': status === 'CONNECTED',
    'status-disconnected': status !== 'CONNECTED'
  };
}

function getSslStatusClass(sslStatus) {
  if (!sslStatus && sslStatus !== '') {
    return { 'ssl-none': true };
  }
  
  const status = typeof sslStatus === 'string' ? sslStatus.toLowerCase() : '';
  
  return {
    'ssl-pending': status === 'pending',
    'ssl-active': status === 'active', 
    'ssl-failed': status === 'failed',
    'ssl-none': !status || status === 'none' || status === 'inativo'
  };
}

function getSslStatusText(sslStatus) {
  if (!sslStatus && sslStatus !== '') {
    return t('domainUrl.ssl.inactive');
  }
  
  const status = typeof sslStatus === 'string' ? sslStatus.toLowerCase() : '';
  
  switch(status) {
    case 'pending': return t('domainUrl.ssl.pending');
    case 'active': return t('domainUrl.ssl.activeStatus');
    case 'failed': return t('domainUrl.ssl.failed');
    default: return t('domainUrl.ssl.inactive');
  }
}

async function loadDomainUrlList(isAutoRefresh = false) {
  try {
    if (!isAutoRefresh) {
      isLoading.value = true;
    }
    
    const domains = await domainsService.getDomainUrlList();
    
    // Validar que domains é um array e filtrar objetos inválidos
    if (Array.isArray(domains)) {
      domainUrlList.value = domains.filter(domain => 
        domain && typeof domain === 'object' && domain.hasOwnProperty('id')
      );
    } else {
      domainUrlList.value = [];
    }
    
  } catch(error) {
    console.error('Error loading domain list:', error);
    domainUrlList.value = [];
    if (!isAutoRefresh) {
      toast.showError(t('domainUrl.loadListError'));
    }
  } finally {
    if (!isAutoRefresh) {
      isLoading.value = false;
    }
  }
}

function startAutoRefresh() {
  autoRefreshInterval.value = setInterval(() => loadDomainUrlList(true), 30000); // 30 segundos
}

function stopAutoRefresh() {
  if (autoRefreshInterval.value) {
    clearInterval(autoRefreshInterval.value);
    autoRefreshInterval.value = null;
  }
}

onMounted(async () => {
  await nextTick()
  
  initializeTableColumns()
  await loadDomainUrlList()
  
  await nextTick()
  tableKey.value++
})

onUnmounted(() => {
  stopAutoRefresh()
  isTableReady.value = false
})
</script>

<style scoped>
.domain-list-container {
  padding: 24px;
  max-width: 1400px;
  margin: 0 auto;
  background: var(--iluria-color-bg);
  color: var(--iluria-color-text-primary);
  min-height: 100vh;
  width: 100%;
}



/* Stats Section */
.stats-section {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 20px;
  margin-bottom: 32px;
}

.stat-card {
  background: var(--iluria-color-white);
  border: 1px solid var(--iluria-color-border);
  border-radius: 12px;
  padding: 20px;
  display: flex;
  align-items: center;
  gap: 16px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
}

.stat-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
}

.stat-icon {
  width: 48px;
  height: 48px;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.stat-icon.connected {
  background: var(--iluria-color-success-bg);
  color: var(--iluria-color-success);
}

.stat-icon.ssl-active {
  background: var(--iluria-color-primary-bg);
  color: var(--iluria-color-primary);
}

.stat-icon.ssl-pending {
  background: var(--iluria-color-warning-bg);
  color: var(--iluria-color-warning);
}

.stat-number {
  font-size: 24px;
  font-weight: 700;
  color: var(--iluria-color-text-primary);
  line-height: 1;
}

.stat-label {
  font-size: 14px;
  color: var(--iluria-color-text-secondary);
  margin-top: 4px;
}

/* Alert Warning */
.alert-warning {
  margin-bottom: 24px;
  padding: 16px 20px;
  background: var(--iluria-color-warning-bg);
  border: 1px solid var(--iluria-color-warning-border);
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.alert-content {
  display: flex;
  align-items: flex-start;
  gap: 12px;
}

.alert-icon {
  color: var(--iluria-color-warning);
  flex-shrink: 0;
  margin-top: 2px;
}

.alert-icon svg {
  width: 20px;
  height: 20px;
}

.alert-title {
  font-size: 14px;
  font-weight: 600;
  color: var(--iluria-color-text-primary);
  margin: 0 0 4px 0;
}

.alert-description {
  font-size: 13px;
  color: var(--iluria-color-text-secondary);
  margin: 0;
}

/* Table Styles */
.table-wrapper {
  margin-bottom: 24px;
  background: var(--iluria-color-white);
  border: 1px solid var(--iluria-color-border);
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}



/* Table Header Styling */
.table-wrapper :deep(.p-datatable-thead tr th) {
  padding: 12px 16px;
  background: var(--iluria-color-bg);
  color: var(--iluria-color-text-primary);
  font-weight: 600;
  font-size: 14px;
  border-bottom: 2px solid var(--iluria-color-border);
  border-right: 1px solid var(--iluria-color-border-light);
  text-align: center;
}

.table-wrapper :deep(.p-datatable-thead tr th:first-child) {
  text-align: left;
}

.table-wrapper :deep(.p-datatable-thead tr th:last-child) {
  border-right: none;
}

/* Remove sorting indicators */
.table-wrapper :deep(.p-sortable-column .p-sortable-column-icon) {
  display: none !important;
}

.table-wrapper :deep(.p-sortable-column-badge) {
  display: none !important;
}

/* Table Body Styling */
.table-wrapper :deep(.p-datatable-tbody tr td) {
  padding: 20px 16px;
  border-bottom: 1px solid var(--iluria-color-border-light);
  vertical-align: middle;
  border-right: 1px solid var(--iluria-color-border-light);
}

.table-wrapper :deep(.p-datatable-tbody tr td:last-child) {
  border-right: none;
}

.table-wrapper :deep(.p-datatable-tbody tr:hover) {
  background: var(--iluria-color-bg-hover);
}

.table-wrapper :deep(.p-datatable-tbody tr:last-child td) {
  border-bottom: none;
}

/* Domain Info */
.domain-info {
  min-width: 0;
  padding: 8px 0;
  width: 100%;
}

.domain-header {
  margin-bottom: 12px;
}

.domain-name {
  font-size: 16px;
  font-weight: 600;
  color: var(--iluria-color-text-primary);
  margin: 0 0 8px 0;
  line-height: 1.4;
  word-wrap: break-word;
  overflow-wrap: break-word;
}

.domain-url {
  display: flex;
  align-items: center;
  gap: 6px;
  font-size: 12px;
  color: var(--iluria-color-text-secondary);
  word-wrap: break-word;
  overflow-wrap: break-word;
}

.domain-url svg {
  width: 14px;
  height: 14px;
  flex-shrink: 0;
}

.domain-badges {
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
}

.badge {
  font-size: 11px;
  font-weight: 600;
  padding: 6px 10px;
  border-radius: 8px;
  text-transform: uppercase;
  letter-spacing: 0.025em;
  display: flex;
  align-items: center;
  gap: 4px;
  white-space: nowrap;
}

.badge svg {
  width: 12px;
  height: 12px;
}

.badge-primary {
  background: var(--iluria-color-primary-bg);
  color: var(--iluria-color-primary);
}

.badge-secondary {
  background: var(--iluria-color-secondary-bg);
  color: var(--iluria-color-text-secondary);
}

/* Status Badge */
.status-container {
  display: flex;
  justify-content: center;
  width: 100%;
}

.status-badge {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 12px;
  font-weight: 600;
  padding: 10px 16px;
  border-radius: 20px;
  transition: all 0.3s ease;
  min-width: 120px;
  justify-content: center;
  white-space: nowrap;
}

.status-indicator {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  animation: pulse 2s infinite;
  flex-shrink: 0;
}

.status-connected {
  background: var(--iluria-color-success-bg);
  color: var(--iluria-color-success);
  border: 1px solid var(--iluria-color-success-border);
}

.status-connected .status-indicator {
  background: var(--iluria-color-success);
}

.status-disconnected {
  background: var(--iluria-color-error-bg);
  color: var(--iluria-color-error);
  border: 1px solid var(--iluria-color-error-border);
}

.status-disconnected .status-indicator {
  background: var(--iluria-color-error);
}

/* SSL badge */
.ssl-container {
  display: flex;
  justify-content: center;
  width: 100%;
}

.ssl-badge {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 10px 16px;
  border-radius: 20px;
  font-size: 12px;
  font-weight: 600;
  text-transform: uppercase;
  transition: all 0.3s ease;
  min-width: 140px;
  justify-content: center;
  white-space: nowrap;
}

.ssl-icon {
  width: 14px;
  height: 14px;
  flex-shrink: 0;
}

.ssl-spinner-icon {
  color: var(--iluria-color-warning);
  animation: spin 1.5s linear infinite;
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

.ssl-pending {
  background: var(--iluria-color-warning-bg);
  color: var(--iluria-color-warning);
  border: 1px solid var(--iluria-color-warning-border);
}

.ssl-pending .ssl-text {
  font-weight: 600;
}

.ssl-active {
  background: var(--iluria-color-success-bg);
  color: var(--iluria-color-success);
  border: 1px solid var(--iluria-color-success-border);
}

.ssl-failed {
  background: var(--iluria-color-error-bg);
  color: var(--iluria-color-error);
  border: 1px solid var(--iluria-color-error-border);
}

.ssl-none {
  background: var(--iluria-color-secondary-bg);
  color: var(--iluria-color-text-secondary);
  border: 1px solid var(--iluria-color-border);
}

.ssl-spinner {
  width: 12px;
  height: 12px;
  border: 2px solid transparent;
  border-top: 2px solid currentColor;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

/* Action Buttons */
.action-buttons {
  display: flex;
  gap: 8px;
  justify-content: center;
  align-items: center;
  width: 100%;
  padding: 4px 0;
}

.btn-action {
  transition: all 0.3s ease;
  border-radius: 8px;
  width: 36px;
  height: 36px;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

.btn-action:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.btn-validate:hover {
  background: var(--iluria-color-success-bg);
  color: var(--iluria-color-success);
}

.btn-edit:hover {
  background: var(--iluria-color-primary-bg);
  color: var(--iluria-color-primary);
}

.btn-delete:hover {
  background: var(--iluria-color-error-bg);
  color: var(--iluria-color-error);
}

/* Empty State */
.empty-state {
  text-align: center;
  padding: 64px 24px;
}

.empty-icon {
  font-size: 64px;
  color: var(--iluria-color-text-disabled);
  margin-bottom: 24px;
}

.empty-icon svg {
  width: 64px;
  height: 64px;
}

.empty-title {
  font-size: 20px;
  font-weight: 600;
  color: var(--iluria-color-text-primary);
  margin: 0 0 12px 0;
}

.empty-description {
  font-size: 16px;
  color: var(--iluria-color-text-secondary);
  margin: 0 0 24px 0;
}

/* Loading State */
.loading-state {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 12px;
  padding: 48px;
  color: var(--iluria-color-text-secondary);
  font-size: 16px;
}

.table-initializing {
  background: var(--iluria-color-white);
  border: 1px solid var(--iluria-color-border);
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.loading-spinner {
  width: 24px;
  height: 24px;
  border: 3px solid var(--iluria-color-border);
  border-top: 3px solid var(--iluria-color-primary);
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

/* Animations */
@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

@keyframes pulse {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.5; }
}

/* Empty cell fallback */
.empty-cell {
  color: var(--iluria-color-text-disabled);
  text-align: center;
  font-size: 14px;
  padding: 8px;
}

/* Utility classes */
.mt-4 {
  margin-top: 1rem;
}

/* Table Header Responsive */
@media (max-width: 768px) {
  .table-wrapper :deep(.p-datatable-thead tr th) {
    padding: 12px 8px;
    font-size: 12px;
  }
  
  .table-wrapper :deep(.p-datatable-tbody tr td) {
    padding: 12px 8px;
  }
}

/* Responsive */
@media (max-width: 768px) {
  .domain-list-container {
    padding: 16px;
  }
  

  
  .stats-section {
    grid-template-columns: 1fr;
    gap: 16px;
    margin-bottom: 24px;
  }
  
  .stat-card {
    padding: 16px;
    gap: 12px;
  }
  
  .stat-icon {
    width: 40px;
    height: 40px;
  }
  
  .stat-number {
    font-size: 20px;
  }
  
  .alert-warning {
    padding: 12px 16px;
  }
  
  .alert-content {
    flex-direction: column;
    gap: 8px;
  }
  
  .alert-icon {
    margin-top: 0;
  }
}

@media (max-width: 640px) {

  
  .action-buttons {
    gap: 4px;
  }
}

@media (max-width: 480px) {
  .domain-list-container {
    padding: 12px;
  }
  

  
  .empty-state {
    padding: 48px 16px;
  }
  
  .empty-icon svg {
    width: 48px;
    height: 48px;
  }
  
  .empty-title {
    font-size: 18px;
  }
  
  .empty-description {
    font-size: 14px;
  }
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .domain-list-container {
    padding: 16px;
  }
  

  
  .table-wrapper :deep(.p-datatable-tbody tr td:nth-child(1)) {
    min-width: 200px;
  }
  
  .domain-name {
    font-size: 14px;
  }
  
  .action-buttons {
    gap: 4px;
  }
  
  .btn-action {
    width: 32px;
    height: 32px;
  }
}


</style>
