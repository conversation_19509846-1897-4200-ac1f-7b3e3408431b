{"welcomeBack": "Boas-vindas de volta", "createStore": "<PERSON><PERSON><PERSON> loja", "manageAccount": "Gerenciar conta", "switchAccount": "Alternar conta", "logout": "<PERSON><PERSON>", "loadingStores": "Carregando lojas...", "noStores": "<PERSON>enhuma loja encontrada", "noStoresDescription": "Você ainda não possui nenhuma loja. Crie sua primeira loja para começar.", "createFirstStore": "Criar primeira loja", "createNewStore": "Criar nova loja", "storeName": "Nome da loja", "storeNamePlaceholder": "Digite o nome da sua loja", "storeUrl": "URL da loja", "storeUrlPlaceholder": "minha-loja", "storeUrlHelp": "Esta será a URL da sua loja: minha-loja.iluria.com", "storeNameRequired": "O nome da loja é obrigatório", "storeCreatedSuccess": "Loja criada com sucesso!", "storeCreationError": "Erro ao criar loja. Tente novamente.", "loadStoresError": "Erro ao carregar lojas. Tente novamente.", "enteringStore": "Entrando na loja", "switchAccountNotImplemented": "Funcionalidade ainda não implementada", "initializingStore": "Configurando sua loja", "settingUpEverything": "Estamos preparando tudo para você", "redirectingToStore": "Redirecionando para sua loja...", "initializationCompleted": "Loja configurada com sucesso!", "initializationError": "Erro na configuração", "initializationSteps": {"starting": "Iniciando configura<PERSON>", "creatingDomain": "Configu<PERSON><PERSON>", "creatingCategories": "<PERSON><PERSON><PERSON> categorias", "creatingProducts": "<PERSON><PERSON><PERSON> produ<PERSON>", "creatingEmailNotificationSettings": "Criando configurações de notificação por email", "completed": "Configuração concluída"}}