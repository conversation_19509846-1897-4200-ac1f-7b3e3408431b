<template>
  <div class="email-content">
    <!-- Alterar Email -->
    <ViewContainer
      title="Email de Contato"
      subtitle="Gerencie seu email de contato principal"
      :icon="MailAtSign01Icon"
      iconColor="blue"
    >
      <div class="form-section">
        <div class="form-group">
          <label for="currentEmail" class="form-label">Email Atual</label>
          <input
            id="currentEmail"
            v-model="currentEmail"
            type="email"
            class="form-input"
            autocomplete="off"
            autocapitalize="off"
            autocorrect="off"
            spellcheck="false"
            disabled
          />
        </div>

        <div class="form-group">
          <label for="newEmail" class="form-label">Novo Email</label>
          <input
            id="newEmail"
            v-model="newEmail"
            type="text"
            class="form-input"
            placeholder="Digite seu novo email"
            autocomplete="nope"
            autocapitalize="off"
            autocorrect="off"
            spellcheck="false"
            :class="{ 'error': emailError }"
          />
          <span v-if="emailError" class="error-message">{{ emailError }}</span>
        </div>

        <div class="form-group">
          <label for="currentPassword" class="form-label">Senha Atual</label>
          <input
            id="currentPassword"
            v-model="currentPassword"
            type="password"
            class="form-input"
            placeholder="Digite sua senha atual para confirmar"
            autocomplete="new-password"
            autocapitalize="off"
            autocorrect="off"
            spellcheck="false"
            :class="{ 'error': passwordError }"
          />
          <span v-if="passwordError" class="error-message">{{ passwordError }}</span>
        </div>

        <div class="form-actions">
          <IluriaButton
            @click="changeEmail"
            variant="solid"
            color="primary"
            :disabled="isChangingEmail || !newEmail || !currentPassword"
            :loading="isChangingEmail"
            :hugeIcon="MailAtSign01Icon"
          >
            Alterar Email
          </IluriaButton>
        </div>
      </div>
    </ViewContainer>

    <!-- Alterar Celular -->
    <ViewContainer
      title="Celular de Contato"
      subtitle="Gerencie seu número de celular de contato"
      :icon="SmartPhone02Icon"
      iconColor="green"
    >
      <div class="form-section">
        <div class="form-group">
          <label for="currentPhone" class="form-label">Celular Atual</label>
          <input
            id="currentPhone"
            v-model="currentPhone"
            type="text"
            class="form-input"
            autocomplete="off"
            autocapitalize="off"
            autocorrect="off"
            spellcheck="false"
            disabled
          />
        </div>

        <div class="form-group">
          <label for="newPhone" class="form-label">Novo Celular</label>
          <input
            id="newPhone"
            v-model="newPhone"
            type="text"
            class="form-input"
            placeholder="(11) 99999-9999"
            autocomplete="nope"
            autocapitalize="off"
            autocorrect="off"
            spellcheck="false"
            :class="{ 'error': phoneError }"
          />
          <span v-if="phoneError" class="error-message">{{ phoneError }}</span>
        </div>

        <div class="form-group">
          <label for="passwordForPhone" class="form-label">Senha Atual</label>
          <input
            id="passwordForPhone"
            v-model="passwordForPhone"
            type="password"
            class="form-input"
            placeholder="Digite sua senha atual para confirmar"
            autocomplete="new-password"
            autocapitalize="off"
            autocorrect="off"
            spellcheck="false"
            :class="{ 'error': passwordForPhoneError }"
          />
          <span v-if="passwordForPhoneError" class="error-message">{{ passwordForPhoneError }}</span>
        </div>

        <div class="form-actions">
          <IluriaButton
            @click="changePhone"
            variant="solid"
            color="primary"
            :disabled="isChangingPhone || !newPhone || !passwordForPhone"
            :loading="isChangingPhone"
            :hugeIcon="SmartPhone02Icon"
          >
            Alterar Celular
          </IluriaButton>
        </div>
      </div>
    </ViewContainer>

  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { useToast } from '@/services/toast.service'
import ViewContainer from '@/components/layout/ViewContainer.vue'
import IluriaButton from '@/components/iluria/IluriaButton.vue'
import { HugeiconsIcon } from '@hugeicons/vue'
import {
  MailAtSign01Icon,
  SmartPhone02Icon
} from '@hugeicons-pro/core-stroke-standard'

// Composables
const toast = useToast()

// Email State
const currentEmail = ref('<EMAIL>')
const newEmail = ref('')
const currentPassword = ref('')
const emailError = ref('')
const passwordError = ref('')
const isChangingEmail = ref(false)

// Phone State
const currentPhone = ref('(11) 99999-9999')
const newPhone = ref('')
const passwordForPhone = ref('')
const phoneError = ref('')
const passwordForPhoneError = ref('')
const isChangingPhone = ref(false)

// Methods
const validateEmail = (email) => {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
  return emailRegex.test(email)
}

const validatePhone = (phone) => {
  const phoneRegex = /^\(\d{2}\)\s\d{4,5}-\d{4}$/
  return phoneRegex.test(phone)
}

const changeEmail = async () => {
  // Reset errors
  emailError.value = ''
  passwordError.value = ''

  // Validate new email
  if (!validateEmail(newEmail.value)) {
    emailError.value = 'Email inválido'
    return
  }

  if (newEmail.value === currentEmail.value) {
    emailError.value = 'O novo email deve ser diferente do atual'
    return
  }

  if (!currentPassword.value) {
    passwordError.value = 'Senha atual é obrigatória'
    return
  }

  isChangingEmail.value = true

  try {
    // Simulate API call
    await new Promise(resolve => setTimeout(resolve, 2000))

    currentEmail.value = newEmail.value

    toast.addToast('Email alterado com sucesso!', 'success')

    // Reset form
    newEmail.value = ''
    currentPassword.value = ''
  } catch (error) {
    toast.addToast('Erro ao alterar email', 'error')
  } finally {
    isChangingEmail.value = false
  }
}

const changePhone = async () => {
  // Reset errors
  phoneError.value = ''
  passwordForPhoneError.value = ''

  // Validate new phone
  if (!validatePhone(newPhone.value)) {
    phoneError.value = 'Formato de celular inválido. Use: (11) 99999-9999'
    return
  }

  if (newPhone.value === currentPhone.value) {
    phoneError.value = 'O novo celular deve ser diferente do atual'
    return
  }

  if (!passwordForPhone.value) {
    passwordForPhoneError.value = 'Senha atual é obrigatória'
    return
  }

  isChangingPhone.value = true

  try {
    // Simulate API call
    await new Promise(resolve => setTimeout(resolve, 2000))

    currentPhone.value = newPhone.value

    toast.addToast('Celular alterado com sucesso!', 'success')

    // Reset form
    newPhone.value = ''
    passwordForPhone.value = ''
  } catch (error) {
    toast.addToast('Erro ao alterar celular', 'error')
  } finally {
    isChangingPhone.value = false
  }
}

// Lifecycle
onMounted(() => {
  // Load user email settings
  // This would typically come from an API call
})
</script>

<style scoped>
.email-content {
  width: 100%;
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.form-section {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.form-group {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.form-label {
  font-size: 14px;
  font-weight: 500;
  color: var(--iluria-color-text-primary);
}

.form-input {
  padding: 0.75rem;
  border: 1px solid var(--iluria-color-border);
  border-radius: 6px;
  background: var(--iluria-color-surface-primary);
  color: var(--iluria-color-text-primary);
  font-size: 14px;
  transition: border-color 0.2s ease;
}

.form-input:focus {
  outline: none;
  border-color: var(--iluria-color-primary);
}

.form-input:disabled {
  background: var(--iluria-color-surface-secondary);
  color: var(--iluria-color-text-secondary);
  cursor: not-allowed;
}

.form-input.error {
  border-color: var(--iluria-color-error);
}

.error-message {
  font-size: 12px;
  color: var(--iluria-color-error);
}

.form-actions {
  display: flex;
  justify-content: flex-end;
  padding-top: 1rem;
}

/* Responsive */
@media (max-width: 768px) {
  .form-actions {
    justify-content: stretch;
  }

  .form-actions button {
    width: 100%;
  }
}
</style>
