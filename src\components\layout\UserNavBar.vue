<template>
  <div class="user-navbar">
    <div class="logo-section">
      <!-- Mobile: Hamburger Men<PERSON> - só mostrar quando necessário -->
      <button 
        v-if="showMobileMenu"
        @click="toggleMobileMenu" 
        class="mobile-menu-button"
        :class="{ 'menu-open': mobileMenuOpen }"
      >
        <Menu v-if="!mobileMenuOpen" class="w-6 h-6" />
        <X v-else class="w-6 h-6" />
      </button>
      
      <div class="logo" @click="navigateToStores" style="cursor: pointer;">
        <IluriaLogo height="2rem" width="auto" class="logo-themed" />
      </div>
    </div>

    <div class="navbar-actions">
      <!-- Notifications -->
      <NotificationBell type="user" />
      
      <!-- Theme Selector -->
      <ThemeSelector />

      <!-- User Menu -->
      <UserMenu :type="type" />
    </div>
  </div>
</template>

<script setup>
import { useRouter } from 'vue-router'
import { Menu, X } from 'lucide-vue-next'
import UserMenu from '@/components/layout/UserMenu.vue'
import ThemeSelector from '@/components/layout/ThemeSelector.vue'
import IluriaLogo from '@/components/iluria/IluriaLogo.vue'
import NotificationBell from '@/components/layout/NotificationBell.vue'
import { useMobileUserMenu } from '@/composables/useMobileUserMenu'

const router = useRouter()
const { mobileMenuOpen, toggleMobileMenu } = useMobileUserMenu()

const props = defineProps({
  type: {
    type: String,
    default: 'user',
    validator: (value) => ['user', 'store'].includes(value)
  },
  showMobileMenu: {
    type: Boolean,
    default: false
  }
})

const navigateToStores = () => {
  router.push('/stores')
}
</script>

<style scoped>
.user-navbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem 2rem;
  background: var(--iluria-color-container-bg);
  border-bottom: 1px solid var(--iluria-color-border);
  box-shadow: var(--iluria-shadow-sm);
  position: sticky;
  top: 0;
  z-index: 60;
}

.logo-section {
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

.logo {
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

/* Mobile Menu Button */
.mobile-menu-button {
  display: none; /* Oculto por padrão */
  align-items: center;
  justify-content: center;
  padding: 0.5rem;
  border: none;
  border-radius: 0.375rem;
  background: transparent;
  color: var(--iluria-color-text-primary);
  cursor: pointer;
  transition: all 0.2s ease;
}

/* Mostrar apenas no mobile */
@media (max-width: 767px) {
  .mobile-menu-button {
    display: flex;
  }
}

.mobile-menu-button:hover {
  background: var(--iluria-color-hover);
}

.mobile-menu-button:active {
  transform: scale(0.95);
}

/* Responsividade do botão mobile */
@media (max-width: 768px) {
  .user-navbar {
    padding: 1rem 1.5rem;
  }
  
  .logo-section {
    gap: 0.5rem;
  }
  
  .mobile-menu-button {
    margin-right: 0.25rem;
  }
}

.navbar-actions {
  display: flex;
  align-items: center;
  gap: 1rem;
}

/* Logo theming */
.logo-themed {
  transition: transform 0.15s ease;
  transform: translateZ(0);
  will-change: transform;
  image-rendering: -webkit-optimize-contrast;
  image-rendering: -moz-crisp-edges;
  image-rendering: crisp-edges;
  shape-rendering: geometricPrecision;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  backface-visibility: hidden;
  -webkit-backface-visibility: hidden;
}

.logo-themed:hover {
  transform: scale(1.05) translateZ(0);
}

/* Media query para displays de alta densidade */
@media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {
  .logo-themed {
    image-rendering: -webkit-optimize-contrast;
    shape-rendering: geometricPrecision;
  }
}
</style>
