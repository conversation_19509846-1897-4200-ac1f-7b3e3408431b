<template>
  <IluriaModal
    v-model="showModal"
    :title="t('measurementTable.title')"
    :show-footer="true"
    :save-label="t('save')"
    :cancel-label="t('cancel')"
    @save="handleSave"
    @cancel="handleCancel"
    size="lg"
    :dialog-style="{ width: '95vw', maxWidth: '1200px' }"
  >
  <Form v-slot="$form" :resolver="resolver" :validate-on-submit="true" :validate-on-blur="false" :validate-on-change="false" :validate-on-value-update="false" :initial-values="form" class="w-full max-w-full measurement-table-form">
    <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6 mt-4">
      <IluriaInputText
        id="measurementTableName"
        name="name"
        v-model="form.name"
        :label="t('measurementTable.name')"
        :placeholder="t('measurementTable.namePlaceholder')"
        :formContext="$form.name"
        @update:model-value="(value) => updateFormField('name', value)"
      />
    </div>

    <div class="mb-6">
      <IluriaRadioGroup
        id="measurementTableType"
        name="type"
        v-model="form.type"
        :label="t('measurementTable.type')"
        :options="typeOptions"
        :formContext="$form.type"
        @update:model-value="onTypeChange"
      />
    </div>

    <!-- Tipo Imagem -->
    <div v-if="form.type === 'IMAGE'" class="mb-6">
      <IluriaSimpleImageUpload
        v-model="form.imageUrl"
        :label="t('measurementTable.image')"
        :description="t('measurementTable.imageDescription')"
        accept="image/*"
        :add-button-text="t('measurementTable.addImage')"
        :change-button-text="t('measurementTable.changeImage')"
        :remove-button-text="t('measurementTable.removeImage')"
        :format-hint="t('measurementTable.imageFormats')"
        :prevent-cache="true"
        @change="onImageChange"
      />
    </div>

    <!-- Tipo Estruturado -->
    <div v-if="form.type === 'STRUCTURED'" class="space-y-6">
      <!-- Configuração com Tags -->
      <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
        <!-- Colunas com Tags -->
        <div class="space-y-4 tags-section">
          <IluriaTitle class="text-lg font-medium">{{ t('measurementTable.columns') }}</IluriaTitle>
          
          <IluriaInputTags
            id="columnTags"
            v-model="columnTags"
            :placeholder="t('measurementTable.columnTagsPlaceholder')"
            :draggable="true"
            class="tags-wrapper"
            inputClass="iluria-tag-input"
          />
          
          <p class="text-sm text-[var(--iluria-color-text-secondary)] tag-hint">{{ t('measurementTable.columnTagsHint') }}</p>
        </div>
        
        <!-- Tamanhos com Tags -->
        <div class="space-y-4 tags-section">
          <IluriaTitle class="text-lg font-medium">{{ t('measurementTable.sizes') }}</IluriaTitle>
          
          <IluriaInputTags
            id="sizeTags"
            v-model="sizeTags"
            :placeholder="t('measurementTable.sizeTagsPlaceholder')"
            :draggable="true"
            class="tags-wrapper"
            inputClass="iluria-tag-input"
          />
          
          <p class="text-sm text-[var(--iluria-color-text-secondary)] tag-hint">{{ t('measurementTable.sizeTagsHint') }}</p>
          
          <!-- Range de Tamanhos Pré-definidos -->
          <div class="mt-4">
            <IluriaLabel class="block text-sm font-medium text-[var(--iluria-color-text-primary)] mb-2">
              {{ t('measurementTable.quickSizeRanges') }}
            </IluriaLabel>
            <div class="flex flex-wrap gap-2 mb-4 size-range-buttons">
              <IluriaButton 
                v-for="range in sizeRanges.filter(r => !r.isCustom)"
                :key="range.key"
                @click="applySizeRange(range.sizes)"
                color="secondary"
                size="small"
              >
                {{ t(`measurementTable.${range.key}`) }}
              </IluriaButton>
            </div>
          </div>
        </div>
      </div>

      <!-- Tabela de Medidas Gerada -->
      <div v-if="columnTags.length > 0 && sizeTags.length > 0" class="mt-6">
        <IluriaTitle class="text-lg font-medium mb-4">{{ t('measurementTable.measurementGrid') }}</IluriaTitle>
        
        <div class="overflow-x-auto border border-[var(--iluria-color-border)] rounded-lg">
          <table class="min-w-full border-collapse bg-[var(--iluria-color-container-bg)] measurement-table">
            <thead class="bg-[var(--iluria-color-sidebar-bg)]">
              <tr>
                <th class="border border-[var(--iluria-color-border)] px-4 py-3 text-left font-medium text-[var(--iluria-color-text-primary)]">
                  {{ t('measurementTable.size') }}
                </th>
                <th 
                  v-for="(column, index) in columnTags" 
                  :key="index"
                  class="border border-[var(--iluria-color-border)] px-4 py-3 text-center font-medium text-[var(--iluria-color-text-primary)]"
                >
                  <div class="space-y-2">
                    <div class="flex items-center justify-center gap-2">
                      <span class="block">{{ column }}</span>
                      <IluriaButton 
                        @click="openImageUploadModal(index, column)"
                        color="secondary"
                        size="small"
                        :hugeIcon="Image01Icon"
                        class="!p-1"
                      />
                    </div>
                    <div class="flex justify-center">
                      <IluriaSelect
                        v-model="columnUnits[index]"
                        class="min-w-[60px] text-xs"
                        :options="unitOptions"
                        @update:model-value="updateColumnUnit(index, $event)"
                      />
                    </div>
                  </div>
                </th>
              </tr>
            </thead>
            <tbody>
              <tr v-for="(size, sizeIndex) in sizeTags" :key="sizeIndex" class="hover:bg-[var(--iluria-color-sidebar-bg)]">
                <td class="border border-[var(--iluria-color-border)] px-4 py-3 font-medium text-[var(--iluria-color-text-primary)]">
                  {{ size }}
                </td>
                <td 
                  v-for="(column, colIndex) in columnTags" 
                  :key="colIndex"
                  class="border border-[var(--iluria-color-border)] px-2 py-2 bg-[var(--iluria-color-input-bg)]"
                >
                  <IluriaInputText
                    :key="`measurement-${sizeIndex}-${colIndex}`"
                    :model-value="getMeasurementValue(sizeIndex, colIndex)"
                    @update:model-value="(value) => setMeasurementValue(sizeIndex, colIndex, value)"
                    :placeholder="t('measurementTable.measurementPlaceholder')"
                    inputClass="measurement-input w-full text-center"
                    @keydown.enter.prevent
                    @keydown.tab="moveToNextCell(sizeIndex, colIndex, $event)"
                    @keydown.shift.tab="moveToPrevCell(sizeIndex, colIndex, $event)"
                  />
                </td>
              </tr>
            </tbody>
          </table>
        </div>
      </div>
    </div>

    <!-- Preview -->
    <div v-if="showPreview" class="mt-8 preview-section">
      <IluriaTitle class="text-lg font-medium mb-4">{{ t('measurementTable.preview') }}</IluriaTitle>
      <MeasurementTablePreview 
        :measurement-table="form" 
        :product-images="productImages"
      />
    </div>

    <!-- Modal de Upload de Imagem para Coluna -->
    <IluriaModal 
      v-model="showImageUploadModal" 
      :title="t('measurementTable.uploadColumnImage')"
      size="md"
      class="image-upload-modal"
    >
      <div v-if="selectedColumnForImage" class="space-y-4">
        <div class="text-center">
          <h4 class="text-lg font-medium text-[var(--iluria-color-text-primary)] mb-2">
            {{ selectedColumnForImage.name }}
          </h4>
          <p class="text-sm text-[var(--iluria-color-text-secondary)]">
            {{ t('measurementTable.columnImageDescription') }}
          </p>
        </div>

        <IluriaSimpleImageUpload
          v-model="columnImagePreview"
          :label="t('measurementTable.columnImage')"
          :description="t('measurementTable.columnImageHelp')"
          accept="image/*"
          :add-button-text="t('measurementTable.addColumnImage')"
          :change-button-text="t('measurementTable.changeColumnImage')"
          :remove-button-text="t('measurementTable.removeColumnImage')"
          :format-hint="t('measurementTable.imageFormats')"
          :prevent-cache="true"
          @change="onColumnImageChange"
        />
      </div>

      <template #footer>
        <div class="flex justify-end gap-3 action-buttons">
          <IluriaButton 
            @click="closeImageUploadModal" 
            color="secondary"
          >
            {{ t('cancel') }}
          </IluriaButton>
          <IluriaButton 
            @click="saveColumnImage" 
            color="primary"
            :disabled="!columnImagePreview"
          >
            {{ t('save') }}
          </IluriaButton>
        </div>
      </template>
    </IluriaModal>
  </Form>
  </IluriaModal>
</template>

<script setup>
import { ref, computed, watch, nextTick } from 'vue'
import { useI18n } from 'vue-i18n'
import { useToast } from '@/services/toast.service'
import { Image01Icon } from '@hugeicons-pro/core-bulk-rounded'
import IluriaInputText from '@/components/iluria/form/IluriaInputText.vue'
import IluriaRadioGroup from '@/components/iluria/form/IluriaRadioGroup.vue'
import IluriaSimpleImageUpload from '@/components/iluria/form/IluriaSimpleImageUpload.vue'
import IluriaModal from '@/components/iluria/IluriaModal.vue'
import IluriaButton from '@/components/iluria/IluriaButton.vue'
import IluriaSelect from '@/components/iluria/form/IluriaSelect.vue'
import MeasurementTablePreview from './MeasurementTablePreview.vue'
import { measurementTableApi } from '@/services/measurementTable.service'
import { Form } from '@primevue/forms'
import { zodResolver } from '@primevue/forms/resolvers/zod'
import { z } from 'zod'
import IluriaInputTags from '@/components/iluria/form/IluriaInputTags.vue'
import IluriaLabel from '@/components/iluria/IluriaLabel.vue'
import IluriaTitle from '@/components/iluria/IluriaTitle.vue'

const { t } = useI18n()
const toast = useToast()

const emit = defineEmits(['update:modelValue', 'save', 'cancel', 'update:visible'])

const props = defineProps({
  modelValue: {
    type: Object,
    default: () => ({})
  },
  showPreview: {
    type: Boolean,
    default: true
  },
  measurementTableId: {
    type: String,
    default: null
  },
  productImages: {
    type: Array,
    default: () => []
  },
  visible: {
    type: Boolean,
    default: false
  }
})

const showModal = computed({
  get: () => props.visible,
  set: (value) => emit('update:visible', value)
})

const form = computed({
  get: () => {
    const defaultValue = {
      name: '',
      type: 'STRUCTURED',
      imageUrl: '',
      columns: [],
      rows: [],
      newImageFile: null
    }
    
    if (!props.modelValue) {
      return defaultValue
    }
    
    return {
      ...defaultValue,
      ...props.modelValue
    }
  },
  set: (value) => {
    if (!isUpdatingFromProps.value) {
      emit('update:modelValue', value)
    }
  }
})

const typeOptions = [
  { label: t('measurementTable.typeImage'), value: 'IMAGE' },
  { label: t('measurementTable.typeStructured'), value: 'STRUCTURED' }
]

const unitOptions = [
  { label: 'cm', value: 'cm' },
  { label: 'mm', value: 'mm' },
  { label: 'pol', value: 'in' },
  { label: 'm', value: 'm' },
]

const sizeRanges = [
  { key: 'sizeRangePPtoGG', sizes: ['PP', 'P', 'M', 'G', 'GG'] },
  { key: 'sizeRangeXStoXL', sizes: ['XS', 'S', 'M', 'L', 'XL'] },
  { key: 'sizeRangeXStoXXL', sizes: ['XS', 'S', 'M', 'L', 'XL', 'XXL'] },
  { key: 'sizeRangeNumeric', sizes: ['36', '38', '40', '42', '44', '46', '48'] },
  { key: 'sizeRangeInfantil', sizes: ['2', '4', '6', '8', '10', '12', '14', '16'] },
  { key: 'sizeRangeBaby', sizes: ['RN', '3M', '6M', '9M', '12M', '18M', '24M'] },
  { key: 'sizeRangeCustom', sizes: [], isCustom: true }
]

// Tags para colunas e tamanhos
const columnTags = ref([])
const sizeTags = ref([])
const columnUnits = ref([])
const measurementGrid = ref({})

const showImageUploadModal = ref(false)
const selectedColumnForImage = ref(null)
const columnImagePreview = ref(null)

// Flag para prevenir recursão infinita
const isUpdatingFromProps = ref(false)

// Função para gerar ID único temporário
const generateTempId = (prefix = 'temp') => {
  return `${prefix}_${Date.now()}_${Math.random().toString(36).substring(2, 9)}`
}

const updateFormField = (field, value) => {
  if (!isUpdatingFromProps.value) {
    const updatedForm = { ...form.value }
    updatedForm[field] = value
    form.value = updatedForm
  }
}

watch(columnTags, (newColumns, oldColumns) => {
  if (!isUpdatingFromProps.value && JSON.stringify(newColumns) !== JSON.stringify(oldColumns)) {
    syncColumnsFromTags(newColumns)
    nextTick(() => debugColumnImages())
  }
}, { deep: true })

watch(sizeTags, (newSizes, oldSizes) => {
  if (!isUpdatingFromProps.value && JSON.stringify(newSizes) !== JSON.stringify(oldSizes)) {
    syncRowsFromTags(newSizes)
  }
}, { deep: true })

const getMeasurementValue = (sizeIndex, colIndex) => {
  const key = `${sizeIndex}-${colIndex}`
  return measurementGrid.value[key] || null
}

const setMeasurementValue = (sizeIndex, colIndex, value) => {
  const key = `${sizeIndex}-${colIndex}`
    if (measurementGrid.value[key] !== value) {
    measurementGrid.value[key] = value
    clearTimeout(updateFormFromGrid.timeoutId)
    updateFormFromGrid.timeoutId = setTimeout(() => {
      updateFormFromGrid()
    }, 50)
  }
}

const moveToNextCell = (sizeIndex, colIndex, event) => {
  event.preventDefault()
  
  let nextColIndex = colIndex + 1
  let nextSizeIndex = sizeIndex
  
  if (nextColIndex >= columnTags.value.length) {
    nextColIndex = 0
    nextSizeIndex = sizeIndex + 1
  }
  
  if (nextSizeIndex >= sizeTags.value.length) {
    nextColIndex = 0
    nextSizeIndex = 0
  }
  
  nextTick(() => {
    let inputs = Array.from(document.querySelectorAll('.measurement-input'))
    // If class is on inner input, they are selected; otherwise, select nested inputs
    if (inputs.length === 0) {
      inputs = Array.from(document.querySelectorAll('.measurement-input input'))
    }
    const nextInputIndex = nextSizeIndex * columnTags.value.length + nextColIndex
    if (inputs[nextInputIndex]) {
      inputs[nextInputIndex].focus()
    }
  })
}

const moveToPrevCell = (sizeIndex, colIndex, event) => {
  event.preventDefault()

  let prevColIndex = colIndex - 1
  let prevSizeIndex = sizeIndex

  if (prevColIndex < 0) {
    prevSizeIndex = sizeIndex - 1
    prevColIndex = columnTags.value.length - 1
  }

  if (prevSizeIndex < 0) {
    prevSizeIndex = sizeTags.value.length - 1
    prevColIndex = columnTags.value.length - 1
  }

  nextTick(() => {
    let inputs = Array.from(document.querySelectorAll('.measurement-input'))
    if (inputs.length === 0) {
      inputs = Array.from(document.querySelectorAll('.measurement-input input'))
    }
    const prevInputIndex = prevSizeIndex * columnTags.value.length + prevColIndex
    if (inputs[prevInputIndex]) {
      inputs[prevInputIndex].focus()
    }
  })
}

const applySizeRange = (sizes) => {
  sizeTags.value = [...sizes]
}

const updateColumnUnit = (index, unit) => {
  columnUnits.value[index] = unit
  syncColumnsFromTags(columnTags.value)
}

const openImageUploadModal = (columnIndex, columnName) => {
  const column = form.value.columns?.[columnIndex]
  
  selectedColumnForImage.value = {
    index: columnIndex,
    name: columnName,
    id: column?.id
  }
  
  if (column?.imageUrl) {
    columnImagePreview.value = column.imageUrl
  } else {
    columnImagePreview.value = null
  }
  
  showImageUploadModal.value = true
}

const closeImageUploadModal = () => {
  showImageUploadModal.value = false
  selectedColumnForImage.value = null
  columnImagePreview.value = null
}

const onColumnImageChange = (file) => {
  columnImagePreview.value = file
}

const saveColumnImage = async () => {
  if (!selectedColumnForImage.value || !columnImagePreview.value) return
  
  const columnIndex = selectedColumnForImage.value.index
  const columnId = selectedColumnForImage.value.id
  
  // Atualizar o formulário para incluir a imagem na coluna
  const updatedForm = { ...form.value }
  
  // Garantir que as colunas existem
  if (!updatedForm.columns) {
    updatedForm.columns = []
  }
  
  // Garantir que a coluna existe
  while (updatedForm.columns.length <= columnIndex) {
    updatedForm.columns.push({
      id: generateTempId(),
      label: columnTags.value[updatedForm.columns.length] || '',
      unit: columnUnits.value[updatedForm.columns.length] || 'cm',
      imageUrl: '',
      position: updatedForm.columns.length
    })
  }
  
  // Verificar se pode fazer upload individual (tabela existe, arquivo válido, ID real não temporário)
  const isULID = columnId && columnId.length === 26 && !columnId.includes('_')
  const canUploadIndividually = props.measurementTableId && 
      columnImagePreview.value instanceof File && 
      isULID
  
  if (canUploadIndividually) {
    try {
      const imageUrl = await measurementTableApi.uploadColumnImage(
        props.measurementTableId, 
        columnId, 
        columnImagePreview.value
      )
      updatedForm.columns[columnIndex].imageUrl = imageUrl
      form.value = updatedForm
      closeImageUploadModal()
      toast.showSuccess(t('measurementTable.columnImageSaved'))
    } catch (error) {
      console.error('Erro no upload individual da imagem da coluna:', error)
      toast.showError(t('measurementTable.imageUploadError'))
    }
  } else {
    // Caso seja novo (sem ID real) ou ID temporário, armazenar para upload posterior
    if (columnImagePreview.value instanceof File) {
      // Armazenar arquivo por ID da coluna, não por índice
      if (!updatedForm.pendingColumnImagesByColumnId) {
        updatedForm.pendingColumnImagesByColumnId = {}
      }
      updatedForm.pendingColumnImagesByColumnId[columnId] = {
        file: columnImagePreview.value,
        columnIndex: columnIndex // Guardar índice para mapear na hora do upload
      }
      
      // Manter compatibilidade com sistema antigo para transição
      if (!updatedForm.pendingColumnImages) {
        updatedForm.pendingColumnImages = {}
      }
      updatedForm.pendingColumnImages[columnIndex] = columnImagePreview.value
      
      // Criar preview local
      const reader = new FileReader()
      reader.onload = (e) => {
        updatedForm.columns[columnIndex].imageUrl = e.target.result
        form.value = updatedForm
        closeImageUploadModal()
        toast.showSuccess(t('measurementTable.columnImageSaved'))
      }
      reader.readAsDataURL(columnImagePreview.value)
    } else {
      // Se é uma URL, usar diretamente
      updatedForm.columns[columnIndex].imageUrl = columnImagePreview.value
      form.value = updatedForm
      closeImageUploadModal()
      toast.showSuccess(t('measurementTable.columnImageSaved'))
    }
  }
}

const syncColumnsFromTags = (columns) => {
  const updatedForm = { ...form.value }
  const existingColumns = updatedForm.columns || []
  
  // Criar mapa de colunas existentes por label para preservar IDs e imagens durante reordenação
  const existingColumnsByLabel = {}
  existingColumns.forEach(col => {
    if (col.label) {
      existingColumnsByLabel[col.label] = col
    }
  })
  
  updatedForm.columns = columns.map((label, index) => {
    // Primeiro, tentar encontrar coluna existente pelo label
    const existingColumnByLabel = existingColumnsByLabel[label]
    // Fallback para coluna na mesma posição
    const existingColumnByIndex = existingColumns[index]
    
    const columnToUse = existingColumnByLabel || existingColumnByIndex
    
    return {
      id: columnToUse?.id || generateTempId(),
      label,
      unit: columnUnits.value[index] || columnToUse?.unit || 'cm',
      imageUrl: columnToUse?.imageUrl || '',
      position: index
    }
  })
  form.value = updatedForm
  
  // Ajustar tamanho do array de unidades
  while (columnUnits.value.length < columns.length) {
    columnUnits.value.push('cm')
  }
  if (columnUnits.value.length > columns.length) {
    columnUnits.value = columnUnits.value.slice(0, columns.length)
  }
}

const syncRowsFromTags = (sizes) => {
  const updatedForm = { ...form.value }
  const existingRows = updatedForm.rows || []
  
  updatedForm.rows = sizes.map((sizeLabel, index) => {
    const existingRow = existingRows[index]
    return {
      id: existingRow?.id || generateTempId(),
      sizeLabel,
      position: index,
      values: existingRow?.values || []
    }
  })
  form.value = updatedForm
}

const updateFormFromGrid = () => {
  if (isUpdatingFromProps.value) return
  
  const updatedForm = { ...form.value }
  
  if (!updatedForm.columns) {
    updatedForm.columns = []
  }
  
  // Criar mapa de colunas existentes por label para preservar durante grid updates
  const existingColumnsByLabel = {}
  updatedForm.columns.forEach(col => {
    if (col.label) {
      existingColumnsByLabel[col.label] = col
    }
  })
  
  updatedForm.columns = columnTags.value.map((label, index) => {
    const existingColumn = existingColumnsByLabel[label] || updatedForm.columns[index]
    return {
      id: existingColumn?.id || generateTempId(),
      label,
      unit: columnUnits.value[index] || existingColumn?.unit || 'cm',
      imageUrl: existingColumn?.imageUrl || '',
      position: index
    }
  })
  
  updatedForm.rows = sizeTags.value.map((sizeLabel, sizeIndex) => ({
    id: updatedForm.rows?.[sizeIndex]?.id || generateTempId(),
    sizeLabel,
    position: sizeIndex,
    values: columnTags.value.map((columnLabel, colIndex) => ({
      id: updatedForm.rows?.[sizeIndex]?.values?.[colIndex]?.id || generateTempId(),
      value: getMeasurementValue(sizeIndex, colIndex) || null
    }))
  }))
  
  form.value = updatedForm
}

const resolver = zodResolver(
  z.object({
    name: z.string()
      .trim()
      .min(1, t('measurementTable.nameRequired'))
      .max(255, t('validation.maxLength', { field: t('measurementTable.name'), length: 255 })),
    type: z.enum(['IMAGE', 'STRUCTURED'], {
      required_error: t('measurementTable.typeRequired'),
    }),
    columns: z.array(z.object({
      label: z.string().min(1, t('validation.fieldRequired', { field: t('measurementTable.columnLabel') })),
      unit: z.string().optional(),
      imageUrl: z.string().optional(),
    })).optional(),
    rows: z.array(z.object({
      sizeLabel: z.string().min(1, t('validation.fieldRequired', { field: t('measurementTable.sizeLabel') })),
      values: z.array(z.object({
        value: z.string().nullable().optional(),
      })).optional(),
    })).optional(),
  })
)

const onImageChange = async (file) => {
  const updatedForm = { ...form.value }
  
  if (!file) {
    if (props.measurementTableId && updatedForm.imageUrl && updatedForm.imageUrl.startsWith('http')) {
      try {
        await measurementTableApi.deleteImage(props.measurementTableId)
        toast.showSuccess(t('measurementTable.imageDeleteSuccess'))
      } catch (error) {
        console.error('Erro ao remover a imagem:', error)
        toast.showError(t('measurementTable.imageDeleteError'))
      }
    }
    
    updatedForm.imageUrl = ''
    updatedForm.newImageFile = null
    form.value = updatedForm
    return
  }
  
  if (file.size > 15 * 1024 * 1024) {
    toast.showError(t('measurementTable.imageTooLarge') || 'A imagem não pode ser maior que 15MB')
    return
  }
  
  updatedForm.newImageFile = file
  
  if (props.measurementTableId) {
    try {
      const imageUrl = await measurementTableApi.uploadImage(props.measurementTableId, file)
      updatedForm.imageUrl = imageUrl
      updatedForm.newImageFile = null
      toast.showSuccess(t('measurementTable.imageUploadSuccess'))
    } catch (error) {
      console.error('Erro no upload:', error)
      toast.showError(t('measurementTable.imageUploadError'))
      return
    }
  } else {
    const reader = new FileReader()
    reader.onload = (e) => {
      updatedForm.imageUrl = e.target.result
      form.value = updatedForm
    }
    reader.readAsDataURL(file)
    return
  }
  
  form.value = updatedForm
}

const onTypeChange = (newType) => {
  const updatedForm = { ...form.value }
  if (newType === 'IMAGE') {
    updatedForm.columns = []
    updatedForm.rows = []
    columnTags.value = []
    sizeTags.value = []
    measurementGrid.value = {}
  } else {
    updatedForm.imageUrl = ''
    updatedForm.newImageFile = null
  }
  form.value = updatedForm
}

// Inicializar tags a partir do formulário existente
watch(() => props.modelValue, (newValue, oldValue) => {
  // Evitar processar se o valor é o mesmo ou se não há referências válidas
  if (newValue === oldValue || !columnTags.value || !sizeTags.value) return
  
  try {
    isUpdatingFromProps.value = true
    
    // Limpar estado anterior apenas se necessário
    if (!newValue || (!newValue.columns && !newValue.rows)) {
      columnTags.value = []
      sizeTags.value = []
      columnUnits.value = []
      measurementGrid.value = {}
      return
    }
    
    // Só atualizar se realmente mudou
    const newColumns = Array.isArray(newValue.columns) ? newValue.columns : []
    const newRows = Array.isArray(newValue.rows) ? newValue.rows : []
    
    const currentColumnLabels = columnTags.value ? columnTags.value.join(',') : ''
    const newColumnLabels = newColumns.map(col => col?.label || '').join(',')
    
    if (currentColumnLabels !== newColumnLabels) {
      columnTags.value = newColumns.map(col => col?.label || '')
      columnUnits.value = newColumns.map(col => col?.unit || 'cm')
    }
    
    const currentRowLabels = sizeTags.value ? sizeTags.value.join(',') : ''
    const newRowLabels = newRows.map(row => row?.sizeLabel || '').join(',')
    
    if (currentRowLabels !== newRowLabels) {
      sizeTags.value = newRows.map(row => row?.sizeLabel || '')
      
      // Limpar grade existente
      measurementGrid.value = {}
      
      // Reconstituir a grade de medidas
      newRows.forEach((row, sizeIndex) => {
        if (row?.values && Array.isArray(row.values) && row.values.length > 0) {
          row.values.forEach((value, colIndex) => {
            if (value && typeof value.value !== 'undefined') {
              const key = `${sizeIndex}-${colIndex}`
              measurementGrid.value[key] = value.value
            }
          })
        }
      })
    }
  } catch (error) {
    console.error('Error in MeasurementTableForm watch:', error)
  } finally {
    // Aguardar próximo tick para garantir que todas as atualizações foram processadas
    nextTick(() => {
      try {
        isUpdatingFromProps.value = false
      } catch (error) {
        console.error('Error resetting isUpdatingFromProps:', error)
      }
    })
  }
}, { immediate: true })

const uploadPendingImage = async () => {
  if (form.value.newImageFile && props.measurementTableId) {
    try {
      const imageUrl = await measurementTableApi.uploadImage(props.measurementTableId, form.value.newImageFile)
      const updatedForm = { ...form.value }
      updatedForm.imageUrl = imageUrl
      updatedForm.newImageFile = null
      form.value = updatedForm
      toast.showSuccess(t('measurementTable.imageUploadSuccess'))
      return imageUrl
    } catch (error) {
      toast.showError(t('measurementTable.imageUploadError'))
      throw error
    }
  }
  return null
}

const uploadPendingColumnImages = async () => {
  if ((form.value.pendingColumnImagesByColumnId || form.value.pendingColumnImages) && props.measurementTableId) {
    try {
      let uploadedUrls = {}
      const updatedForm = { ...form.value }
      
      if (form.value.pendingColumnImagesByColumnId) {
        const indexedImages = {}
        Object.entries(form.value.pendingColumnImagesByColumnId).forEach(([columnId, data]) => {
          const currentIndex = updatedForm.columns.findIndex(col => col.id === columnId)
          if (currentIndex >= 0) {
            indexedImages[currentIndex] = data.file
          }
        })
        
        if (Object.keys(indexedImages).length > 0) {
          uploadedUrls = await measurementTableApi.batchUploadColumnImages(
            props.measurementTableId, 
            indexedImages
          )
        }
      } 
      else if (form.value.pendingColumnImages) {
        uploadedUrls = await measurementTableApi.batchUploadColumnImages(
          props.measurementTableId, 
          form.value.pendingColumnImages
        )
      }
      
      Object.entries(uploadedUrls).forEach(([columnIndex, imageUrl]) => {
        const index = parseInt(columnIndex)
        if (updatedForm.columns[index]) {
          updatedForm.columns[index].imageUrl = imageUrl
        }
      })
      
      updatedForm.pendingColumnImages = {}
      updatedForm.pendingColumnImagesByColumnId = {}
      form.value = updatedForm
      
      toast.showSuccess(t('measurementTable.columnImageSaved'))
      return uploadedUrls
    } catch (error) {
      toast.showError(t('measurementTable.imageUploadError'))
      throw error
    }
  }
  return {}
}

// Handlers para eventos do modal principal
const handleSave = async () => {
  try {
    // Upload de imagens pendentes se houver
    if (form.value.newImageFile) {
      await uploadPendingImage()
    }
    if (form.value.pendingColumnImages && Object.keys(form.value.pendingColumnImages).length > 0) {
      await uploadPendingColumnImages()
    }
    emit('save', form.value)
  } catch (error) {
    console.error('Erro ao salvar:', error)
    toast.showError(t('measurementTable.errorSaving'))
  }
}

const handleCancel = () => {
  emit('cancel')
}

defineExpose({
  uploadPendingImage,
  uploadPendingColumnImages
})
</script>

<style scoped>
/* Transições suaves para temas */
.measurement-table-form {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* Tabela com melhor responsividade */
.measurement-table {
  min-width: 100%;
  border-collapse: collapse;
  background-color: var(--iluria-color-container-bg);
  border-radius: 8px;
  overflow: hidden;
  box-shadow: var(--iluria-shadow-sm);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.measurement-table th {
  background-color: var(--iluria-color-sidebar-bg);
  color: var(--iluria-color-text-primary);
  font-weight: 600;
  padding: 12px 16px;
  text-align: left;
  border-bottom: 2px solid var(--iluria-color-border);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.measurement-table td {
  padding: 8px 12px;
  border-bottom: 1px solid var(--iluria-color-border);
  background-color: var(--iluria-color-input-bg);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.measurement-table tr:hover td {
  background-color: var(--iluria-color-sidebar-bg);
}

/* Inputs da tabela */
.measurement-input {
  border: none;
  background: transparent;
  color: var(--iluria-color-text-primary);
  font-size: 0.875rem;
  text-align: center;
  width: 100%;
  padding: 4px 8px;
  border-radius: 4px;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.measurement-input:focus {
  background-color: var(--iluria-color-container-bg);
  box-shadow: 0 0 0 2px var(--iluria-color-border-focus);
  outline: none;
}

.measurement-input::placeholder {
  color: var(--iluria-color-text-muted);
}

/* Botões de range de tamanhos */
.size-range-buttons {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  margin-bottom: 16px;
}

/* Seção de preview */
.preview-section {
  margin-top: 32px;
  padding: 24px;
  background-color: var(--iluria-color-container-bg);
  border-radius: 8px;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* Modal de upload de imagem */
.image-upload-modal {
  background-color: var(--iluria-color-container-bg);
  color: var(--iluria-color-text-primary);
}

.image-upload-modal .modal-content {
  padding: 24px;
}

.image-upload-modal .modal-header {
  color: var(--iluria-color-text-primary);
  border-bottom: 1px solid var(--iluria-color-border);
}

.image-upload-modal .modal-footer {
  border-top: 1px solid var(--iluria-color-border);
  padding-top: 16px;
}

/* Animações para mudança de tema */
@media (prefers-reduced-motion: no-preference) {
  .measurement-table,
  .measurement-table th,
  .measurement-table td,
  .measurement-input,
  .preview-section {
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  }
}

/* Suporte para temas escuros */
@media (prefers-color-scheme: dark) {
  .measurement-table {
    box-shadow: var(--iluria-shadow-lg);
  }
}

/* Responsividade */
@media (max-width: 768px) {
  .measurement-table {
    font-size: 0.875rem;
  }
  
  .measurement-table th,
  .measurement-table td {
    padding: 8px 12px;
  }
  
  .measurement-input {
    font-size: 0.8rem;
  }
}

/* Melhoria na acessibilidade */
.measurement-table:focus-within {
  box-shadow: 0 0 0 2px var(--iluria-color-border-focus);
}

/* Estilo para tags */
.tags-section {
  margin-bottom: 24px;
}

.tags-section .tag-input {
  margin-bottom: 8px;
}

.tags-section .tag-hint {
  color: var(--iluria-color-text-secondary);
  font-size: 0.875rem;
  margin-top: 4px;
}

/* Estilo para botões de ação */
.action-buttons {
  display: flex;
  gap: 8px;
  justify-content: flex-end;
  margin-top: 16px;
}

/* Suporte para modo de alto contraste */
@media (prefers-contrast: high) {
  .measurement-table {
    border: 2px solid var(--iluria-color-border);
  }
  
  .measurement-table th,
  .measurement-table td {
    border: 1px solid var(--iluria-color-border);
  }
  
  .measurement-input:focus {
    box-shadow: 0 0 0 3px var(--iluria-color-border-focus);
  }
}

/* Estilo para inputs dentro de IluriaInputTags */
:deep(.iluria-tag-input) {
  border: 1px solid var(--iluria-color-input-border);
  background-color: var(--iluria-color-input-bg);
  color: var(--iluria-color-input-text);
  transition: all 0.2s ease;
}

:deep(.iluria-tag-input:focus) {
  border-color: var(--iluria-color-input-border-focus);
  box-shadow: 0 0 0 2px var(--iluria-color-input-border-focus);
  outline: none;
}
</style>

 