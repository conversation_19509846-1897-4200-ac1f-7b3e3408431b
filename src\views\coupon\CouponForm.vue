<template>
  <div class="coupon-form-container">
    <!-- Loading State -->
    <div v-if="loading" class="loading-container">
      <div class="spinner"></div>
      <p>{{ t('couponForm.loading') }}</p>
    </div>

    <!-- Main Content -->
    <div v-else>
      <!-- Header with actions -->
      <IluriaHeader
        :title="isEditing ? t('couponForm.editTitle') : t('couponForm.newTitle')"
        :subtitle="isEditing ? 'Edite as informações do cupom de desconto' : 'Cadastre e edite informações de cupom de desconto'"
        :showCancel="true"
        :cancelText="t('couponForm.cancel')"
        :showSave="true"
        :saveText="isEditing ? t('update') : t('save')"
        @cancel-click="goBackToList"
        @save-click="saveCoupon"
      />

      <!-- Form Content -->
      <Form v-slot="$form" :resolver="resolver" @submit="saveCoupon" :validate-on-submit="true" :validate-on-blur="true"
        :validate-on-change="false" :validate-on-value-update="false" :initial-values="formData">
        <div class="form-content">
          <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
            <!-- Main Content Column -->
            <div class="lg:col-span-2 space-y-6">
              <!-- Dados Básicos do Cupom -->
              <ViewContainer :title="t('couponForm.basicDataTitle')" :subtitle="t('couponForm.basicDataSubtitle')"
                :icon="CouponPercentIcon" iconColor="blue">
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <!-- Código do Cupom -->
                  <div class="md:col-span-2 space-y-2">
                    <IluriaInputText id="code" ref="codeInputRef" name="code" v-model="form.code"
                      :label="t('couponForm.codeLabel')" :placeholder="t('couponForm.codePlaceholder')" class="w-full"
                      :formContext="$form.code" />
                    <div class="flex gap-2">
                      <IluriaButton type="button" @click="generateRandomCode()" color="primary" :hugeIcon="ReloadIcon"
                        size="small" class="shrink-0">
                        {{ t('couponForm.generateCode') }}
                      </IluriaButton>
                    </div>
                  </div>

                  <!-- Descrição -->
                  <div class="md:col-span-2 space-y-2">
                    <IluriaInputText id="description" name="description" v-model="form.description"
                      :label="t('couponForm.descriptionLabel')" :placeholder="t('couponForm.descriptionPlaceholder')"
                      class="w-full" />
                  </div>

                  <!-- Tipo de Desconto e Valor -->
                  <div class="space-y-2">
                    <IluriaSelect id="discountType" v-model="discountTypeComputed" :options="discountTypes"
                      :label="t('couponForm.discountTypeLabel')" class="w-full" :formContext="$form.discountType" />
                  </div>

                  <div class="space-y-2" v-if="!['FREE_SHIPPING', 'GIFT_COUPON'].includes(discountTypeComputed)">
                    <IluriaInputText id="discountValue" name="discountValue" v-model.number="form.discountValue"
                      :label="t('couponForm.discountValueLabel')"
                      :type="discountTypeComputed.includes('PERCENTAGE') ? 'number' : 'money'"
                      :min="0"
                      :max="discountTypeComputed.includes('PERCENTAGE') ? 100 : null" :placeholder="discountTypeComputed.includes('PERCENTAGE')
                        ? t('couponForm.percentagePlaceholder')
                        : t('couponForm.fixedPlaceholder')"
                      :step="discountTypeComputed.includes('PERCENTAGE') ? 0.001 : 0.01"
                      :suffix="discountTypeComputed.includes('PERCENTAGE') ? '%' : ''"
                      :prefix="discountTypeComputed.includes('PERCENTAGE') ? '' : 'R$'" class="w-full"
                      :formContext="$form.discountValue" />
                  </div>

                  <!-- Status -->
                  <div class="md:col-span-2 grid grid-cols-2 gap-6">
                    <div class="space-y-2 flex items-center justify-between">
                      <IluriaToggleSwitch id="active" v-model="form.active" :label="t('couponForm.activeLabel')"
                        class="w-full" />
                    </div>
                    <div class="space-y-2 flex items-center justify-between">
                      <IluriaToggleSwitch id="promotional-cumulative" v-model="form.promotionalCumulative"
                        :label="t('couponForm.promotionalCumulativeLabel')" class="w-full" />
                    </div>
                  </div>
                </div>
              </ViewContainer>

              <!-- Limites e Condições -->
              <ViewContainer :title="t('couponForm.limitsConditionsTitle')"
                :subtitle="t('couponForm.limitsConditionsSubtitle')" :icon="LimitationIcon" iconColor="orange">
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <!-- Limite de Uso Total -->
                  <div class="space-y-2">
                    <div class="flex items-center justify-between">
                      <label for="usage-limit" class="field-label">
                        {{ t('couponForm.usageLimitLabel') }}
                      </label>
                      <IluriaToggleSwitch v-model="showUsageLimit" />
                    </div>
                    <IluriaInputText v-if="showUsageLimit" id="usageLimit" name="usageLimit"
                      v-model.number="form.usageLimit" type="number" :min="0"
                      :placeholder="t('couponForm.usageLimitPlaceholder')" class="w-full"
                      :formContext="$form.usageLimit" />
                  </div>

                  <!-- Limite por Cliente -->
                  <div class="space-y-2">
                    <div class="flex items-center justify-between">
                      <label for="usage-limit-per-customer" class="field-label">
                        {{ t('couponForm.usageLimitPerCustomerLabel') }}
                      </label>
                      <IluriaToggleSwitch v-model="showUsageLimitPerCustomer" />
                    </div>
                    <IluriaInputText v-if="showUsageLimitPerCustomer" id="usageLimitPerCustomer"
                      name="usageLimitPerCustomer" v-model.number="form.usageLimitPerCustomer" type="number" :min="0"
                      :placeholder="t('couponForm.usageLimitPerCustomerPlaceholder')" class="w-full"
                      :formContext="$form.usageLimitPerCustomer" />
                  </div>

                  <!-- Quantidade Mínima -->
                  <div class="space-y-2">
                    <div class="flex items-center justify-between">
                      <label for="minimum-quantity" class="field-label">
                        {{ t('couponForm.minimumQuantityLabel') }}
                      </label>
                      <IluriaToggleSwitch v-model="showMinimumQuantity" />
                    </div>
                    <IluriaInputText v-if="showMinimumQuantity" id="minimumQuantity" name="minimumQuantity"
                      v-model.number="form.minimumQuantity" type="number" :min="0"
                      :placeholder="t('couponForm.minimumQuantityPlaceholder')" class="w-full"
                      :formContext="$form.minimumQuantity" />
                  </div>

                  <!-- Valor Mínimo -->
                  <div class="space-y-2">
                    <div class="flex items-center justify-between">
                      <label for="minimum-value" class="field-label">
                        {{ t('couponForm.minimumValueLabel') }}
                      </label>
                      <IluriaToggleSwitch v-model="showMinimumValue" />
                    </div>
                    <IluriaInputText v-if="showMinimumValue" id="minimumValue" name="minimumValue"
                      v-model.number="form.minimumValue" type="number" :min="0" :step="0.01"
                      :placeholder="t('couponForm.minimumValuePlaceholder')" prefix="R$ " class="w-full"
                      :formContext="$form.minimumValue" />
                  </div>
                </div>
              </ViewContainer>

              <!-- Período de Validade -->
              <ViewContainer :title="t('couponForm.validityPeriodTitle')"
                :subtitle="t('couponForm.validityPeriodSubtitle')" :icon="CalendarIcon" iconColor="purple">
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <!-- Data de Início -->
                  <div class="space-y-2">
                    <label for="starts-at" class="field-label required">
                      {{ t('couponForm.startsAtLabel') }}
                    </label>
                    <IluriaInputDatePicker id="starts-at" v-model="form.startsAt" date-format="dd/mm/yy"
                      :show-time="true" required class="w-full" />
                  </div>

                  <!-- Data de Término -->
                  <div class="space-y-2">
                    <div class="flex items-center justify-between">
                      <label for="ends-at" class="field-label">
                        {{ t('couponForm.endsAtLabel') }}
                      </label>
                      <IluriaToggleSwitch v-model="showEndsAt" />
                    </div>
                    <IluriaInputDatePicker v-if="showEndsAt" id="ends-at" v-model="form.endsAt" date-format="dd/mm/yy"
                      :show-time="true" class="w-full" />
                  </div>
                </div>
              </ViewContainer>

              <!-- Seção de Uso por Cliente -->
              <ViewContainer :title="t('couponForm.usageByCustomerTitle')"
                :subtitle="t('couponForm.usageByCustomerDescription')" :icon="UserIcon" iconColor="pink">
                <div class="usage-section">
                  <div v-if="form.couponUsages && form.couponUsages.length > 0" class="space-y-4">
                    <div v-for="(usage, index) in form.couponUsages" :key="index" class="usage-record">
                      <div class="usage-record-header">
                        <h4 class="usage-record-title">{{ t('couponForm.customerRecord') }} #{{ index + 1 }}</h4>
                        <IluriaButton color="danger" size="small" :hugeIcon="Delete01Icon"
                          @click="removeCouponUsage(index)" :title="t('couponForm.removeCustomer')">
                          {{ t('couponForm.removeCustomer') }}
                        </IluriaButton>
                      </div>

                      <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                        <!-- Customer selection -->
                        <div class="md:col-span-2">
                          <label class="field-label">{{ t('couponForm.selectCustomer') }}</label>
                          <IluriaSelect :id="`customer_id_${index}`" v-model="usage.customerId"
                            @change="updateCustomerInfo(usage)" class="w-full" :options="customers" optionLabel="label"
                            optionValue="id" />
                        </div>
                        <!-- Usage limit -->
                        <div>
                          <label class="field-label">{{ t('couponForm.usageLimitCustomer') }}</label>
                          <IluriaInputText :id="`usage_limit_${index}`" v-model.number="usage.usageLimit" type="number"
                            :min="0" class="w-full" />
                        </div>
                      </div>

                      <!-- Customer info display -->
                      <div v-if="usage.customerId" class="customer-info">
                        <div class="usage-stats">
                          <div>
                            <span class="usage-label">{{ t('couponForm.usageCountLabel') }}</span>
                            <span class="usage-value">{{ usage.usageCount }}</span>
                          </div>
                          <div v-if="usage.usageLimit > 0" class="usage-progress"
                            :class="usage.usageCount >= usage.usageLimit ? 'usage-exceeded' : 'usage-available'">
                            {{ usage.usageCount }} / {{ usage.usageLimit }}
                            <span v-if="usage.usageCount >= usage.usageLimit" class="ml-1">⚠️</span>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                  <div v-else class="empty-usage">
                    {{ t('couponForm.noUsageRecords') }}
                  </div>
                  <!-- Add usage button -->
                  <IluriaButton @click="addCouponUsage" :hugeIcon="Add01Icon" color="primary" class="add-usage-button">
                    {{ t('couponForm.addUsageRecord') }}
                  </IluriaButton>
                </div>
              </ViewContainer>
            </div>

            <!-- Sidebar Column -->
            <div class="lg:col-span-1">
              <!-- Categorias -->
              <ViewContainer :title="t('couponForm.categoriesTitle')" :subtitle="t('couponForm.categoriesSubtitle')"
                :icon="TagIcon" iconColor="indigo">
                <div class="space-y-2">
                  <ProductCategories id="category-ids" v-model="form.categoryIds"
                    :placeholder="t('couponForm.categoryIdsPlaceholder')" class="w-full" />
                  <p class="category-help">
                    {{ t('couponForm.categoryIdsHelp') }}
                  </p>
                </div>
              </ViewContainer>
            </div>
          </div>
        </div>
      </Form>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, computed, watch } from 'vue';
import { useRouter, useRoute } from 'vue-router';
import { useToast } from 'primevue/usetoast';
import { useI18n } from 'vue-i18n';
import IluriaButton from '@/components/iluria/IluriaButton.vue';
import IluriaHeader from '@/components/iluria/IluriaHeader.vue';
import IluriaMultipleCategorySelector from '@/components/iluria/form/IluriaMultipleCategorySelector.vue';
import couponService from '@/services/coupon.service';
import customerService from '@/services/customer.service';
import IluriaInputText from '@/components/iluria/form/IluriaInputText.vue';
import IluriaSelect from '@/components/iluria/form/IluriaSelect.vue'
import IluriaToggleSwitch from '@/components/iluria/IluriaToggleSwitch.vue'
import IluriaInputDatePicker from '@/components/iluria/form/IluriaInputDatePicker.vue';
import ViewContainer from '@/components/layout/ViewContainer.vue';
import { Form } from '@primevue/forms';
import { zodResolver } from '@primevue/forms/resolvers/zod';
import { z } from 'zod';
import { requiredText, requiredNonNegativeNumber, optionalNonNegativeNumber } from '@/services/validation.service';
import {
  FloppyDiskIcon,
  CouponPercentIcon,
  LimitationIcon,
  CalendarIcon,
  UserIcon,
  TagIcon,
  ReloadIcon,
  Add01Icon,
  Delete01Icon
} from '@hugeicons-pro/core-stroke-rounded';
import ProductCategories from '@/components/products/ProductCategories.vue';

const router = useRouter();
const route = useRoute();
const toast = useToast();
const { t } = useI18n();

const defaultForm = {
  id: null,
  code: '',
  description: '',
  discountType: 'PERCENTAGE_TOTAL',
  discountValue: null,
  usageLimit: null,
  usageLimitPerCustomer: null,
  startsAt: new Date(),
  endsAt: new Date(new Date().setMonth(new Date().getMonth() + 1)),
  timezone: Intl.DateTimeFormat().resolvedOptions().timeZone,
  promotionalCumulative: false,
  categoryIds: [],
  minimumValue: null,
  minimumQuantity: null,
  active: true,
  couponUsages: []
};

const form = reactive({ ...defaultForm });
const formData = ref({ ...defaultForm });

const discountValueError = ref(false);
const loading = ref(false);
const isEditing = ref(false);
const customers = ref([]);
const currentPage = ref(0);
const totalPages = ref(0);
const codeInputRef = ref(null);

const discountTypes = computed(() => [
  { label: t('couponManager.percentageProducts'), value: 'PERCENTAGE_PRODUCTS' },
  { label: t('couponManager.percentageTotal'), value: 'PERCENTAGE_TOTAL' },
  { label: t('couponManager.percentageShipping'), value: 'PERCENTAGE_SHIPPING' },
  { label: t('couponManager.fixedTotal'), value: 'FIXED_TOTAL' },
  { label: t('couponManager.freeShipping'), value: 'FREE_SHIPPING' },
  { label: t('couponManager.gift'), value: 'GIFT_COUPON' }
]);

const createToggleComputed = (fieldName, defaultValue = 1) => {
  return computed({
    get: () => form[fieldName] !== null && form[fieldName] !== 0,
    set: (value) => {
      if (value) {
        form[fieldName] = defaultValue;
        formData.value = { ...form };
      } else {
        form[fieldName] = null;
      }
    }
  });
};

const showUsageLimit = createToggleComputed('usageLimit');
const showUsageLimitPerCustomer = createToggleComputed('usageLimitPerCustomer');
const showMinimumValue = createToggleComputed('minimumValue');
const showMinimumQuantity = createToggleComputed('minimumQuantity');

const showEndsAt = computed({
  get: () => form.endsAt !== null,
  set: (value) => {
    form.endsAt = value
      ? (form.endsAt ?? new Date(new Date().setMonth(new Date().getMonth() + 1)))
      : null;
  }
});

const discountTypeComputed = computed({
  get: () => form.discountType,
  set: (newVal) => {
    form.discountType = newVal;

    if (['FREE_SHIPPING', 'GIFT_COUPON'].includes(newVal)) {
      form.discountValue = 100;
    } else if (form.discountValue === null) {
      form.discountValue = 0;
    }

    if (newVal?.includes('PERCENTAGE') && form.discountValue > 100) {
      form.discountValue = 100;
    }
  }
});

const resolver = zodResolver(
  z.object({
    code: requiredText(t('couponForm.codeLabel')),
    discountValue: requiredNonNegativeNumber(t('couponForm.discountValueLabel')),
    usageLimit: optionalNonNegativeNumber(t('couponForm.usageLimitLabel')),
    usageLimitPerCustomer: optionalNonNegativeNumber(t('couponForm.usageLimitPerCustomerLabel')),
    minimumValue: optionalNonNegativeNumber(t('couponForm.minimumValueLabel')),
    minimumQuantity: optionalNonNegativeNumber(t('couponForm.minimumQuantityLabel')),
  })
)

const loadCustomers = async (searchTerm = '') => {
  loading.value = true;
  try {
    const response = await customerService.getCustomers(searchTerm, currentPage.value, 10);
    customers.value = response.content.map(customer => ({
      ...customer,
      label: `${customer.name || ''} (${customer.email || ''})`
    }));
    totalPages.value = response.totalPages;
  } catch (error) {
    console.error('Error loading customers:', error);
  } finally {
    loading.value = false;
  }
};

const addCouponUsage = () => {
  form.couponUsages.push({
    customerId: null,
    customerName: '',
    customerEmail: '',
    usageLimit: 1,
    usageCount: 0
  });

  formData.value = { ...form };
};

const removeCouponUsage = (index) => {
  form.couponUsages.splice(index, 1);

  formData.value = { ...form };
};

const updateCustomerInfo = (usage) => {
  const selectedCustomer = customers.value.find(c => c.id === usage.customerId);
  if (selectedCustomer) {
    usage.customerName = selectedCustomer.name;
    usage.customerEmail = selectedCustomer.email;
    formData.value = { ...form };
  }
};

const loadCoupon = async (couponId) => {
  try {
    loading.value = true;

    const coupon = await couponService.getCoupon(couponId);
    if (!coupon) throw new Error('Cupom não encontrado');

    const loadedForm = { ...defaultForm };

    Object.keys(loadedForm).forEach(key => {
      if (key in coupon) {
        if (key === 'startsAt' || key === 'endsAt') {
          loadedForm[key] = coupon[key] ? new Date(coupon[key]) : null;
        } else {
          loadedForm[key] = coupon[key];
        }
      }
    });

    const zeroFields = ['usageLimit', 'usageLimitPerCustomer', 'minimumValue', 'minimumQuantity'];
    zeroFields.forEach(field => {
      loadedForm[field] = loadedForm[field] === 0 ? null : loadedForm[field];
    });

    loadedForm.couponUsages = Array.isArray(coupon.couponUsages)
      ? coupon.couponUsages.map(usage => ({
        customerId: usage.customerId || '',
        usageLimit: parseInt(usage.usageLimit || 0, 10),
        usageCount: parseInt(usage.usageCount || 0, 10)
      }))
      : [];

    Object.assign(form, loadedForm);
    formData.value = { ...loadedForm };

    isEditing.value = true;
  } catch (error) {
    console.error('Erro ao carregar cupom:', error);
    toast.add({ severity: 'error', summary: t('couponForm.errorTitle'), detail: t('couponForm.errorLoadCoupon'), life: 3000 });
    router.push('/coupons');
  } finally {
    loading.value = false;
  }
};

const codeWasGenerated = ref(false);

const generateRandomCode = () => {
  form.code = couponService.generateRandomCode();
  codeWasGenerated.value = true;
  setTimeout(() => {
    const codeInput = document.getElementById('code');
    if (codeInput) {
      codeInput.dispatchEvent(new Event('input'));
      codeInput.dispatchEvent(new Event('blur'));
      codeInput.style.backgroundColor = 'var(--iluria-color-7)';
      codeInput.classList.remove('p-invalid');
      codeInput.classList.remove('input-error');
      const errorMessages = document.querySelectorAll('.p-message-error, .p-message-text');
      errorMessages.forEach(el => {
        if (el.textContent.includes(t('couponForm.codeLabel'))) {
          el.style.display = 'none';
        }
      });
    }
  }, 50);
};


const validateRequiredFields = () => {
  if (!form.code?.trim()) {
    showErrorToast(t('couponForm.codeLabel'));
    return false;
  }

  const isDiscountTypeExempt = ['FREE_SHIPPING', 'GIFT_COUPON'].includes(form.discountType);
  if (!isDiscountTypeExempt && !form.discountValue) {
    discountValueError.value = true;
    showErrorToast(t('couponForm.discountValueLabel'));
    return false;
  }
  discountValueError.value = false;

  const invalidUsages = form.couponUsages.filter(usage => !usage.customerId);
  if (invalidUsages.length > 0) {
    showErrorToast(t('couponForm.errorCustomerRequired'));
    return false;
  }

  return true;
};

const prepareCouponData = () => {
  const couponData = { ...form };

  if (couponData.startsAt) {
    couponData.startsAt = formatDateForAPI(couponData.startsAt);
  }
  if (couponData.endsAt) {
    couponData.endsAt = formatDateForAPI(couponData.endsAt);
  }

  ['usageLimit', 'usageLimitPerCustomer', 'minimumQuantity'].forEach(field => {
    if (couponData[field]) {
      couponData[field] = parseInt(couponData[field], 10);
    }
  });

  couponData.categoryIds = Array.isArray(couponData.categoryIds) ? [...couponData.categoryIds] : [];

  couponData.couponUsages = (couponData.couponUsages || []).map(usage => ({
    customerId: usage.customerId,
    usageLimit: parseInt(usage.usageLimit || 0, 10),
    usageCount: parseInt(usage.usageCount || 0, 10)
  }));

  return couponData;
};

const formatDateForAPI = (date) => {
  const formattedDate = new Date(date + ":00");
  return formattedDate.toISOString().replace(/\.\d{3}Z$/, "");
};

const showErrorToast = (fieldName) => {
  toast.add({
    severity: 'error',
    summary: t('couponForm.errorTitle'),
    detail: `${fieldName} ${t('validation.isRequired')}`,
    life: 3000
  });
};

const saveCoupon = async (e) => {
  if (e?.preventDefault) e.preventDefault();

  try {
    loading.value = true;

    if (!validateRequiredFields()) {
      loading.value = false;
      return;
    }

    const couponData = prepareCouponData();

    if (isEditing.value) {
      await couponService.updateCoupon(form.id, couponData);
      toast.add({ severity: 'success', summary: t('couponForm.successTitle'), detail: t('couponForm.successUpdateCoupon'), life: 3000 });
    } else {
      await couponService.createCoupon(couponData);
      toast.add({ severity: 'success', summary: t('couponForm.successTitle'), detail: t('couponForm.successCreateCoupon'), life: 3000 });
    }

    router.push('/coupons');
  } catch (error) {
    console.error('Erro ao salvar cupom:', error);
    toast.add({ severity: 'error', summary: t('couponForm.errorTitle'), detail: t('couponForm.errorSaveCoupon'), life: 3000 });
  } finally {
    loading.value = false;
  }
};

const goBackToList = () => {
  router.push('/coupons');
};

onMounted(() => {
  const couponId = route.params.id;
  if (couponId && couponId !== 'new') {
    isEditing.value = true;
    loadCoupon(couponId);
  } else {
    formData.value = { ...defaultForm };
    loading.value = false;
  }

  loadCustomers();
});
</script>

<style scoped>
.coupon-form-container {
  max-width: 1400px;
  margin: 0 auto;
  padding: 24px;
  background: var(--iluria-color-bg);
  color: var(--iluria-color-text-primary);
  min-height: 100vh;
}

/* Loading */
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 80px 24px;
  color: var(--iluria-color-text-secondary);
}

.spinner {
  width: 48px;
  height: 48px;
  border: 4px solid #f3f4f6;
  border-top: 4px solid #3b82f6;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 16px;
}

/* Header */
.form-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 32px;
  padding-bottom: 20px;
  border-bottom: 1px solid var(--iluria-color-border);
}

.header-content h1.page-title {
  font-size: 28px;
  font-weight: 700;
  color: var(--iluria-color-text-primary);
  margin: 0;
  line-height: 1.2;
}

.header-content .page-subtitle {
  font-size: 16px;
  color: var(--iluria-color-text-secondary);
  margin: 4px 0 0 0;
}

.header-actions {
  display: flex;
  gap: 12px;
  align-items: center;
}

/* Main Content */
.form-content {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

/* Field Labels */
.field-label {
  display: block;
  font-size: 14px;
  font-weight: 500;
  color: var(--iluria-color-text-primary);
  margin-bottom: 4px;
}

/* Usage Section */
.usage-section {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.usage-record {
  padding: 20px;
  border: 1px solid var(--iluria-color-border);
  border-radius: 12px;
  background: var(--iluria-color-container-bg);
  box-shadow: var(--iluria-shadow-sm);
  transition: box-shadow 0.2s ease;
}

.usage-record:hover {
  box-shadow: var(--iluria-shadow-md);
}

.usage-record-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
  padding-bottom: 12px;
  border-bottom: 1px solid var(--iluria-color-border);
}

.usage-record-title {
  font-size: 16px;
  font-weight: 600;
  color: var(--iluria-color-text-primary);
  margin: 0;
}

.customer-info {
  margin-top: 16px;
  padding: 16px;
  background: var(--iluria-color-surface);
  border: 1px solid var(--iluria-color-border);
  border-radius: 8px;
}

.usage-stats {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.usage-label {
  font-size: 12px;
  color: var(--iluria-color-text-secondary);
  display: block;
}

.usage-value {
  font-size: 14px;
  font-weight: 600;
  color: var(--iluria-color-text-primary);
}

.usage-progress {
  font-size: 12px;
  padding: 4px 8px;
  border-radius: 12px;
  font-weight: 500;
}

.usage-available {
  background: var(--iluria-color-success-light);
  color: var(--iluria-color-success-dark);
}

.usage-exceeded {
  background: var(--iluria-color-error-light);
  color: var(--iluria-color-error-dark);
}

.empty-usage {
  text-align: center;
  padding: 32px 24px;
  color: var(--iluria-color-text-secondary);
  background: var(--iluria-color-surface);
  border: 1px solid var(--iluria-color-border);
  border-radius: 8px;
}

.add-usage-button {
  margin-top: 16px;
  width: 100%;
  padding: 12px 16px;
}

/* Category Help */
.category-help {
  font-size: 12px;
  color: var(--iluria-color-text-secondary);
  margin-top: 8px;
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }

  to {
    transform: rotate(360deg);
  }
}

/* Responsive */
@media (max-width: 768px) {
  .coupon-form-container {
    padding: 16px;
  }

  .form-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 16px;
  }

  .header-content h1.page-title {
    font-size: 24px;
  }

  .header-content .page-subtitle {
    font-size: 14px;
  }

  .header-actions {
    width: 100%;
    justify-content: space-between;
  }
}

@media (max-width: 480px) {
  .header-actions {
    flex-direction: column;
    align-items: stretch;
    gap: 12px;
  }

  .header-content h1.page-title {
    font-size: 22px;
  }
}
</style>
