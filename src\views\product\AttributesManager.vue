<template>
  <div class="page-container">
    <!-- Header da página -->
    <IluriaHeader
      :title="t('product.attributes.title')"
      subtitle="Gerencie os atributos e filtros disponíveis para os produtos"
      :showAdd="true"
      addText="Criar Atributo"
      @add-click="openCreateModal"
    />

    <!-- Main Content -->
    <ViewContainer
      :title="t('product.attributes.title')"
      :icon="TagIcon"
      iconColor="blue"
    >
      <!-- Filtros e busca -->
      <div class="p-6 mb-6 rounded-lg transition-all duration-200" style="background: var(--iluria-color-container-bg); box-shadow: var(--iluria-shadow-sm);">
        <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
          <!-- Busca -->
          <div class="relative">
            <input
              type="text"
              v-model="searchQuery"
              @input="loadAttributes"
              placeholder="Buscar atributos..."
              class="w-full px-4 py-3 pl-10 rounded-lg focus:outline-none focus:ring-2 focus:ring-[var(--color-primary)] text-[var(--color-text)] transition-all duration-200"
              style="background: var(--iluria-color-container-bg); border: 1px solid var(--iluria-color-border);"
            />
            <svg class="w-5 h-5 text-[var(--color-text-light)] absolute left-3 top-3.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"/>
            </svg>
          </div>

          <!-- Filtro por categoria -->
          <CategoryDropdown
            v-model="selectedCategoryFilter"
            :categories="rawCategories"
            placeholder="Todas as categorias"
            :showAllOption="true"
            allOptionText="Todas as categorias"
            @update:modelValue="loadAttributes"
          />

          <!-- Botão limpar filtros + Estatísticas -->
          <div class="flex items-center space-x-4 text-sm text-[var(--color-text-light)]">
            <!-- Botão limpar filtros -->
            <button
              @click="clearFilters"
              type="button"
              class="p-2 text-[var(--color-text-light)] hover:text-[var(--color-text)] rounded-full hover:bg-[var(--color-background-alt)] focus:outline-none active:scale-95 transition-all duration-150"
              :title="'Limpar filtros'"
              style="outline: none !important; box-shadow: none !important;"
            >
              <!-- Ícone funil com X -->
              <svg xmlns="http://www.w3.org/2000/svg" class="w-5 h-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <!-- Funil -->
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 4a1 1 0 011-1h16a1 1 0 011 1v2a1 1 0 01-.293.707L14 12.414V19a1 1 0 01-.553.894l-4 2A1 1 0 018 21v-8.586L2.293 6.707A1 1 0 012 6V4z" />
                <!-- X -->
                <line x1="9" y1="9" x2="15" y2="15" stroke="currentColor" stroke-width="2" stroke-linecap="round" />
                <line x1="15" y1="9" x2="9" y2="15" stroke="currentColor" stroke-width="2" stroke-linecap="round" />
              </svg>
            </button>

            <!-- Estatísticas -->
            <span>Total: {{ totalAttributes }}</span>
            <span>•</span>
            <span>Ativos: {{ activeAttributes }}</span>
          </div>
        </div>
      </div>

    <!-- Loading -->
    <div v-if="loading" class="flex justify-center items-center py-12">
      <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
    </div>

    <!-- Lista de atributos -->
    <div v-else-if="attributes.length > 0" class="space-y-6">
      <!-- Grupos por categoria hierárquica -->
      <div v-for="category in hierarchicalCategories" :key="category.id" class="space-y-4">
        <!-- Categoria pai -->
        <div class="space-y-3">
          <!-- Header da categoria pai -->
          <div 
            @click="toggleCategoryCollapse(category.id)"
            class="flex items-center justify-between p-4 rounded-lg cursor-pointer transition-all duration-200"
            style="background: var(--iluria-color-sidebar-bg); box-shadow: var(--iluria-shadow-sm);"
            @mouseenter="$event.target.style.boxShadow = 'var(--iluria-shadow-md)'"
            @mouseleave="$event.target.style.boxShadow = 'var(--iluria-shadow-sm)'"
          >
            <div class="flex items-center space-x-3">
              <div class="flex items-center justify-center w-10 h-10 bg-[var(--color-primary)]/10 rounded-lg">
                <svg class="w-5 h-5 text-[var(--color-primary)]" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"/>
                </svg>
              </div>
              <div>
                <h3 class="text-lg font-semibold text-[var(--color-text)]">{{ category.name }}</h3>
                <div class="flex items-center space-x-3 text-sm text-[var(--color-text-light)]">
                  <span>{{ category.totalAttributes }} atributo(s) total</span>
                  <span v-if="category.children && category.children.length > 0" class="flex items-center space-x-1">
                    <span class="text-[var(--color-text-lighter)]">•</span>
                    <span>{{ category.children.length }} subcategoria(s)</span>
                  </span>
                </div>
              </div>
            </div>
            
            <!-- Ícone de collapse -->
            <svg 
              :class="[
                'w-5 h-5 text-[var(--color-text-light)] transition-transform duration-200',
                collapsedCategories[category.id] ? 'transform rotate-180' : ''
              ]" 
              fill="none" 
              stroke="currentColor" 
              viewBox="0 0 24 24"
            >
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"/>
            </svg>
          </div>
          
          <!-- Conteúdo da categoria pai (sempre visível quando não colapsada) -->
          <div v-show="!collapsedCategories[category.id]" class="space-y-4">
            <!-- Atributos diretos da categoria pai -->
            <div v-if="category.directAttributes.length > 0" class="ml-4 space-y-3">
              <div
                v-for="attribute in category.directAttributes"
                :key="attribute.id"
                class="rounded-lg transition-all duration-200"
                style="background: var(--iluria-color-container-bg); box-shadow: var(--iluria-shadow-sm);"
                @mouseenter="$event.target.style.boxShadow = 'var(--iluria-shadow-md)'"
                @mouseleave="$event.target.style.boxShadow = 'var(--iluria-shadow-sm)'"
              >
                <div class="p-6">
                  <div class="flex items-center justify-between">
                    <div class="flex items-center space-x-4">
                      <!-- Ícone do atributo -->
                      <div class="flex items-center justify-center w-12 h-12 bg-[var(--color-primary)]/10 rounded-lg">
                        <svg class="w-6 h-6 text-[var(--color-primary)]" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 7h.01M7 3h5c.512 0 1.024.195 1.414.586l7 7a2 2 0 010 2.828l-7 7a2 2 0 01-2.828 0l-7-7A1.994 1.994 0 013 12V7a4 4 0 014-4z"/>
                        </svg>
                      </div>
                      
                      <div>
                        <h4 class="text-lg font-semibold text-[var(--color-text)]">{{ attribute.name }}</h4>
                        <div class="flex items-center space-x-4 mt-1">
                          <span class="text-sm text-[var(--color-text-light)]">
                            {{ attribute.valuesCount || 0 }} valor(es)
                          </span>
                          <span class="text-sm text-[var(--color-text-lighter)]">•</span>
                          <span class="text-sm text-[var(--color-text-light)]">
                            {{ attribute.usageCount || 0 }} uso(s)
                          </span>
                        </div>
                      </div>
                    </div>

                    <div class="flex items-center space-x-3">
                      <!-- Badge de status -->
                      <span 
                        :class="[
                          'inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium',
                          attribute.active ? 'bg-[var(--color-success)]/10 text-[var(--color-success)]' : 'bg-[var(--color-text-light)]/10 text-[var(--color-text-light)]'
                        ]"
                      >
                        {{ attribute.active ? 'Ativo' : 'Inativo' }}
                      </span>

                      <!-- Botões de ação -->
                      <button
                        @click="editAttribute(attribute)"
                        class="p-2 text-[var(--color-text-light)] hover:text-[var(--color-primary)] rounded-full hover:bg-[var(--color-primary)]/10"
                      >
                        <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"/>
                        </svg>
                      </button>
                      
                      <button
                        @click="confirmDeleteAttribute(attribute)"
                        class="p-2 text-[var(--color-text-light)] hover:text-[var(--color-danger)] rounded-full hover:bg-[var(--color-danger)]/10"
                      >
                        <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"/>
                        </svg>
                      </button>
                    </div>
                  </div>

                  <!-- Valores do atributo -->
                  <div v-if="attribute.values && attribute.values.length > 0" class="mt-4">
                    <p class="text-sm font-medium text-[var(--color-text)] mb-2">Valores:</p>
                    <div class="flex flex-wrap gap-2">
                      <span
                        v-for="value in attribute.values.slice(0, 10)"
                        :key="value.id"
                        class="inline-flex items-center px-2.5 py-1 rounded-full text-xs font-medium bg-[var(--color-primary)]/10 text-[var(--color-primary)]"
                      >
                        {{ value.value }}
                        <span v-if="value.usageCount" class="ml-1 text-[var(--color-primary)]">({{ value.usageCount }})</span>
                      </span>
                      <span
                        v-if="attribute.values.length > 10"
                        class="inline-flex items-center px-2.5 py-1 rounded-full text-xs font-medium bg-[var(--color-text-light)]/10 text-[var(--color-text-light)]"
                      >
                        +{{ attribute.values.length - 10 }} mais
                      </span>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <!-- Subcategorias (renderização recursiva para N níveis) -->
            <div v-if="category.children.length > 0" class="ml-8 space-y-4">
              <div v-for="subcategory in category.children" :key="subcategory.id" class="space-y-3">
                <!-- Renderização recursiva de subcategorias -->
                <CategoryHierarchyItem 
                  :category="subcategory" 
                  :level="1"
                  :collapsed-categories="collapsedCategories"
                  @toggle-collapse="toggleCategoryCollapse"
                  @edit-attribute="editAttribute"
                  @delete-attribute="confirmDeleteAttribute"
                />
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Estado vazio -->
    <div v-else-if="!loading" class="text-center py-12 rounded-lg" style="background: var(--iluria-color-container-bg); box-shadow: var(--iluria-shadow-sm);">
      <div class="w-16 h-16 mx-auto mb-4 text-[var(--color-text-light)]">
        <svg fill="none" stroke="currentColor" viewBox="0 0 24 24" class="w-full h-full">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="1.5" d="M7 7h.01M7 3h5c.512 0 1.024.195 1.414.586l7 7a2 2 0 010 2.828l-7 7a2 2 0 01-2.828 0l-7-7A1.994 1.994 0 013 12V7a4 4 0 014-4z"/>
        </svg>
      </div>
      <h3 class="text-lg font-medium text-[var(--color-text)] mb-2">Nenhum atributo encontrado</h3>
      <p class="text-[var(--color-text-light)] mb-6">Crie seu primeiro atributo para organizar melhor seus produtos</p>
      <IluriaButton 
        :hugeIcon="TagIcon" 
        @click="openCreateModal"
      >
        Criar Primeiro Atributo
      </IluriaButton>
    </div>

    <!-- Paginação -->
    <div v-if="totalPages > 1" class="flex justify-center mt-6">
      <nav class="flex items-center space-x-2">
        <button
          @click="changePage(currentPage - 1)"
          :disabled="currentPage <= 1"
          class="px-3 py-2 text-sm font-medium text-[var(--color-text-light)] rounded-md disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-200"
          style="background: var(--iluria-color-container-bg); border: 1px solid var(--iluria-color-border);"
          @mouseenter="!$event.target.disabled && ($event.target.style.background = 'var(--iluria-color-hover)')"
          @mouseleave="$event.target.style.background = 'var(--iluria-color-container-bg)'"
        >
          Anterior
        </button>
        
        <span class="px-3 py-2 text-sm text-[var(--color-text)]">
          Página {{ currentPage }} de {{ totalPages }}
        </span>
        
        <button
          @click="changePage(currentPage + 1)"
          :disabled="currentPage >= totalPages"
          class="px-3 py-2 text-sm font-medium text-[var(--color-text-light)] rounded-md disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-200"
          style="background: var(--iluria-color-container-bg); border: 1px solid var(--iluria-color-border);"
          @mouseenter="!$event.target.disabled && ($event.target.style.background = 'var(--iluria-color-hover)')"
          @mouseleave="$event.target.style.background = 'var(--iluria-color-container-bg)'"
        >
          Próxima
        </button>
      </nav>
    </div>

    <!-- Modal de criar/editar atributo usando componente reutilizável -->
    <AttributeModal
      :visible="showModal"
      :is-editing="isEditing"
      :saving="saving"
      :attribute="currentAttribute"
      :categories="rawCategories"
      :mode="'attribute'"
      @update:visible="showModal = $event"
      @save="handleSaveAttribute"
      @cancel="closeModal"
    />

    <!-- Modal de confirmação -->
    <IluriaConfirmationModal
      :is-visible="showConfirmModal"
      :title="`Excluir Atributo '${attributeToDelete?.name}'`"
      :message="attributeUsage.length > 0 ? `Este atributo está sendo usado em ${attributeUsage.length} produto(s). A exclusão removerá o atributo desses produtos.` : `Tem certeza que deseja excluir o atributo '${attributeToDelete?.name}'? Esta ação não pode ser desfeita.`"
      confirm-text="Excluir"
      cancel-text="Cancelar"
      type="error"
      @confirm="confirmDelete"
      @cancel="cancelDelete"
    >
      <template #extra-content v-if="attributeUsage.length > 0">
        <div class="mt-4 p-3 bg-gray-50 rounded-md border border-gray-200">
            <p class="text-sm font-semibold mb-2 text-gray-800">Produtos afetados:</p>
            <ul class="list-disc list-inside space-y-1 text-sm text-gray-700 max-h-32 overflow-y-auto pr-2">
                <li v-for="product in attributeUsage.slice(0, 5)" :key="product.productId">
                    {{ product.productName }}
                </li>
            </ul>
            <p v-if="attributeUsage.length > 5" class="text-sm text-gray-600 mt-2">
                ... e mais {{ attributeUsage.length - 5 }} produto(s).
            </p>
        </div>
      </template>
    </IluriaConfirmationModal>
    </ViewContainer>
  </div>
</template>

<script setup>
import { ref, onMounted, computed, watch, nextTick } from 'vue';
import { useI18n } from 'vue-i18n';
import { useToast } from '@/services/toast.service';
import IluriaButton from '@/components/iluria/IluriaButton.vue';
import IluriaHeader from '@/components/iluria/IluriaHeader.vue';
import { TagIcon } from '@hugeicons-pro/core-bulk-rounded';
import { attributesApi } from '@/services/attributes.service';
import { categoryService } from '@/services/category.service';
import CategoryDropdown from '@/components/iluria/CategoryDropdown.vue';
import IluriaConfirmationModal from '@/components/iluria/modal/IluriaConfirmationModal.vue';
import CategoryHierarchyItem from '@/components/products/CategoryHierarchyItem.vue';
import AttributeModal from '@/components/products/AttributeModal.vue';
import ViewContainer from '@/components/layout/ViewContainer.vue';

const { t } = useI18n();
const { showSuccess, showError } = useToast();

// Estado da página
const loading = ref(false);
const saving = ref(false);
const attributes = ref([]);
const categories = ref([]);
const rawCategories = ref([]);
const searchQuery = ref('');
const selectedCategoryFilter = ref(null);
const collapsedCategories = ref({});
const categoryNamesMap = ref({});

// Paginação
const currentPage = ref(1);
const pageSize = ref(20);
const totalAttributes = ref(0);
const totalPages = ref(0);

// Modal
const showModal = ref(false);
const isEditing = ref(false);
const currentAttribute = ref({
  name: '',
  categoryId: null,
  active: true,
  initialValues: ''
});

// Modal de confirmação
const showConfirmModal = ref(false);
const attributeToDelete = ref(null);
const attributeUsage = ref([]);

// Computed
const activeAttributes = computed(() => {
  return attributes.value.filter(attr => attr.active).length;
});

// Hierarquia de categorias para exibição
const hierarchicalCategories = computed(() => {
  if (!attributes.value || attributes.value.length === 0) return [];
  

  
  // Primeiro, criar um mapa de categorias com seus atributos
  const categoryMap = {};
  
  attributes.value.forEach(attribute => {
    const categoryId = attribute.categoryId;
    const categoryName = categoryNamesMap.value[categoryId] || categoryId || 'Sem Categoria';
    
    if (!categoryMap[categoryId]) {
      categoryMap[categoryId] = {
        id: categoryId,
        name: categoryName,
        directAttributes: [],
        children: [],
        totalAttributes: 0
      };
    }
    
    categoryMap[categoryId].directAttributes.push(attribute);
    categoryMap[categoryId].totalAttributes++;
  });
  
  
  // Se há um filtro de categoria específico aplicado, mostrar apenas essa categoria
  if (selectedCategoryFilter.value && selectedCategoryFilter.value !== null) {
    const filteredCategoryData = categoryMap[selectedCategoryFilter.value];
    
    // Encontrar a categoria nas rawCategories para obter informações completas
    const findCategoryInRaw = (categories, targetId) => {
      for (const cat of categories) {
        if (cat.id === targetId) return cat;
        if (cat.children) {
          const found = findCategoryInRaw(cat.children, targetId);
          if (found) return found;
        }
      }
      return null;
    };
    
    const categoryInfo = findCategoryInRaw(rawCategories.value, selectedCategoryFilter.value);
    
    if (categoryInfo) {
      // Função recursiva para construir subcategorias com atributos
      const buildSubcategoriesWithAttributes = (category, level = 1) => {
        const children = [];
        
        if (category.children && category.children.length > 0) {
          category.children.forEach(child => {
            const childData = categoryMap[child.id];
            if (childData && childData.totalAttributes > 0) {
              children.push({
                id: child.id,
                name: child.title || child.name,
                directAttributes: childData.directAttributes,
                children: buildSubcategoriesWithAttributes(child, level + 1),
                totalAttributes: childData.totalAttributes,
                level
              });
            }
          });
        }
        
        return children;
      };
      
      const result = [{
        id: selectedCategoryFilter.value,
        name: categoryInfo.title || categoryInfo.name,
        directAttributes: filteredCategoryData ? filteredCategoryData.directAttributes : [],
        children: buildSubcategoriesWithAttributes(categoryInfo),
        totalAttributes: filteredCategoryData ? filteredCategoryData.totalAttributes : 0,
        level: 0
      }];
      
      // Calcular total de atributos incluindo subcategorias
      const calculateTotalWithChildren = (item) => {
        let total = item.directAttributes.length;
        item.children.forEach(child => {
          total += calculateTotalWithChildren(child);
        });
        return total;
      };
      
      result[0].totalAttributes = calculateTotalWithChildren(result[0]);
      

      return result;
    }
    return [];
  }
  
  // Caso contrário, mostrar hierarquia completa
  const hierarchical = [];
  const processedCategories = new Set();
  
  // Função para construir hierarquia recursivamente (suporta N níveis)
  const buildHierarchy = (categories, level = 0) => {
    const result = [];
    
    categories.forEach(category => {
      if (processedCategories.has(category.id)) return;
      
      const categoryData = categoryMap[category.id];
      
      const hierarchyItem = {
        id: category.id,
        name: category.title || category.name,
        directAttributes: categoryData ? categoryData.directAttributes : [],
        children: [],
        totalAttributes: 0,
        level
      };
      
      // Processar filhos recursivamente (N níveis)
      if (category.children && category.children.length > 0) {
        hierarchyItem.children = buildHierarchy(category.children, level + 1);
      }
      
      // Calcular total de atributos (diretos + dos filhos recursivamente)
      const calculateTotalAttributes = (item) => {
        let total = item.directAttributes.length;
        item.children.forEach(child => {
          total += calculateTotalAttributes(child);
        });
        return total;
      };
      
      hierarchyItem.totalAttributes = calculateTotalAttributes(hierarchyItem);
      
      // Só adicionar se tem atributos diretos ou filhos com atributos
      if (hierarchyItem.totalAttributes > 0) {
        result.push(hierarchyItem);
        processedCategories.add(category.id);
      }
    });
    
    return result;
  };
  
  // Construir hierarquia a partir das categorias raiz
  const hierarchy = buildHierarchy(rawCategories.value);
  
  // Adicionar categoria "ALL" se existir
  if (categoryMap['ALL']) {
    hierarchy.unshift({
      id: 'ALL',
      name: 'Todas as Categorias',
      directAttributes: categoryMap['ALL'].directAttributes,
      children: [],
      totalAttributes: categoryMap['ALL'].totalAttributes,
      level: 0
    });
  }
  
  return hierarchy;
});

// Função para inicializar estados de collapse
const initializeCollapseStates = () => {
  // Pais abertos por padrão, filhos colapsados por padrão
  const setDefaultStates = (categories, isChild = false) => {
    categories.forEach(category => {
      // Pais (level 0) abertos, filhos (level > 0) colapsados
      collapsedCategories.value[category.id] = isChild;
      
      if (category.children && category.children.length > 0) {
        setDefaultStates(category.children, true);
      }
    });
  };
  
  setDefaultStates(hierarchicalCategories.value);
};

// Watcher para reinicializar estados quando dados mudarem
watch(() => hierarchicalCategories.value, () => {
  nextTick(() => {
    initializeCollapseStates();
  });
}, { immediate: false });

// Função para alternar collapse de categoria
const toggleCategoryCollapse = (categoryId) => {
  collapsedCategories.value[categoryId] = !collapsedCategories.value[categoryId];
};

// Função para criar mapa de nomes das categorias
const createCategoryNamesMap = (categories) => {
  const map = {};
  
  const processCategory = (category) => {
    if (category.id) {
      map[category.id] = category.name || category.title;
    }
    
    if (category.children && Array.isArray(category.children)) {
      category.children.forEach(processCategory);
    }
  };
  
  if (Array.isArray(categories)) {
    categories.forEach(processCategory);
  }
  
  return map;
};

// Função para carregar categorias
const loadCategories = async () => {
  try {
    const response = await categoryService.fetchCategories();
    
    // Armazenar categorias brutas para o CategoryDropdown
    rawCategories.value = response.content || response || [];
    
    // Criar mapa de nomes das categorias
    categoryNamesMap.value = createCategoryNamesMap(rawCategories.value);
    
    // Processar categorias para ter estrutura plana e com propriedade 'name'
    const processCategories = (categories) => {
      const processed = [];
      
      if (Array.isArray(categories)) {
        categories.forEach(category => {
          // Adicionar categoria principal
          processed.push({
            id: category.id,
            name: category.name || category.title,
            level: 0
          });
          
          // Adicionar subcategorias se existirem
          if (category.children && Array.isArray(category.children)) {
            category.children.forEach(subCategory => {
              processed.push({
                id: subCategory.id,
                name: `-- ${subCategory.name || subCategory.title}`,
                level: 1
              });
              
              // Adicionar sub-subcategorias se existirem
              if (subCategory.children && Array.isArray(subCategory.children)) {
                subCategory.children.forEach(subSubCategory => {
                  processed.push({
                    id: subSubCategory.id,
                    name: `---- ${subSubCategory.name || subSubCategory.title}`,
                    level: 2
                  });
                });
              }
            });
          }
        });
      }
      
      return processed;
    };
    
    categories.value = processCategories(rawCategories.value);
    

  } catch (error) {
    console.error('Erro ao carregar categorias:', error);
    showError('Erro ao carregar categorias');
    rawCategories.value = [];
  }
};

// Função para obter todas as subcategorias de uma categoria
const getAllSubcategoryIds = (categoryId, categories = rawCategories.value) => {
  const subcategoryIds = [categoryId]; // Incluir a própria categoria
  
  const findSubcategories = (cats) => {
    cats.forEach(cat => {
      if (cat.id === categoryId) {
        // Encontrou a categoria, adicionar todos os filhos recursivamente
        const addChildren = (category) => {
          if (category.children && category.children.length > 0) {
            category.children.forEach(child => {
              subcategoryIds.push(child.id);
              addChildren(child); // Recursivo para N níveis
            });
          }
        };
        addChildren(cat);
      } else if (cat.children && cat.children.length > 0) {
        findSubcategories(cat.children);
      }
    });
  };
  
  findSubcategories(categories);
  return subcategoryIds;
};

// Função para carregar atributos
const loadAttributes = async () => {
  try {
    loading.value = true;
    
    let response;
    
    // Se há uma categoria selecionada, incluir todas as subcategorias
    if (selectedCategoryFilter.value) {
      const allCategoryIds = getAllSubcategoryIds(selectedCategoryFilter.value);

      
      const params = {
        page: currentPage.value - 1,
        size: pageSize.value,
        name: searchQuery.value || undefined,
        categoryIds: allCategoryIds
      };
      

      response = await attributesApi.getAllByMultipleCategories(params);
    } else {
      // Sem filtro de categoria, usar método original
      const params = {
        page: currentPage.value - 1,
        size: pageSize.value,
        name: searchQuery.value || undefined
      };
      

      response = await attributesApi.getAll(params);
    }
    

    
    // Tratar diferentes formatos de resposta
    if (response && response.data) {
      if (response.data.content) {
        // Resposta paginada
        attributes.value = response.data.content || [];
        totalAttributes.value = response.data.page?.totalElements || response.data.totalElements || 0;
        totalPages.value = response.data.page?.totalPages || response.data.totalPages || 1;
      } else if (Array.isArray(response.data)) {
        // Resposta direta como array
        attributes.value = response.data;
        totalAttributes.value = response.data.length;
        totalPages.value = 1;
      } else {
        // Resposta como objeto único
        attributes.value = [response.data];
        totalAttributes.value = 1;
        totalPages.value = 1;
      }
    } else if (Array.isArray(response)) {
      // Resposta direta como array
      attributes.value = response;
      totalAttributes.value = response.length;
      totalPages.value = 1;
    } else {
      // Fallback vazio
      attributes.value = [];
      totalAttributes.value = 0;
      totalPages.value = 1;
    }
    

  } catch (error) {
    console.error('Erro ao carregar atributos:', error);
    showError('Erro ao carregar atributos');
    
    // Fallback vazio em caso de erro
    attributes.value = [];
    totalAttributes.value = 0;
    totalPages.value = 1;
  } finally {
    loading.value = false;
  }
};

// Função para mudar página
const changePage = (page) => {
  if (page >= 1 && page <= totalPages.value) {
    currentPage.value = page;
    loadAttributes();
  }
};

// Função para limpar filtros de busca e categoria
const clearFilters = () => {
  if (searchQuery.value) {
    searchQuery.value = '';
  }
  if (selectedCategoryFilter.value) {
    selectedCategoryFilter.value = null;
  }
  currentPage.value = 1;
  loadAttributes();
};

// Funções do modal
const openCreateModal = () => {
  isEditing.value = false;
  currentAttribute.value = {
    name: '',
    categoryId: selectedCategoryFilter.value || null,
    active: true,
    values: []
  };
  showModal.value = true;
};

const editAttribute = (attribute) => {
  isEditing.value = true;
  
  // Preparar valores como array para o novo modal
  let valuesArray = [];
  if (attribute.values && Array.isArray(attribute.values)) {
    valuesArray = attribute.values.map(v => v.value || v);
  }
  
  currentAttribute.value = {
    id: attribute.id,
    name: attribute.name,
    categoryId: attribute.categoryId || attribute.category?.id,
    active: attribute.active,
    values: valuesArray
  };
  
  showModal.value = true;
};

const closeModal = () => {
  showModal.value = false;
  isEditing.value = false;
  currentAttribute.value = {
    name: '',
    categoryId: null,
    active: true,
    values: []
  };
};

// Nova função para lidar com o modal reutilizável
const handleSaveAttribute = async (attributeData) => {
  if (!attributeData.name || !attributeData.categoryId) {
    showError('Nome do atributo e categoria são obrigatórios');
    return;
  }
  
  try {
    saving.value = true;
    
    if (isEditing.value) {
      const attributeId = currentAttribute.value.id;

      // 1. Atualizar metadados do atributo (nome, categoria, etc.)
      const updateData = {
        name: attributeData.name,
        categoryId: attributeData.categoryId,
        active: attributeData.active
      };
      await attributesApi.update(attributeId, updateData);
      
      // 2. Sincronizar valores (Adicionar e Remover)
      const currentAttributeData = await attributesApi.getAttributeWithValues(attributeId);
      const existingValueObjects = currentAttributeData.values || [];
      const submittedValueStrings = attributeData.values || [];

      // Identificar valores a serem REMOVIDOS
      const valuesToDelete = existingValueObjects.filter(
        v => !submittedValueStrings.includes(v.value)
      );

      // Identificar valores a serem CRIADOS
      const existingValueStrings = existingValueObjects.map(v => v.value);
      const valuesToCreate = submittedValueStrings.filter(
        v => !existingValueStrings.includes(v)
      );
      
      // Executar operações na API de forma sequencial para evitar race conditions
      // 1. Deletar primeiro
      const deletePromises = valuesToDelete.map(value => attributesApi.deleteValue(value.id));
      await Promise.all(deletePromises);
      
      // 2. Criar depois
      const createPromises = valuesToCreate.map(value => attributesApi.createValue({
        value: value,
        attributeId: attributeId
      }));
      await Promise.all(createPromises);
      
      showSuccess('Atributo atualizado com sucesso');
    } else {
      const createData = {
        name: attributeData.name,
        categoryId: attributeData.categoryId,
        active: attributeData.active,
        initialValues: attributeData.initialValues || []
      };
      
      await attributesApi.create(createData);
      showSuccess('Atributo criado com sucesso');
    }
    
    closeModal();
    await loadAttributes();
  } catch (error) {
    let errorMessage = isEditing.value ? 'Erro ao atualizar atributo' : 'Erro ao criar atributo';
    
    if (error.response?.data?.message) {
      errorMessage += ': ' + error.response.data.message;
    } else if (error.response?.status === 500) {
      errorMessage += ': Erro interno do servidor';
    } else if (error.response?.status === 404) {
      errorMessage += ': Endpoint não encontrado';
    } else if (error.response?.status === 400) {
      errorMessage += ': Dados inválidos';
    }
    
    showError(errorMessage);
  } finally {
    saving.value = false;
  }
};

const confirmDeleteAttribute = async (attribute) => {
  attributeToDelete.value = attribute;
  
  try {
    // Verificar uso do atributo antes de abrir o modal
    const usage = await attributesApi.getUsage(attribute.id);
    attributeUsage.value = usage || [];
  } catch (error) {
    // Se a API de uso falhar, proceder com a exclusão normal
    console.error("Falha ao verificar uso do atributo:", error);
    attributeUsage.value = [];
  }
  
  showConfirmModal.value = true;
};

const confirmDelete = async () => {
  try {
    const forceDelete = attributeUsage.value.length > 0;
    await attributesApi.delete(attributeToDelete.value.id, forceDelete);
    showSuccess('Atributo excluído com sucesso');
    cancelDelete(); // Limpa estado e fecha modal
    loadAttributes();
  } catch (error) {
    console.error('Erro ao excluir atributo:', error);
    showError('Erro ao excluir atributo');
    cancelDelete();
  }
};

const cancelDelete = () => {
  showConfirmModal.value = false;
  attributeToDelete.value = null;
  attributeUsage.value = []; // Limpar a lista de uso
};

// Lifecycle
onMounted(async () => {
  await loadCategories();
  await loadAttributes();
  initializeCollapseStates();
});
</script>

<style scoped>
.page-container {
  padding: 24px;
  max-width: 1400px;
  margin: 0 auto;
}



/* Responsive */
@media (max-width: 768px) {
  .page-container {
    padding: 16px;
  }
  

}

@media (max-width: 640px) {

}

@media (max-width: 480px) {
  .page-container {
    padding: 12px;
  }
  
  .page-title {
    font-size: 22px;
  }
  
  .page-subtitle {
    font-size: 14px;
    margin: 8px 0 0 0;
  }
}
</style> 
