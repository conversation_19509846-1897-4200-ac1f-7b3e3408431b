// Gera um avatar padrão baseado no nome ou tipo
export const generateDefaultAvatar = (name = '', type = 'user') => {
  // Cores padrão para avatares
  const colors = [
    '#3B82F6', // blue
    '#10B981', // emerald
    '#8B5CF6', // violet
    '#F59E0B', // amber
    '#EF4444', // red
    '#06B6D4', // cyan
    '#84CC16', // lime
    '#F97316'  // orange
  ];

  // Pega as iniciais do nome
  const getInitials = (fullName) => {
    if (!fullName) return type === 'product' ? 'P' : 'U';
    
    return fullName
      .split(' ')
      .filter(word => word.length > 0)
      .slice(0, 2)
      .map(word => word[0])
      .join('')
      .toUpperCase();
  };

  // Seleciona cor baseada no nome
  const getColor = (text) => {
    let hash = 0;
    for (let i = 0; i < text.length; i++) {
      hash = text.charCodeAt(i) + ((hash << 5) - hash);
    }
    return colors[Math.abs(hash) % colors.length];
  };

  const initials = getInitials(name);
  const backgroundColor = getColor(name || type);
  
  // Gera SVG do avatar
  const svgAvatar = `
    <svg width="40" height="40" viewBox="0 0 40 40" xmlns="http://www.w3.org/2000/svg">
      <rect width="40" height="40" fill="${backgroundColor}" rx="20"/>
      <text 
        x="20" 
        y="25" 
        text-anchor="middle" 
        fill="white" 
        font-family="Arial, sans-serif" 
        font-size="14" 
        font-weight="600"
      >
        ${initials}
      </text>
    </svg>
  `;

  // Converte SVG para data URL
  return `data:image/svg+xml;base64,${btoa(svgAvatar)}`;
};

// Gera avatar específico para produtos
export const generateProductAvatar = (productName = '') => {
  return generateDefaultAvatar(productName, 'product');
};

// Gera avatar específico para clientes
export const generateCustomerAvatar = (customerName = '') => {
  return generateDefaultAvatar(customerName, 'user');
};

// Hook para tratar erro de imagem e usar avatar padrão
export const handleImageError = (event, fallbackName = '', type = 'user') => {
  const img = event.target;
  const avatar = generateDefaultAvatar(fallbackName, type);
  img.src = avatar;
  img.onerror = null; // Evita loop infinito
}; 