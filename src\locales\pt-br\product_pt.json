{"title": "<PERSON><PERSON><PERSON>", "add": "<PERSON><PERSON><PERSON><PERSON>", "description": "Descrição do Produto", "edit": "<PERSON><PERSON>", "delete": "Excluir", "products": "<PERSON><PERSON><PERSON>", "noProducts": "Nenhum produto encontrado para esta loja", "noProductsAdded": "Nenhum produto adicionado", "loadError": "Erro ao carregar produtos", "viewProducts": "<PERSON><PERSON> Pro<PERSON>", "image": "Imagem", "name": "Nome do Produto", "sku": "SKU", "price": "Preço", "stock": "Estoque", "addProduct": "<PERSON><PERSON><PERSON><PERSON>", "remove": "Remover", "onlyInStock": "Apenas com estoque", "noProductsFound": "Nenhum produto encontrado", "confirmDelete": "Tem certeza que deseja excluir {name}?", "confirmDeleteTitle": "Confirmar exclusão", "confirmClone": "Tem certeza que deseja clonar este produto?", "confirmCloneTitle": "Confirmar clonagem", "cloneSuccess": "Produto clonado com sucesso!", "clone": "Clonar produto", "skuCode": "Código SKU", "barCode": "Código de <PERSON>", "shortDescription": "Descrição Resumida", "pricesAndStock": "Preços e Estoque", "originalPrice": "Preço Original", "costPrice": "Preço de Custo", "stockQuantity": "Quantidade em Estoque", "weight": "Peso", "boxLength": "Comprimento", "boxWidth": "<PERSON><PERSON><PERSON>", "boxDepth": "Profundidade", "productData": "Dados do Produto", "tags": "Tags", "variations": "Variações", "customFields": "Campos Personalizados (Opcional)", "customFieldsSubtitle": "Crie e configure campos personalizados para o produto", "pricesAndStockSubtitle": "Informações sobre o estoque e o preço", "highlight": "Marcar como Destaque", "newTag": "Marcar como Novidade", "supplierData": "Dados do fornecedor (opcional)", "supplierDataSubtitle": "Use os campos abaixo para manter registro das informações do fornecedor deste produto. Estas informações não são mostradas na loja e servem apenas para seu melhor controle interno.", "supplierName": "Nome do fornecedor (opcional)", "supplierLink": "Link para o produto ou fornecedor (opcional)", "supplierNotes": "Anotações (opcional)", "selectStatus": "Selecione um status", "status": "Visibilidade na Loja", "statusDraft": "Invisível", "statusActive": "Visível", "typePhysical": "Físico", "typeDigital": "Digital", "typeGift": "<PERSON><PERSON><PERSON>", "hasVariation": "Produto possui Variaç<PERSON>", "hasVariantionTooltip": "Produtos com variações permitem oferecer diferentes opções para o mesmo produto, como Cor (Azul, Verde, Amarelo), <PERSON><PERSON><PERSON> (P, M, G), ou Material. Cada variação pode ter seu próprio preço, estoque e imagens.", "variationTypeSimple": "Simples", "variationTypeWithVariation": "Com variação", "optionName": "Nome da opção", "variationPlaceholder": "<PERSON><PERSON><PERSON>, Cor, Material, etc.", "showOnSearch": "Mostrar na busca", "addValue": "Adicionar {value}", "addVariation": "Adicionar variação (<PERSON><PERSON>, <PERSON>, Material...)", "groupBy": "Agrupar por", "variant": "Variação", "variants": "Variações", "editVariations": "Editar V<PERSON>", "editVariation": "Editar Varia<PERSON>", "editGroupVariations": "Editar Grupo de Variações", "editingVariations": "<PERSON>ando {count} variaç<PERSON>es", "differentValuesInGroup": "Valores diferentes no grupo", "differentValues": "Valores diferentes", "mixedValues": "Valores mistos", "mixed": "<PERSON><PERSON><PERSON>", "noVariations": "Sem variações", "filterByAttributes": "Filtrar por atributos", "images": "Imagens", "dragAndDropImages": "Arraste e solte imagens aqui ou clique para selecionar", "uploadImages": "Selecionar Imagens", "imageUploaded": "Imagem enviada com sucesso", "imageUploadError": "Erro ao enviar imagem", "saveProductFirst": "Salve o produto primeiro para habilitar o upload de imagens", "confirmDeleteImage": "Confirmar exclus<PERSON> da imagem", "confirmDeleteImageMessage": "Tem certeza que deseja excluir esta imagem?", "giftPackaging": "<PERSON>balar para presente", "giftPackagingQuestion": "Per<PERSON><PERSON> embalagem para presente?", "giftPackagingPrice": "Preço da embalagem", "giftPackagingType": "Como deseja calcular o valor no carrinho?", "giftPackagingPricePlaceholder": "R$: 00,00", "giftPackagingTypeSum": "<PERSON><PERSON> o preço de todas as embalagens selecionadas", "giftPackagingTypeMax": "Usar apenas o maior preço entre os produtos embalados", "quantityLimit": "Limitar Quantidade", "minQuantityLimitCheck": "Habilitar quantidade Mínima", "maxQuantityLimitCheck": "Habilitar quantidade Máxima", "minQuantityLimit": "Quantidade Mínima", "maxQuantityLimit": "Quantidade Máxima", "customization": {"sectionTitle": "Opções de personalização", "sectionDescription": "Configure como os clientes poderão personalizar este produto durante a compra.", "enabled": "<PERSON><PERSON><PERSON>", "customization": "Personalização", "newCustomization": "Nova personalização", "addCustomization": "Adicionar personaliza<PERSON>", "emptyTitle": "Nenhuma personalização definida", "emptyDescription": "Adicione opções de personalização para este produto", "type": "Tipo", "selectType": "Selecione um tipo", "title": "<PERSON><PERSON><PERSON><PERSON>", "titlePlaceholder": "Ex: Nome a ser gravado", "description": "Descrição", "descriptionPlaceholder": "Ex: Informe o nome que será gravado na camiseta", "required": "Campo obrigatório", "additionalPrice": "Preço adicional", "pricePlaceholder": "0,00", "characterLimit": "Limite de caracteres", "characterLimitPlaceholder": "Ex: 50", "options": "Opções de escolha", "optionTitle": "Opção", "optionLabel": "<PERSON><PERSON><PERSON><PERSON> da <PERSON>ção", "optionLabelPlaceholder": "Ex: Sim, Não", "addOption": "Adicionar <PERSON>", "noOptionsAdded": "Nenhuma opção adicionada", "nestedCustomization": "Personalização aninhada", "addNestedCustomization": "Adicionar personalização aninhada", "enableNested": "Habilitar", "moveUp": "Mover para cima", "moveDown": "Mover para baixo", "types": {"multipleChoice": "Múltipla escolha", "text": "Texto", "number": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "image": "Imagem"}}, "addPriceRanges": "Adicionar faixas de preços", "addPriceRangeButton": "Adicionar faixa de preço", "hasPriceRanges": "Esta variação possui faixa de preços diferentes", "fromQuantity": "A partir de", "currency": "R$", "units": "unidade(s)", "addPriceRange": "Adicionar Faixa de Preço", "copyPriceToAll": "Copiar preço para todas as variações", "copyStockToAll": "Copiar estoque para todas as variações", "copyOriginalPriceToAll": "Copiar preço original para todas as variações", "copyCostPriceToAll": "Copiar preço de custo para todas as variações", "copyWeightToAll": "Copiar peso para todas as variações", "copyLengthToAll": "Copiar comprimento para todas as variações", "copyWidthToAll": "Co<PERSON>r largura para todas as variações", "copyDepthToAll": "Copiar profundidade para todas as variações", "newProduct": "Novo Produto", "newProductSubtitle": "Crie um novo produto para sua loja", "editProduct": "<PERSON><PERSON>", "editProductSubtitle": "Edite as informações do produto", "productCreated": "Produto criado com sucesso", "productUpdated": "Produto atualizado com sucesso", "errorLoadingProduct": "Erro ao carregar produto", "errorUpdatingProduct": "Erro ao atualizar produto", "anErrorOccurred": "Ocorreu um erro", "variation": "Variação", "newVariation": "Nova variação", "imagesUploadedSuccessfully": "Imagens carregadas com sucesso", "variationImagesUploaded": "Imagens das variações carregadas com sucesso", "errorUploadingImages": "Erro ao carregar imagens", "errorDeletingImage": "Erro ao deletar imagem", "variationStructureChanged": "A estrutura de variações foi alterada. As imagens antigas podem não fazer mais sentido para as novas variações.", "variationStructureChangedHelp": "Clique no ícone da câmera ao lado de cada variação para adicionar ou reatribuir imagens. As imagens antigas foram removidas automaticamente.", "variationsRemoved": "As variações foram removidas. As imagens de variações serão descartadas.", "variationsAdded": "Variações foram adicionadas. Você pode agora adicionar imagens específicas para cada variação.", "imagePositionsUpdated": "Posições das imagens atualizadas", "errorUpdatingPositions": "Erro ao atualizar posições das imagens", "nameRequired": "O nome do produto é obrigatório", "shortDescriptionRequired": "A descrição curta é obrigatória", "shipping": {"title": "Frete", "free": "Este produto tem frete grátis: ", "fixed": "Este produto tem frete fixo: ", "yes": "<PERSON>m", "no": "Não", "dimensionsInPriceAndStock": "Adicione as dimensões do produto na seção de dimensões e peso", "dimensionsInVariations": "Adicione as dimensões do produto na seção de variações", "value": "Valor do frete fixo:", "combinedValue": "Valor do frete combinado:", "maxUnities": "Usar o frete fixo até:", "dimensions": "Dimensões do pacote:", "length": "Comprimento:", "width": "Largura:", "height": "Altura:", "weight": "Peso estimado do pacote até:", "valuePlaceholder": "", "combinedValuePlaceholder": "", "maxUnitiesPlaceholder": "unidades"}, "relatedProducts": "Produtos relacionados", "relatedProductsSubtitle": "Adicione produtos relacionados para melhorar a experiência do cliente", "imagesWillBeUploadedOnSave": "As imagens serão carregadas quando você salvar o produto", "maxFilesReached": "Limite máximo de {max} arquivos atingido", "duplicateFilesSkipped": "Arquivos duplicados foram ignorados", "imagesAddedSuccessfully": "{count} imagem(ns) adicionada(s) com sucesso", "uploadingImages": "Enviando imagens... Aguarde até concluir.", "photosMovedToVariations": "As fotos foram removidas pois o produto agora possui variações", "uploadingImagesInBackground": "Carregando imagens em segundo plano...", "allImagesUploadedSuccessfully": "<PERSON><PERSON> as imagens foram carregadas com sucesso", "someImagesFailedToUpload": "Algumas imagens falharam ao carregar", "searchProducts": "Pes<PERSON><PERSON> produtos", "searchByName": "Pesquisar por nome", "productsSelected": "produtos selecionados", "addToOrder": "Adicionar ao pedido", "itemAdded": "item adicionado", "itemsAdded": "itens adicionados", "product": "Produ<PERSON>", "quantity": "Quantidade", "unitPrice": "Preço Unitário", "availableStock": "Estoque Disponível", "loading": "Carregando...", "category": "Categoria", "selectCategory": "Selecionar categoria", "allCategories": "<PERSON><PERSON> as categorias", "label": {"title": "Etiquetas de Produtos Iluria", "create": "<PERSON><PERSON><PERSON>", "AiGeneratorTitle": "Gerador de Etiquetas IA", "used": "0/0 usado(s)", "upgrade": "Upgrade", "buttonDescription": "Crie etiquetas a partir de texto", "createFromAi": "Criar com IA", "badgesTitle": "<PERSON><PERSON><PERSON>", "badgesDescription": "Crie emblemas para o produto", "labelsTitle": "Etiquetas", "labelsDescription": "Crie etiquetas personalizadas para o produto"}, "attributes": {"title": "Atributos & Filtros", "sectionTitle": "Atributos & Filtros", "sectionDescription": "Configure os atributos deste produto e defina quais serão usados como filtros na loja", "addAttribute": "Adicionar <PERSON>", "attribute": "Atributo", "values": "Valores", "useAsFilter": "Usar como filtro na loja", "attributeName": "Nome do Atributo", "attributeNamePlaceholder": "Ex: <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON>...", "attributeValues": "Valores do atributo", "addValue": "Adicionar valor", "addNewAttribute": "Adicionar novo atributo", "noAttributesAdded": "Nenhum atributo adicionado", "emptyTitle": "Nenhum atributo definido", "emptyDescription": "Adicione atributos para organizar melhor seus produtos e facilitar a busca dos clientes", "searchAttributes": "Pesquisar atributos...", "createNewAttribute": "Criar novo atributo", "createAttributeModal": {"title": "Adicionar Novo Atributo", "name": "Nome do Atributo", "namePlaceholder": "Ex: <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON>...", "nameHelper": "Este será o nome do atributo (ex: <PERSON><PERSON>, <PERSON><PERSON><PERSON>, Material)", "category": "Categoria vinculada", "categoryHelper": "O atributo será específico para esta categoria", "initialValues": "Valores iniciais", "initialValuesPlaceholder": "Ex: Azul, Verde, Amarelo", "initialValuesHelper": "Adicione alguns valores iniciais separados por vírgula (opcional)", "cancel": "<PERSON><PERSON><PERSON>", "save": "<PERSON><PERSON>"}, "suggestions": {"recentlyUsed": "Usados recentemente", "popular": "Mais populares", "allAttributes": "Todos os atributos"}, "validation": {"attributeRequired": "Selecione um atributo", "valuesRequired": "Adicione pelo menos um valor", "nameRequired": "O nome do atributo é obrigatório", "categoryRequired": "Selecione uma categoria"}, "success": {"attributeCreated": "Atributo criado com sucesso", "attributesSaved": "Atributos salvos com sucesso"}, "error": {"loadingAttributes": "Erro ao carregar atributos", "savingAttributes": "Erro ao salvar atributos", "creatingAttribute": "Erro ao criar atributo"}}, "labelsDescription": "Crie etiquetas para o produto", "listTitle": "Listas", "viewFullListButton": "Ver lista completa", "measurementTableSubtitle": "Selecione uma tabela de medidas para este produto (opcional)", "type": "Tipo do Produto", "skuCodeLabel": "Código único interno usado para identificação no sistema", "profitMargin": "<PERSON><PERSON><PERSON>", "negativeMargin": "Margem negativa", "negativeMarginHint": "O preço está menor que o custo", "negativeMarginError": "Não é possível salvar um produto com margem de lucro negativa", "noOptionsFound": "Nenhuma opção encontrada", "percentage": "%", "supplierNamePlaceholder": "Nome do fornecedor", "supplierLinkPlaceholder": "Link do fornecedor ou produto", "supplierNotesPlaceholder": "Anotações sobre o fornecedor", "identificationCodes": "Códigos de identificação", "giftCardTitle": "Cart<PERSON>es <PERSON>e", "giftCardSubtitle": "Gerencie seus cartões presente", "giftCardButton": "<PERSON><PERSON><PERSON><PERSON>", "newGiftCardTitle": "Novo Cartão Presente", "editGiftCardTitle": "<PERSON><PERSON>", "newGiftCardSubtitle": "Adicione e edite informações do cartão presente", "seoConfiguration": "Configurações de SEO", "basicGiftCard": "Dados do Cartão Presente", "giftCardSavedSuccessfully": "Cartão Presente criado com sucesso", "giftCardSavedFailure": "Criação do Cartão Presente falhou", "giftCardExcludedSuccess": "Cartão presente excluído com sucesso", "giftCardExcludedFail": "Exclusão do cartão presente falhou", "loadGiftCardFailure": "<PERSON><PERSON> <PERSON><PERSON> as informações do cartão presente", "customer": "Cliente", "denominations": "Denominações", "giftCardName": "Título do Cartão Presente", "import": {"title": "Importar", "subtitle": "Importe produtos em massa a partir de um arquivo CSV", "tabs": {"import": "Importação", "guide": "<PERSON><PERSON><PERSON>"}, "fileSelection": "Seleção de Arquivo", "fileSelectionDescription": "Selecione um arquivo CSV para importar produtos", "selectFile": "Selecionar Arquivo CSV", "selectFileDescription": "Arraste e solte um arquivo CSV aqui ou clique para selecionar", "chooseFile": "Escolher <PERSON>", "removeFile": "Remover Arquivo", "nextStep": "Próximo <PERSON>", "loadingFields": "Carregando campos disponíveis...", "fieldMapping": "Mapeamento de Campos", "fieldMappingDescription": "Configure como os campos do CSV serão mapeados para os campos do produto", "csvPreview": "Prévia do CSV", "columns": "colu<PERSON>", "mapFields": "Mapear <PERSON>s", "fieldsMapped": "campos mapeados", "selectField": "Selecionar campo...", "fieldIgnored": "Campo ignorado", "backToFile": "Voltar ao Arquivo", "mappingPreview": "Prévia do Mapeamento", "howWillBeSaved": "Veja como os dados serão salvos no sistema", "product": "Produ<PERSON>", "exampleRecords": "registros de exemplo", "productData": "Dados do Produto", "productVariations": "Variações do Produto", "startImport": "Inici<PERSON>", "confirmTitle": "Confirma<PERSON>", "confirmMessage": "Deseja iniciar a importação dos produtos selecionados?", "importStarted": "Importação Iniciada", "importStartedMessage": "A importação foi iniciada com sucesso. Você pode acompanhar o progresso na lista de produtos.", "importError": "Erro na Importação", "parseError": "Erro ao processar o arquivo CSV", "fields": {"name": "Nome do Produto", "sku": "SKU", "description": "Descrição", "price": "Preço", "cost": "Custo", "stock": "Estoque", "status": "Status", "category": "Categoria", "tags": "Tags", "color": "Cor", "size": "<PERSON><PERSON><PERSON>", "material": "Material", "variationPrice": "Preço da Variação", "variationStock": "Estoque da Variação", "variationSku": "SKU da Variação"}, "guide": {"csvPreparation": "Preparação do CSV", "csvPreparationDescription": "Como preparar seu arquivo CSV para importação", "csvStructure": "Estrutura do CSV", "csvStructureDescription": "Seu arquivo CSV deve ter uma linha de cabeçalho com os nomes dos campos:", "bestPractices": "<PERSON><PERSON><PERSON>", "practice1": "Use codificação UTF-8 para caracteres especiais", "practice2": "Mantenha os nomes dos campos em português ou inglês", "practice3": "Certifique-se de que os preços usem ponto como separador decimal", "practice4": "Valores de estoque devem ser números inteiros", "fieldMapping": "Mapeamento de Campos", "fieldMappingDescription": "Exemplos de como mapear campos do CSV para o sistema", "productFields": "Campos do Produto", "variationFields": "Campos de Variação"}}, "export": {"title": "Exportar", "subtitle": "Exporte seus produtos para um arquivo CSV ou Excel", "basicData": "Dados Básicos", "basicDataDescription": "Selecione o formato do arquivo para exportação", "fileFormat": "Formato do Arquivo", "csvDescription": "Arquivo CSV (valores separados por vírgula)", "xlsxDescription": "Planilha Excel (XLSX)", "fieldSelection": "Seleção de Campos", "fieldSelectionDescription": "Escolha quais campos incluir na exportação", "selectAllFields": "Selecionar todos os campos", "selectAllDescription": "Marque esta opção para selecionar todos os campos disponíveis de uma vez", "selectedFieldsCount": "Campos selecionados", "fields": "campos", "summary": "Resumo da Exportação", "summaryDescription": "Revise as configurações antes de iniciar a exportação", "format": "Formato", "totalProducts": "Total de produtos", "products": "produtos", "startExport": "Iniciar <PERSON>", "viewExports": "Ver Exportações", "confirmTitle": "Confirmar <PERSON>rta<PERSON>", "confirmMessage": "Deseja iniciar a exportação dos produtos selecionados?", "exportStarted": "Exportação Iniciada", "exportStartedMessage": "A exportação foi iniciada com sucesso.", "exportError": "Erro na Exportação", "noFieldsSelected": "Selecione pelo menos um campo para exportar"}, "exportList": {"title": "Exportações de Produtos", "subtitle": "Gerencie suas exportações de dados de produtos", "newExport": "Nova Exportação", "fileName": "Nome do Arquivo", "createdAt": "Data de Criação", "fileSize": "<PERSON><PERSON><PERSON>", "status": "Status", "actions": "Ações", "download": "Baixar arquivo", "delete": "Excluir exportação", "noExports": "Nenhuma exportação encontrada", "noExportsDescription": "Você ainda não criou nenhuma exportação de produtos", "createFirstExport": "Criar Primeira Exportação", "loadError": "Erro ao carregar exportações", "downloadStarted": "Download iniciado", "downloadError": "Erro ao baixar arquivo", "deleteTitle": "Confirmar <PERSON>", "deleteMessage": "Tem certeza que deseja excluir esta exportação?", "deleteConfirm": "Excluir", "deleteSuccess": "Exportação excluída com sucesso", "deleteError": "Erro ao excluir exportação", "loading": "Carregando exportações...", "backToProducts": "Voltar para Listagem de Produtos", "infoTitle": "Informações sobre Exportações", "retentionTitle": "Retenção de Arquivos", "retentionDescription": "Os arquivos de exportação são mantidos por 30 dias e depois removidos automaticamente.", "processingTitle": "Processamento", "processingDescription": "Exportações grandes podem levar alguns minutos para serem processadas.", "formatsTitle": "Formatos Suportados", "formatsDescription": "Suportamos exportação em CSV e Excel (XLSX) para máxima compatibilidade."}, "bulk": {"selected": "selecionado", "selectedPlural": "selecionados", "actions": "Ações em massa", "deleteSelected": "Excluir selecionados", "deleteSelectedShort": "Excluir", "selectProduct": "Selecionar produto", "confirmDeleteTitle": "Confirmar exclusão em massa", "confirmDeleteMessage": "Tem certeza que deseja excluir {count} {entity}? Esta ação não pode ser desfeita.", "confirmDeleteMessageSingle": "Tem certeza que deseja excluir 1 {entity}? Esta ação não pode ser desfeita.", "deleteSuccess": "{count} {entity} excluídos com sucesso", "deleteSuccessSingle": "1 {entity} excluído com sucesso", "deleteError": "Erro ao excluir {entity} selecionados", "deletePartialError": "Alguns {entity} não puderam ser excluídos", "processing": "Processando...", "clearSelection": "<PERSON><PERSON>", "selectAll": "Selecionar todos", "deselectAll": "<PERSON><PERSON><PERSON> to<PERSON>", "product": "produto", "products": "produtos"}}