<template>
  <div class="store-initialization-overlay">
    <div class="initialization-modal">
      <!-- <PERSON><PERSON> -->
      <div class="modal-header">
        <div class="iluria-logo-container">
          <IluriaLogo height="3rem" width="auto" class="iluria-logo" />
        </div>
        <h1 class="main-title">{{ $t('stores.initializingStore') }}</h1>
        <p class="store-name">{{ storeName }}</p>
        <p class="subtitle">{{ $t('stores.settingUpEverything') }}</p>
      </div>

      <!-- Progress Section -->
      <div class="progress-section">
        <!-- Main Progress Bar -->
        <div class="progress-container">
          <div class="progress-bar">
            <div
              class="progress-fill"
              :style="{ width: `${error ? 0 : Math.max(5, progress.progress)}%` }"
              :class="{
                'progress-error': error,
                'progress-complete': progress.progress === 100 && !error,
                'progress-animated': !error && progress.progress > 0 && progress.progress < 100
              }"
            >
              <div class="progress-shine"></div>
            </div>
          </div>
          <div class="progress-percentage">
            {{ error ? '0' : Math.max(5, progress.progress) }}%
          </div>
        </div>

        <!-- Current Step Display -->
        <div class="current-step-card" :class="{ 'step-error': error, 'step-success': progress.progress === 100 && !error }">
          <div class="step-icon-container">
            <div class="step-icon" :class="getStepIconClass()">
              <HugeiconsIcon
                :icon="getStepIcon()"
                size="28"
                :strokeWidth="1.5"
              />
            </div>
          </div>
          <div class="step-content">
            <h3 class="step-title">{{ getStepTitle() }}</h3>
            <p class="step-message">{{ error || progress.message }}</p>
            <div v-if="!error && progress.progress > 0 && progress.progress < 100" class="loading-dots">
              <span></span>
              <span></span>
              <span></span>
            </div>
            <div v-if="progress.progress === 100 && !error" class="success-message">
              <HugeiconsIcon :icon="CheckmarkCircle02Icon" size="18" :strokeWidth="1.5" />
              <span>{{ $t('stores.redirectingToStore') }}</span>
            </div>
          </div>
        </div>

        <!-- Steps Timeline -->
        <div v-if="!error" class="steps-timeline">
          <div
            v-for="(step, index) in steps"
            :key="step.key"
            class="timeline-item" 
            :class="{
              'timeline-completed': isStepCompleted(step.key),
              'timeline-current': isStepCurrent(step.key),
              'timeline-pending': isStepPending(step.key)
            }"
          >
            <div 
              class="timeline-connector" 
              :class="getConnectorClass(step.key, index)"
              v-if="index < steps.length - 1"
            ></div>
            <div class="timeline-indicator">
              <HugeiconsIcon
                v-if="isStepCompleted(step.key)"
                :icon="CheckmarkCircle02Icon"
                size="16"
                :strokeWidth="1.5"
              />
              <div v-else-if="isStepCurrent(step.key)" class="spinner"></div>
              <HugeiconsIcon
                v-else
                :icon="step.icon"
                size="16"
                :strokeWidth="1.5"
              />
            </div>
            <div class="timeline-content">
              <span class="timeline-label">{{ step.label }}</span>
            </div>
          </div>
        </div>

        <!-- Error Section -->
        <div v-if="error" class="error-section">
          <div class="error-icon">
            <HugeiconsIcon :icon="Alert02Icon" size="32" :strokeWidth="1.5" />
          </div>
          <h3 class="error-title">{{ $t('stores.initializationError') }}</h3>
          <p class="error-message">{{ error }}</p>
          <button class="retry-btn" @click="$emit('retry')">
            <HugeiconsIcon :icon="RefreshIcon" size="18" :strokeWidth="1.5" />
            {{ $t('tryAgain') }}
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { computed, watch } from 'vue'
import { useI18n } from 'vue-i18n'
import IluriaLogo from '@/components/iluria/IluriaLogo.vue'
import { HugeiconsIcon } from '@hugeicons/vue'
import {
  PlayIcon,
  FolderIcon,
  CheckmarkCircle02Icon,
  CheckmarkSquare02Icon,
  ShoppingBasket01Icon,
  Alert02Icon,
  RefreshIcon,
  Mail01Icon 
} from '@hugeicons-pro/core-stroke-rounded'

const { t } = useI18n()

const props = defineProps({
  storeName: {
    type: String,
    required: true
  },
  progress: {
    type: Object,
    required: true
  },
  error: {
    type: String,
    default: null,
    validator(value) {
      // Aceita null, undefined ou string
      return value === null || value === undefined || typeof value === 'string'
    }
  }
})

const emit = defineEmits(['retry', 'completed'])

const steps = computed(() => [
  { key: 'STARTED', order: 1, label: t('stores.initializationSteps.starting'), icon: PlayIcon },
  { key: 'CREATING_DOMAIN', order: 2, label: t('stores.initializationSteps.creatingDomain'), icon: FolderIcon },
  { key: 'CREATING_CATEGORIES', order: 3, label: t('stores.initializationSteps.creatingCategories'), icon: FolderIcon },
  { key: 'CREATING_EMAIL_NOTIFICATION_SETTINGS', order: 4, label: t('stores.initializationSteps.creatingEmailNotificationSettings'), icon: Mail01Icon },
  { key: 'CREATING_PRODUCTS', order: 5, label: t('stores.initializationSteps.creatingProducts'), icon: ShoppingBasket01Icon },
  { key: 'COMPLETED', order: 6, label: t('stores.initializationSteps.completed'), icon: CheckmarkSquare02Icon }
])

const getStepTitle = () => {
  if (props.error) return t('stores.initializationError')

  const currentStep = steps.value.find(step => step.key === props.progress.step)
  return currentStep ? currentStep.label : t('stores.initializingStore')
}

const getStepIcon = () => {
  if (props.error) return Alert02Icon
  if (props.progress.progress === 100) return CheckmarkSquare02Icon

  const currentStep = steps.value.find(step => step.key === props.progress.step)
  return currentStep ? currentStep.icon : PlayIcon
}

const getStepIconClass = () => {
  if (props.error) return 'step-icon-error'
  if (props.progress.progress === 100) return 'step-icon-success'
  return 'step-icon-loading'
}

const isStepCompleted = (stepKey) => {
  const stepOrder = steps.value.find(s => s.key === stepKey)?.order || 0
  const currentOrder = steps.value.find(s => s.key === props.progress.step)?.order || 0
  return stepOrder < currentOrder || props.progress.step === 'COMPLETED'
}

const isStepCurrent = (stepKey) => {
  return stepKey === props.progress.step && !props.error
}

const isStepPending = (stepKey) => {
  const stepOrder = steps.value.find(s => s.key === stepKey)?.order || 0
  const currentOrder = steps.value.find(s => s.key === props.progress.step)?.order || 0
  return stepOrder > currentOrder && !props.error
}

const getConnectorClass = (currentStepKey, index) => {
  // Não adiciona classe se é o último item (não tem connector)
  if (index >= steps.value.length - 1) return ''
  
  const nextStep = steps.value[index + 1]
  if (!nextStep) return ''
  
  // Se a próxima etapa estiver completa, linha verde
  if (isStepCompleted(nextStep.key)) {
    return 'timeline-connector-completed'
  }
  
  // Se a próxima etapa for a atual, linha na cor do tema
  if (isStepCurrent(nextStep.key)) {
    return 'timeline-connector-current'
  }
  
  // Se a próxima etapa estiver pendente, linha cinza
  return 'timeline-connector-pending'
}

// Watch for completion and emit event
watch(() => props.progress.step, (newStep) => {
  if (newStep === 'COMPLETED' && !props.error) {
    // Wait 3 seconds to show the success message, then emit completion
    setTimeout(() => {
      emit('completed')
    }, 3000)
  }
}, { immediate: true })
</script>

<style scoped>
/* Base Overlay */
.store-initialization-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(0, 0, 0, 0.9) 0%, rgba(0, 0, 0, 0.8) 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 9999;
  backdrop-filter: blur(10px);
  animation: overlayFadeIn 0.4s ease-out;
}

@keyframes overlayFadeIn {
  from {
    opacity: 0;
    backdrop-filter: blur(0px);
  }
  to {
    opacity: 1;
    backdrop-filter: blur(10px);
  }
}

/* Main Modal */
.initialization-modal {
  background: var(--iluria-color-container-bg);
  border-radius: 24px;
  padding: 3rem 2.5rem;
  max-width: 560px;
  width: 92%;
  box-shadow: 
    0 32px 64px -12px rgba(0, 0, 0, 0.4),
    0 0 0 1px rgba(255, 255, 255, 0.05);
  border: 1px solid var(--iluria-color-border);
  animation: modalSlideIn 0.5s cubic-bezier(0.34, 1.56, 0.64, 1);
  position: relative;
  overflow: hidden;
}

.initialization-modal::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 2px;
  background: linear-gradient(90deg, 
    transparent 0%, 
    var(--iluria-color-primary) 25%, 
    var(--iluria-color-primary-hover) 75%, 
    transparent 100%
  );
  animation: topShimmer 3s infinite;
}

@keyframes modalSlideIn {
  from {
    opacity: 0;
    transform: translateY(-30px) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

@keyframes topShimmer {
  0%, 100% { opacity: 0.3; }
  50% { opacity: 1; }
}

/* Header Section */
.modal-header {
  text-align: center;
  margin-bottom: 2.5rem;
}

.iluria-logo-container {
  margin-bottom: 1.5rem;
  animation: logoFadeIn 0.8s ease-out 0.2s both;
}

.iluria-logo {
  filter: drop-shadow(0 4px 8px rgba(0, 0, 0, 0.15));
  animation: logoGlow 3s infinite ease-in-out;
}

@keyframes logoFadeIn {
  from {
    opacity: 0;
    transform: translateY(-20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes logoGlow {
  0%, 100% {
    filter: drop-shadow(0 4px 8px rgba(0, 0, 0, 0.15));
  }
  50% {
    filter: drop-shadow(0 4px 12px rgba(var(--iluria-color-primary-rgb), 0.3));
  }
}

.main-title {
  font-size: 2rem;
  font-weight: 700;
  color: var(--iluria-color-text-primary);
  margin: 0 0 0.5rem 0;
  animation: titleSlideIn 0.6s ease-out 0.3s both;
}

.store-name {
  color: var(--iluria-color-primary);
  font-size: 1.25rem;
  font-weight: 600;
  margin: 0 0 0.5rem 0;
  animation: nameSlideIn 0.6s ease-out 0.4s both;
}

.subtitle {
  color: var(--iluria-color-text-secondary);
  font-size: 1rem;
  font-weight: 400;
  margin: 0;
  animation: subtitleSlideIn 0.6s ease-out 0.5s both;
}

@keyframes titleSlideIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes nameSlideIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes subtitleSlideIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Progress Section */
.progress-section {
  display: flex;
  flex-direction: column;
  gap: 2rem;
}

/* Main Progress Bar */
.progress-container {
  display: flex;
  align-items: center;
  gap: 1.25rem;
  margin-bottom: 0.5rem;
}

.progress-bar {
  flex: 1;
  height: 16px;
  background: var(--iluria-color-border);
  border-radius: 12px;
  overflow: hidden;
  position: relative;
  box-shadow: inset 0 2px 6px rgba(0, 0, 0, 0.1);
}

.progress-fill {
  height: 100%;
  background: linear-gradient(90deg, 
    var(--iluria-color-primary) 0%, 
    var(--iluria-color-primary-hover) 100%
  );
  transition: width 1s cubic-bezier(0.4, 0, 0.2, 1);
  border-radius: 12px;
  position: relative;
  overflow: hidden;
}

.progress-fill.progress-animated {
  animation: progressPulse 2s infinite ease-in-out;
}

.progress-fill.progress-error {
  background: linear-gradient(90deg, #ef4444 0%, #dc2626 100%);
  animation: none;
}

.progress-fill.progress-complete {
  background: linear-gradient(90deg, #10b981 0%, #059669 100%);
  animation: progressComplete 0.8s ease-out;
}

.progress-shine {
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, 
    transparent, 
    rgba(255, 255, 255, 0.5), 
    transparent
  );
  animation: progressShine 2.5s infinite;
}

@keyframes progressPulse {
  0%, 100% {
    box-shadow: 0 0 8px rgba(var(--iluria-color-primary-rgb), 0.4);
  }
  50% {
    box-shadow: 0 0 16px rgba(var(--iluria-color-primary-rgb), 0.7);
  }
}

@keyframes progressShine {
  0% { left: -100%; }
  100% { left: 100%; }
}

@keyframes progressComplete {
  0% { transform: scaleY(1); }
  50% { transform: scaleY(1.3); }
  100% { transform: scaleY(1); }
}

.progress-percentage {
  font-weight: 700;
  color: var(--iluria-color-text-primary);
  font-size: 1.25rem;
  min-width: 60px;
  text-align: right;
  transition: all 0.3s ease;
}

/* Current Step Card */
.current-step-card {
  display: flex;
  align-items: center;
  gap: 1.5rem;
  padding: 1.75rem;
  background: var(--iluria-color-hover);
  border-radius: 20px;
  border: 1px solid var(--iluria-color-border);
  animation: stepCardSlideIn 0.6s ease-out;
  position: relative;
  overflow: hidden;
}

.current-step-card.step-error {
  background: rgba(239, 68, 68, 0.1);
  border-color: rgba(239, 68, 68, 0.3);
}

.current-step-card.step-success {
  background: rgba(16, 185, 129, 0.1);
  border-color: rgba(16, 185, 129, 0.3);
  animation: successGlow 1s ease-in-out infinite alternate;
}

@keyframes successGlow {
  0% {
    box-shadow: 0 0 5px rgba(16, 185, 129, 0.3);
  }
  100% {
    box-shadow: 0 0 20px rgba(16, 185, 129, 0.6);
  }
}

@keyframes stepCardSlideIn {
  from {
    opacity: 0;
    transform: translateX(-30px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

.step-icon-container {
  position: relative;
}

.step-icon {
  width: 56px;
  height: 56px;
  border-radius: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.5rem;
  position: relative;
  transition: all 0.3s ease;
}

.step-icon-loading {
  background: var(--iluria-color-primary);
  color: var(--iluria-color-primary-contrast);
  animation: iconPulse 1.5s infinite ease-in-out;
}

.step-icon-success {
  background: #10b981;
  color: white;
  animation: successBounce 0.8s cubic-bezier(0.68, -0.55, 0.265, 1.55);
}

.step-icon-error {
  background: #ef4444;
  color: white;
  animation: errorShake 0.8s ease-in-out;
}

@keyframes iconPulse {
  0%, 100% {
    transform: scale(1);
    box-shadow: 0 4px 12px rgba(var(--iluria-color-primary-rgb), 0.3);
  }
  50% {
    transform: scale(1.05);
    box-shadow: 0 6px 20px rgba(var(--iluria-color-primary-rgb), 0.5);
  }
}

@keyframes successBounce {
  0% {
    transform: scale(0.8) rotate(-15deg);
  }
  60% {
    transform: scale(1.1) rotate(5deg);
  }
  100% {
    transform: scale(1) rotate(0deg);
  }
}

@keyframes errorShake {
  0%, 100% { transform: translateX(0); }
  20% { transform: translateX(-8px); }
  40% { transform: translateX(8px); }
  60% { transform: translateX(-4px); }
  80% { transform: translateX(4px); }
}

.step-content {
  flex: 1;
}

.step-title {
  font-size: 1.125rem;
  font-weight: 600;
  color: var(--iluria-color-text-primary);
  margin: 0 0 0.5rem 0;
}

.step-message {
  color: var(--iluria-color-text-secondary);
  margin: 0 0 0.75rem 0;
  font-size: 1rem;
  line-height: 1.5;
}

.loading-dots {
  display: flex;
  gap: 6px;
  align-items: center;
}

.loading-dots span {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background: var(--iluria-color-primary);
  animation: loadingDots 1.6s infinite ease-in-out;
}

.loading-dots span:nth-child(1) { animation-delay: 0s; }
.loading-dots span:nth-child(2) { animation-delay: 0.2s; }
.loading-dots span:nth-child(3) { animation-delay: 0.4s; }

@keyframes loadingDots {
  0%, 60%, 100% {
    transform: scale(1);
    opacity: 0.4;
  }
  30% {
    transform: scale(1.4);
    opacity: 1;
  }
}

/* Success Message */
.success-message {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  margin-top: 0.5rem;
  padding: 0.75rem 1rem;
  background: rgba(16, 185, 129, 0.1);
  border-radius: 12px;
  border: 1px solid rgba(16, 185, 129, 0.2);
  animation: successMessageSlideIn 0.6s ease-out;
}

.success-message i {
  color: #10b981;
  font-size: 1.125rem;
  animation: successCheckBounce 0.8s ease-out;
}

.success-message span {
  color: #10b981;
  font-weight: 600;
  font-size: 0.95rem;
}

@keyframes successMessageSlideIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes successCheckBounce {
  0% {
    transform: scale(0.5);
    opacity: 0;
  }
  50% {
    transform: scale(1.2);
    opacity: 1;
  }
  100% {
    transform: scale(1);
    opacity: 1;
  }
}

/* Steps Timeline */
.steps-timeline {
  display: flex;
  flex-direction: column;
  gap: 1rem;
  padding: 1rem 0;
}

.timeline-item {
  display: flex;
  align-items: center;
  gap: 1rem;
  position: relative;
  padding: 0.5rem 0;
}

.timeline-connector {
  position: absolute;
  left: 16px;
  top: 32px;
  width: 2px;
  height: 100%;
  background: var(--iluria-color-border);
  z-index: 0;
  transition: background-color 0.3s ease;
}

.timeline-connector-completed {
  background: #10b981;
}

.timeline-connector-current {
  background: var(--iluria-color-primary);
}

.timeline-connector-pending {
  background: var(--iluria-color-border);
}

.timeline-indicator {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 0.875rem;
  font-weight: 600;
  z-index: 1;
  position: relative;
  transition: all 0.3s ease;
}

.timeline-completed .timeline-indicator {
  background: #10b981;
  color: white;
  box-shadow: 0 2px 8px rgba(16, 185, 129, 0.3);
}

.timeline-current .timeline-indicator {
  background: var(--iluria-color-primary);
  color: var(--iluria-color-primary-contrast);
  box-shadow: 0 2px 8px rgba(var(--iluria-color-primary-rgb), 0.4);
}

.timeline-pending .timeline-indicator {
  background: var(--iluria-color-border);
  color: var(--iluria-color-text-secondary);
}

/* Spinner for timeline current step */
.spinner {
  width: 16px;
  height: 16px;
  border: 2px solid transparent;
  border-top: 2px solid currentColor;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.timeline-content {
  flex: 1;
}

.timeline-label {
  font-size: 0.95rem;
  color: var(--iluria-color-text-secondary);
  transition: all 0.3s ease;
}

.timeline-completed .timeline-label {
  color: var(--iluria-color-text-primary);
  font-weight: 500;
}

.timeline-current .timeline-label {
  color: var(--iluria-color-text-primary);
  font-weight: 600;
}

/* Error Section */
.error-section {
  text-align: center;
  padding: 2rem 1rem;
  animation: errorFadeIn 0.6s ease-out;
}

@keyframes errorFadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.error-icon {
  width: 64px;
  height: 64px;
  border-radius: 50%;
  background: rgba(239, 68, 68, 0.1);
  color: #ef4444;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.5rem;
  margin: 0 auto 1.5rem;
  animation: errorPulse 2s infinite ease-in-out;
}

@keyframes errorPulse {
  0%, 100% {
    transform: scale(1);
    box-shadow: 0 0 0 0 rgba(239, 68, 68, 0.3);
  }
  50% {
    transform: scale(1.05);
    box-shadow: 0 0 0 8px rgba(239, 68, 68, 0);
  }
}

.error-title {
  font-size: 1.25rem;
  font-weight: 600;
  color: var(--iluria-color-text-primary);
  margin: 0 0 0.75rem 0;
}

.error-message {
  color: var(--iluria-color-text-secondary);
  margin: 0 0 2rem 0;
  font-size: 1rem;
  line-height: 1.5;
}

.retry-btn {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  background: var(--iluria-color-button-primary-bg);
  border: none;
  border-radius: 14px;
  padding: 1rem 2rem;
  color: var(--iluria-color-button-primary-fg);
  font-weight: 600;
  font-size: 1rem;
  cursor: pointer;
  transition: all 0.3s ease;
  animation: retryBounceIn 0.6s ease-out 0.3s both;
  box-shadow: 0 4px 12px rgba(var(--iluria-color-primary-rgb), 0.3);
}

.retry-btn:hover {
  background: var(--iluria-color-button-primary-bg-hover);
  transform: translateY(-2px);
  box-shadow: 0 8px 20px rgba(var(--iluria-color-primary-rgb), 0.4);
}

@keyframes retryBounceIn {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Responsive Design */
@media (max-width: 768px) {
  .initialization-modal {
    padding: 2rem 1.5rem;
    margin: 1rem;
  }
  
  .main-title {
    font-size: 1.5rem;
  }
  
  .store-name {
    font-size: 1.125rem;
  }
  
  .current-step-card {
    padding: 1.25rem;
    gap: 1rem;
  }
  
  .step-icon {
    width: 48px;
    height: 48px;
  }
}
</style>