<template>
  <div class="w-full measurement-table-selector">
    <!-- Select Section -->
    <div class="select-section">
      <div class="flex items-center justify-between mb-4">
        <label class="block text-sm font-medium text-[var(--iluria-color-text-primary)] mb-1">
          {{ label || t('measurementTable.selectTable') }}
        </label>
        <IluriaButton
          v-if="allowCreate"
          @click="openCreateModal"
          color="dark"
          size="small"
          :hugeIcon="PlusSignCircleIcon"
        >
          {{ t('measurementTable.createNew') }}
        </IluriaButton>
      </div>

    <IluriaSelect
      :model-value="modelValue"
      :options="filteredTables"
      :filter="true"
      option-label="name"
      option-value="id"
      :delay="300"
      :min-length="1"
      :placeholder="t('measurementTable.selectTablePlaceholder')"
      @update:model-value="$emit('update:modelValue', $event)"
      @filter="filterTables"
      class="w-full"
      clearable
      :loading="loading"
      filter-placeholder="Digite para buscar tabelas..."
      dropdown-position="down"
    >
    </IluriaSelect>

    <div v-if="selectedTable" class="mt-4 space-y-2">
      <div class="flex justify-end gap-2">
        <IluriaButton
          color="secondary"
          size="small"
          :hugeIcon="Edit02Icon"
          @click="editTable(selectedTable)"
        >
          {{ t('edit') }}
        </IluriaButton>
        <IluriaButton
          color="text-danger"
          size="small"
          :hugeIcon="Delete02Icon"
          @click="deleteTable(selectedTable)"
        >
          {{ t('delete') }}
        </IluriaButton>
      </div>

      <MeasurementTablePreview 
        :measurement-table="selectedTable" 
        :product-images="productImages"
      />
    </div>

    <MeasurementTableForm
      v-model="newTable"
      v-model:visible="showCreateModal"
      :show-preview="true"
      @save="createTable"
      @cancel="showCreateModal = false"
    />

    <MeasurementTableForm
      v-model="editingTable"
      v-model:visible="showEditModal"
      :show-preview="true"
      :measurement-table-id="editingTable.id"
      @save="handleEditSave"
      @cancel="showEditModal = false"
    />

    <div v-if="showPreviewModal" class="fixed inset-0 z-50 flex items-center justify-center">
      <div class="absolute inset-0 bg-black bg-opacity-50" @click="showPreviewModal = false"></div>
      
      <div class="relative bg-white rounded-lg shadow-xl w-full max-w-4xl h-[90vh] max-h-[800px] mx-4 flex flex-col">
        <div class="flex items-center justify-between px-8 py-6 border-b border-gray-200">
          <h2 class="text-xl font-semibold text-gray-900">{{ t('measurementTable.preview') }}: {{ previewingTable?.name || '' }}</h2>
          <button @click="showPreviewModal = false" class="text-gray-400 hover:text-gray-600 transition-colors">
            <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
            </svg>
          </button>
        </div>
        
        <div class="flex-1 px-8 py-6 overflow-y-auto">
          <div class="mb-4 p-3 bg-green-50 border border-green-200 rounded-lg">
            <div class="flex items-center gap-2">
              <div class="w-6 h-6 bg-green-100 rounded-full flex items-center justify-center">
                <svg class="w-3 h-3 text-green-600" fill="currentColor" viewBox="0 0 20 20">
                  <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                </svg>
              </div>
              <span class="text-sm font-medium text-green-800">{{ t('measurementTable.previewMode') }}</span>
            </div>
          </div>
          
          <div class="measurement-table-form-container">
            <MeasurementTablePreview
              v-if="previewingTable"
              :measurement-table="previewingTable"
              :product-images="productImages"
            />
          </div>
        </div>
        
        <div class="flex items-center justify-between px-8 py-6 border-t border-gray-200 bg-gray-50">
          <div class="text-sm text-gray-500">
            {{ t('measurementTable.previewFooterNote') }}
          </div>
          <div class="flex gap-3">
            <IluriaButton 
              @click="showPreviewModal = false" 
              color="secondary"
            >
              {{ t('close') }}
            </IluriaButton>
            <IluriaButton 
              v-if="previewingTable && !modelValue"
              @click="emit('update:modelValue', previewingTable.id); showPreviewModal = false" 
              color="dark"
            >
              <template #icon>
                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                </svg>
              </template>
              {{ t('measurementTable.selectThisTable') }}
            </IluriaButton>
          </div>
        </div>
      </div>
    </div>
    </div>
    <IluriaConfirmationModal
      :is-visible="showConfirmModal"
      :title="confirmModalTitle"
      :message="confirmModalMessage"
      :confirm-text="confirmModalConfirmText"
      :cancel-text="confirmModalCancelText"
      :type="confirmModalType"
      @confirm="handleConfirm"
      @cancel="handleCancel"
    />
  </div>
</template>

<script setup>
import { ref, computed, onMounted, watch } from 'vue'
import { useI18n } from 'vue-i18n'
import { useToast } from '@/services/toast.service'
import { PlusSignCircleIcon, Edit02Icon, Delete02Icon } from '@hugeicons-pro/core-stroke-rounded'

import IluriaSelect from '@/components/iluria/form/IluriaSelect.vue'
import IluriaButton from '@/components/iluria/IluriaButton.vue'
import MeasurementTableForm from './MeasurementTableForm.vue'
import MeasurementTablePreview from './MeasurementTablePreview.vue'
import { measurementTableApi } from '@/services/measurementTable.service'
import IluriaConfirmationModal from '@/components/iluria/modal/IluriaConfirmationModal.vue'

const { t } = useI18n()
const toast = useToast()

// Modal state
const showConfirmModal = ref(false)
const confirmModalTitle = ref('')
const confirmModalMessage = ref('')
const confirmModalConfirmText = ref('Confirmar')
const confirmModalCancelText = ref('Cancelar')
const confirmModalType = ref('info')
const confirmCallback = ref(null)

const props = defineProps({
  modelValue: {
    type: String,
    default: null
  },
  label: {
    type: String,
    default: ''
  },
  allowCreate: {
    type: Boolean,
    default: true
  },
  productImages: {
    type: Array,
    default: () => []
  }
})

const emit = defineEmits(['update:modelValue'])

const measurementTables = ref([])
const loading = ref(false)
const creating = ref(false)
const updating = ref(false)
const filteredTables = ref([])

const showCreateModal = ref(false)
const showEditModal = ref(false)
const showPreviewModal = ref(false)

const newTable = ref({})
const editingTable = ref({})
const previewingTable = ref(null)

const selectedTable = computed(() => {
  if (!props.modelValue) return null
  return measurementTables.value.find(table => table.id === props.modelValue)
})

const loadTables = async () => {
  try {
    loading.value = true
    const tables = await measurementTableApi.getAllList()
    measurementTables.value = Array.isArray(tables) ? tables : []
    filteredTables.value = measurementTables.value
  } catch (error) {
    console.error('Erro ao carregar tabelas de medidas:', error)
  } finally {
    loading.value = false
  }
}

let searchTimeout = null
const filterTables = (event) => {
  clearTimeout(searchTimeout)
  const query = event.filter || ''
  searchTimeout = setTimeout(() => {
    if (!query) {
      filteredTables.value = measurementTables.value
    } else {
      filteredTables.value = measurementTables.value.filter(table => {
        const nameMatch = table.name.toLowerCase().includes(query.toLowerCase())
        const typeMatch = table.type && table.type.toLowerCase().includes(query.toLowerCase())
        const typeTranslatedMatch = t(`measurementTable.type${table.type}`).toLowerCase().includes(query.toLowerCase())
        
        return nameMatch || typeMatch || typeTranslatedMatch
      })
    }
  }, 300)
}

const openCreateModal = () => {
  newTable.value = {
    name: '',
    type: 'STRUCTURED',
    imageUrl: '',
    columns: [],
    rows: []
  }
  showCreateModal.value = true
}

const createTable = async () => {
  try {
    creating.value = true
    const dataToSave = {
      name: newTable.value.name.trim(),
      type: newTable.value.type,
      columns: newTable.value.columns?.map(col => ({
        label: col.label,
        unit: col.unit,
        position: col.position,
        imageUrl: col.imageUrl && !col.imageUrl.startsWith('data:') ? col.imageUrl : ''
      })) || [],
      rows: newTable.value.rows?.map(row => ({
        sizeLabel: row.sizeLabel,
        position: row.position,
        values: row.values?.map(val => ({
          value: val.value
        })) || []
      })) || [],
      imageUrl: newTable.value.imageUrl && !newTable.value.imageUrl.startsWith('data:') ? newTable.value.imageUrl : ''
    }
    
    const response = await measurementTableApi.create(dataToSave)
    const tableId = response.data.id
    
    if (newTable.value.type === 'IMAGE' && newTable.value.newImageFile) {
      try {
        await measurementTableApi.uploadImage(tableId, newTable.value.newImageFile)
      } catch (e) {
        toast.showError(t('measurementTable.imageUploadError'))
      }
    }
    
    if (newTable.value.pendingColumnImages && Object.keys(newTable.value.pendingColumnImages).length > 0) {
      try {
        await measurementTableApi.batchUploadColumnImages(tableId, newTable.value.pendingColumnImages)
        toast.showSuccess(t('measurementTable.columnImageSaved'))
      } catch (e) {
        toast.showError(t('measurementTable.imageUploadError'))
      }
    }
    
    await loadTables()
    emit('update:modelValue', response.data.id)
    showCreateModal.value = false
    
    toast.showSuccess(t('measurementTable.createdSuccess'))
  } catch (error) {
    console.error('Erro ao criar tabela de medidas:', error)
    toast.showError(t('measurementTable.errorCreating'))
  } finally {
    creating.value = false
  }
}

const editTable = (table) => {
  editingTable.value = {
    ...table,
    columns: table.columns ? table.columns.map(col => ({ ...col })) : [],
    rows: table.rows ? table.rows.map(row => ({
      ...row,
      values: row.values ? row.values.map(val => ({ ...val })) : []
    })) : []
  }
  
  showEditModal.value = true
}

const handleEditSave = async (formData) => {
  try {
    updating.value = true
    
    const tableData = formData || editingTable.value
    
    if (!tableData.name || !tableData.name.trim()) {
      toast.showError(t('measurementTable.nameRequired'))
      return
    }
    
    if (!tableData.type) {
      toast.showError(t('measurementTable.typeRequired'))
      return
    }
    
    if (!tableData.id) {
      toast.showError('ID da tabela não encontrado')
      return
    }
    
    const dataToSave = {
      name: tableData.name.trim(),
      type: tableData.type,
      columns: tableData.columns?.map(col => ({
        ...(col.id && !col.id.startsWith('col_') && !col.id.startsWith('temp_') ? { id: col.id } : {}),
        label: col.label || '',
        unit: col.unit || 'cm',
        position: col.position || 0,
        imageUrl: col.imageUrl && !col.imageUrl.startsWith('data:') ? col.imageUrl : ''
      })) || [],
      rows: tableData.rows?.map(row => ({
        ...(row.id && !row.id.startsWith('row_') && !row.id.startsWith('temp_') ? { id: row.id } : {}),
        sizeLabel: row.sizeLabel || '',
        position: row.position || 0,
        values: row.values?.map(val => ({
          ...(val.id && !val.id.startsWith('val_') && !val.id.startsWith('temp_') ? { id: val.id } : {}),
          value: val.value || ''
        })) || []
      })) || [],
      imageUrl: tableData.imageUrl && !tableData.imageUrl.startsWith('data:') ? tableData.imageUrl : ''
    }
    
    await measurementTableApi.update(tableData.id, dataToSave)
    
    if (tableData.type === 'IMAGE' && tableData.newImageFile) {
      try {
        await measurementTableApi.uploadImage(tableData.id, tableData.newImageFile)
      } catch (e) {
        toast.showError(t('measurementTable.imageUploadError'))
      }
    }
    
    if (tableData.pendingColumnImages && Object.keys(tableData.pendingColumnImages).length > 0) {
      try {
        await measurementTableApi.batchUploadColumnImages(tableData.id, tableData.pendingColumnImages)
        toast.showSuccess(t('measurementTable.columnImageSaved'))
      } catch (e) {
        toast.showError(t('measurementTable.imageUploadError'))
      }
    }
    
    await loadTables()
    showEditModal.value = false
    
    toast.showSuccess(t('measurementTable.updatedSuccess'))
  } catch (error) {
    console.error('Erro ao atualizar tabela de medidas:', error)
    
    let errorMessage = t('measurementTable.errorUpdating')
    if (error.response?.status === 400) {
      errorMessage = 'Dados inválidos. Verifique se todos os campos estão preenchidos corretamente.'
    } else if (error.response?.status === 404) {
      errorMessage = 'Tabela não encontrada.'
    } else if (error.response?.status === 500) {
      errorMessage = 'Erro interno do servidor. Tente novamente em alguns instantes.'
    }
    
    toast.showError(errorMessage)
  } finally {
    updating.value = false
  }
}

const previewTable = (table) => {
  previewingTable.value = table
  showPreviewModal.value = true
}

// Modal control functions
const showConfirmDanger = (message, title, onConfirm) => {
  confirmModalMessage.value = message
  confirmModalTitle.value = title
  confirmModalConfirmText.value = t('delete')
  confirmModalCancelText.value = t('cancel')
  confirmModalType.value = 'error'
  confirmCallback.value = onConfirm
  showConfirmModal.value = true
}

const handleConfirm = () => {
  if (confirmCallback.value) {
    confirmCallback.value()
  }
  showConfirmModal.value = false
}

const handleCancel = () => {
  showConfirmModal.value = false
}

const deleteTable = (table) => {
  if (!table) return
  showConfirmDanger(
    t('measurementTable.deleteMessage', { name: table.name }),
    t('measurementTable.deleteTitle'),
    () => confirmDelete(table)
  )
}

const confirmDelete = async (table) => {
  try {
    loading.value = true
    await measurementTableApi.delete(table.id)

    await loadTables()

    if (props.modelValue === table.id) {
      emit('update:modelValue', null)
    }

    toast.showSuccess(t('measurementTable.deletedSuccess'))
  } catch (error) {
    console.error('Erro ao excluir tabela de medidas:', error)
    toast.showError(t('measurementTable.errorDeleting'))
  } finally {
    loading.value = false
  }
}

onMounted(() => {
  loadTables()
})

watch(() => props.modelValue, async (newValue) => {
  if (newValue && !measurementTables.value.some(t => t.id === newValue)) {
    try {
      loading.value = true
      const table = await measurementTableApi.getById(newValue)
      if (table) {
        measurementTables.value.push(table)
        filteredTables.value = [...measurementTables.value]
      }
    } catch (error) {
      console.error('Erro ao carregar tabela selecionada:', error)
      toast.showError(t('measurementTable.errorLoadingTable'))
    } finally {
      loading.value = false
    }
  }
}, { immediate: true })
</script>

<style>
.measurement-table-selector {
  max-width: 1400px;
  margin: 0 auto;
  padding: 24px;
  background: var(--iluria-color-bg);
  color: var(--iluria-color-text-primary);
  min-height: 100vh;
}

.select-section {
  margin-bottom: 24px;
}

.measurement-table-selector .measurement-table-form-container {
  width: 100%;
  max-width: 100%;
  overflow-x: hidden;
}

.measurement-table-selector .measurement-table-form-container :deep(.overflow-x-auto) {
  width: 100%;
  max-width: 100%;
  overflow-x: auto;
}

.measurement-table-selector .measurement-table-form-container :deep(table) {
  width: 100%;
  min-width: 600px;
  max-width: 100%;
}

.measurement-table-selector .measurement-table-form-container :deep(.grid) {
  width: 100%;
  max-width: 100%;
}

.measurement-table-selector .measurement-table-form-container :deep(.measurement-input) {
  width: 100%;
  max-width: 120px;
  min-width: 60px;
}

.measurement-table-selector .measurement-table-form-container :deep(.border.rounded-lg) {
  width: 100%;
  max-width: 100%;
  overflow: hidden;
}

.measurement-table-selector :deep(.measurement-table-preview) {
  background-color: white;
  border: 1px solid #e5e7eb;
  border-radius: 0.5rem;
  overflow: hidden;
}

.measurement-table-selector :deep(.measurement-table-preview table) {
  width: 100%;
  border-collapse: collapse;
}

.measurement-table-selector :deep(.measurement-table-preview th),
.measurement-table-selector :deep(.measurement-table-preview td) {
  padding: 0.75rem;
  border: 1px solid #e5e7eb;
  font-size: 0.875rem;
}

.measurement-table-selector :deep(.measurement-table-preview th) {
  background-color: #f9fafb;
  font-weight: 500;
  color: #374151;
}

.measurement-table-selector :deep(.measurement-table-preview td) {
  color: #4b5563;
}

/* Responsive */
@media (max-width: 768px) {
  .measurement-table-selector {
    padding: 16px;
  }
}

@media (max-width: 480px) {
  .measurement-table-selector {
    padding: 12px;
  }
}
</style> 
