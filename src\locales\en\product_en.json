{"title": "Products", "add": "Add Product", "description": "Product Description", "edit": "Edit Product", "products": "Products", "noProducts": "No products found for this store", "noProductsAdded": "No products added", "loadError": "Error loading products", "viewProducts": "View Products", "image": "Image", "name": "Product Name", "sku": "SKU", "price": "Price", "stock": "Stock", "addProduct": "Add Product", "remove": "Remove", "onlyInStock": "Only in stock", "noProductsFound": "No products found", "confirmDelete": "Are you sure you want to delete {name}?", "confirmDeleteTitle": "Confirm Delete", "skuCode": "SKU Code", "barCode": "Barcode", "shortDescription": "Short Description", "pricesAndStock": "Prices and Stock", "originalPrice": "Original Price", "costPrice": "Cost Price", "stockQuantity": "Stock Quantity", "weight": "Weight", "boxLength": "Length", "boxWidth": "<PERSON><PERSON><PERSON>", "boxDepth": "De<PERSON><PERSON>", "productData": "Product Data", "tags": "Tags", "variations": "Variations", "customFields": "Custom Fields", "customFieldsSubtitle": "Create and configure custom fields for the product", "pricesAndStockSubtitle": "Information about the stock and price", "highlight": "Highlight", "newTag": "New", "supplierData": "Supplier Data (optional)", "supplierDataSubtitle": "Use the fields below to keep track of the supplier information for this product. These information are not shown in the store and are only for your internal control.", "supplierName": "Supplier Name (optional)", "supplierLink": "Link to the product or supplier (optional)", "supplierNotes": "Notes (optional)", "selectStatus": "Select a status", "status": "Status", "statusDraft": "Invisible", "statusActive": "Visible", "typePhysical": "Physical", "typeDigital": "Digital", "typeGift": "Gift", "hasVariation": "Has variations", "hasVariantionTooltip": "Products with variations allow you to offer different options for the same product, such as Color (Blue, Green, Yellow), Size (P, M, G), or Material. Each variation can have its own price, stock and images.", "variationTypeSimple": "Simple", "variationTypeWithVariation": "With variation", "optionName": "Option Name", "variationPlaceholder": "Size, Color, Material, etc.", "showOnSearch": "Show on search", "addValue": "Add {value}", "addVariation": "Add variation (Color, Size, Material...)", "groupBy": "Group by", "variant": "<PERSON><PERSON><PERSON>", "variants": "Variants", "editVariations": "Edit Variations", "editVariation": "Edit Variation", "editGroupVariations": "Edit Group of Variations", "editingVariations": "Editing {count} variations", "differentValuesInGroup": "Different values in group", "differentValues": "Different values", "mixedValues": "Mixed values", "mixed": "Mixed", "noVariations": "No variations", "filterByAttributes": "Filter by attributes", "images": "Images", "dragAndDropImages": "Drag and drop images here or click to select", "uploadImages": "Select Images", "imageUploaded": "Image uploaded successfully", "imageUploadError": "Error uploading image", "saveProductFirst": "Save the product first to enable image upload", "confirmDeleteImage": "Confirm delete image", "confirmDeleteImageMessage": "Are you sure you want to delete this image?", "giftPackaging": "Gift packaging", "giftPackagingQuestion": "Allow gift packaging?", "giftPackagingPrice": "Gift packaging price", "giftPackagingType": "How do you want to calculate the value in the cart?", "giftPackagingPricePlaceholder": "R$: 00,00", "giftPackagingTypeSum": "Sum the price of all selected packaging", "giftPackagingTypeMax": "Use only the highest price among the packaged products", "quantityLimit": "Quantity limit", "minQuantityLimitCheck": "Enable minimum quantity", "maxQuantityLimitCheck": "Enable maximum quantity", "minQuantityLimit": "Minimum quantity", "maxQuantityLimit": "Maximum quantity", "addPriceRanges": "Add price ranges", "addPriceRangeButton": "Add price range", "customization": {"sectionTitle": "Customization options", "sectionDescription": "Configure how customers can customize this product during purchase.", "enabled": "Customizable Product", "customization": "Customization", "newCustomization": "New customization", "addCustomization": "Add customization", "emptyTitle": "No customization defined", "emptyDescription": "Add customization options for this product", "type": "Type", "selectType": "Select a type", "title": "Title", "titlePlaceholder": "Ex: Name to be engraved", "description": "Description", "descriptionPlaceholder": "Ex: Enter the name to be engraved on the shirt", "required": "Required field", "additionalPrice": "Additional price", "pricePlaceholder": "0.00", "characterLimit": "Character limit", "characterLimitPlaceholder": "Ex: 50", "options": "Choice options", "optionTitle": "Option", "optionLabel": "Option Title", "optionLabelPlaceholder": "Ex: Yes, No", "addOption": "Add option", "noOptionsAdded": "No options added", "nestedCustomization": "Nested customization", "enableNested": "Enable", "moveUp": "Move up", "moveDown": "Move down", "types": {"multipleChoice": "Multiple choice", "text": "Text", "number": "Number", "image": "Image"}}, "hasPriceRanges": "This variation has different price ranges", "fromQuantity": "From", "currency": "$", "units": "unit(s)", "addPriceRange": "Add Price Range", "copyPriceToAll": "Copy price to all variations", "copyStockToAll": "Copy stock to all variations", "copyOriginalPriceToAll": "Copy original price to all variations", "copyCostPriceToAll": "Copy cost price to all variations", "copyWeightToAll": "Copy weight to all variations", "copyLengthToAll": "Copy length to all variations", "copyWidthToAll": "Copy width to all variations", "copyDepthToAll": "Copy depth to all variations", "newProduct": "New Product", "newProductSubtitle": "Create a new product for your store", "editProduct": "Edit Product", "editProductSubtitle": "Edit the product information", "productCreated": "Product created successfully", "productUpdated": "Product updated successfully", "errorLoadingProduct": "Error loading product", "errorUpdatingProduct": "Error updating product", "anErrorOccurred": "An error occurred", "variation": "Variation", "newVariation": "New variation", "imagesUploadedSuccessfully": "Images uploaded successfully", "variationImagesUploaded": "Variation images uploaded successfully", "errorUploadingImages": "Error uploading images", "errorDeletingImage": "Error deleting image", "variationStructureChanged": "The variation structure has been changed. The old images may no longer make sense for the new variations.", "variationStructureChangedHelp": "Click on the camera icon next to each variation to add or reassign images. The old images were removed automatically.", "variationsRemoved": "Variations were removed. The variation images will be discarded.", "variationsAdded": "Variations were added. You can now add specific images for each variation.", "imagePositionsUpdated": "Image positions updated", "errorUpdatingPositions": "Error updating image positions", "nameRequired": "The product name is required", "shortDescriptionRequired": "The short description is required", "shipping": {"title": "Shipping", "free": "This product has free shipping: ", "fixed": "This product has fixed shipping: ", "yes": "Yes", "no": "No", "dimensionsInPriceAndStock": "Add the product dimensions in the prices and stock section", "dimensionsInVariations": "Add the product dimensions in the variations section", "value": "Fixed shipping value:", "combinedValue": "Combined shipping value:", "maxUnities": "Use fixed shipping until:", "dimensions": "Package dimensions:", "length": "Length:", "width": "Width:", "height": "Height:", "weight": "Estimated package weight:", "valuePlaceholder": "", "combinedValuePlaceholder": "", "maxUnitiesPlaceholder": "units"}, "relatedProducts": "Related products", "relatedProductsSubtitle": "Add related products to improve the customer experience", "photosMovedToVariations": "The photos were removed because the product now has variations", "uploadingImagesInBackground": "Uploading images in background...", "allImagesUploadedSuccessfully": "All images uploaded successfully", "someImagesFailedToUpload": "Some images failed to upload", "imagesWillBeUploadedOnSave": "The images will be uploaded when you save the product", "maxFilesReached": "Maximum limit of {max} files reached", "uploadingImages": "Uploading images... Please wait until completed.", "label": {"title": "Iluria Product Labels", "create": "Create", "AiGeneratorTitle": "AI Label Generator", "used": "0/0 used", "upgrade": "Upgrade", "buttonDescription": "Create labels from text", "createFromAi": "Create from AI", "badgesTitle": "Badges", "badgesDescription": "Create product badges", "labelsTitle": "Labels", "labelsDescription": "Create product labels", "listTitle": "Lists", "viewFullListButton": "View full list"}, "searchProducts": "Search products", "searchByName": "Search by name", "productsSelected": "products selected", "addToOrder": "Add to order", "itemAdded": "item added", "itemsAdded": "items added", "product": "Product", "quantity": "Quantity", "unitPrice": "Unit Price", "availableStock": "Available Stock", "loading": "Loading...", "category": "Category", "selectCategory": "Select category", "allCategories": "All categories", "measurementTableSubtitle": "Select a measurement table for this product (optional)", "skuCodeLabel": "Unique internal code used for identification in the system", "profitMargin": "<PERSON><PERSON>", "negativeMargin": "Negative margin", "negativeMarginHint": "The price is lower than the cost price", "negativeMarginError": "Cannot save a product with negative profit margin", "noOptionsFound": "No options found", "percentage": "%", "supplierNamePlaceholder": "Supplier name", "supplierLinkPlaceholder": "Supplier link or product", "supplierNotesPlaceholder": "Notes about the supplier", "identificationCodes": "Identification codes", "giftCardTitle": "Gift Cards", "giftCardSubtitle": "Manage your gift cards", "giftCardButton": "Add Gift Card", "newGiftCardTitle": "New Gift Card", "editGiftCardTitle": "Edit Gift Card", "newGiftCardSubtitle": "Add and edit gift card informations", "seoConfiguration": "SEO Configuration", "basicGiftCard": "Gift Card Data", "giftCardSavedSuccessfully": "Gift Card created successfully", "giftCardSavedFailure": "Gift Card Creation Failed", "giftCardExcludedSuccess": "Gift card deleted successfully", "giftCardExcludedFail": "Gift card deletion failed", "loadGiftCardFailure": "Error loading gift card information", "customer": "Customer", "denominations": "Denominations", "giftCardName": "Gift Card Title", "import": "Import", "export": "Export", "exportList": {"title": "Product Exports", "subtitle": "Manage your product data exports", "newExport": "New Export", "fileName": "File Name", "createdAt": "Created At", "fileSize": "Size", "status": "Status", "actions": "Actions", "download": "Download file", "delete": "Delete export", "noExports": "No exports found", "noExportsDescription": "You haven't created any product exports yet", "createFirstExport": "Create First Export", "loadError": "Error loading exports", "downloadStarted": "Download started", "downloadError": "Error downloading file", "deleteTitle": "Confirm Deletion", "deleteMessage": "Are you sure you want to delete this export?", "deleteConfirm": "Delete", "deleteSuccess": "Export deleted successfully", "deleteError": "Error deleting export", "loading": "Loading exports...", "backToProducts": "Back to Product List", "infoTitle": "Export Information", "retentionTitle": "File Retention", "retentionDescription": "Export files are kept for 30 days and then automatically removed.", "processingTitle": "Processing", "processingDescription": "Large exports may take a few minutes to process.", "formatsTitle": "Supported Formats", "formatsDescription": "We support CSV and Excel (XLSX) export for maximum compatibility."}, "bulk": {"selected": "selected", "selectedPlural": "selected", "actions": "Bulk actions", "deleteSelected": "Delete selected", "deleteSelectedShort": "Delete", "selectProduct": "Select product", "confirmDeleteTitle": "Confirm bulk deletion", "confirmDeleteMessage": "Are you sure you want to delete {count} {entity}? This action cannot be undone.", "confirmDeleteMessageSingle": "Are you sure you want to delete 1 {entity}? This action cannot be undone.", "deleteSuccess": "{count} {entity} deleted successfully", "deleteSuccessSingle": "1 {entity} deleted successfully", "deleteError": "Error deleting selected {entity}", "deletePartialError": "Some {entity} could not be deleted", "processing": "Processing...", "clearSelection": "Clear selection", "selectAll": "Select all", "deselectAll": "Deselect all", "product": "product", "products": "products"}}