<template>
    <div class="product-list-container">
        <!-- Header Section -->
        <IluriaHeader
            :title="t('products')"
            subtitle="Gerencie seus produtos e variações"
            :showSearch="true"
            :showAdd="true"
            :addText="t('addProduct')"
            :customButtons="headerCustomButtons"
            @search="handleSearch"
            @add-click="addProduct"
            @custom-click="handleCustomButtonClick"
        />

        <!-- Main Content -->

            <div class="table-wrapper">
                <IluriaDataTable
                    :value="sortedProducts"
                    :columns="mainTableColumns"
                    :loading="loading"
                    dataKey="id"
                    v-model:expandedRows="expandedRows"
                    :rowClass="rowClass"
                    class="product-table iluria-data-table"
                >
                    <template #header-checkbox>
                        <span class="column-header checkbox-header">
                        </span>
                    </template>
                    <template #header-image>
                        <span class="column-header">{{ t('product.image') }}</span>
                    </template>
                    <template #header-name>
                        <span class="column-header" data-sortable="true" @click="toggleSort('name')">
                            {{ t('product.name') }}
                            <span v-if="sortField === 'name'">{{ sortOrder === 1 ? '▲' : '▼' }}</span>
                        </span>
                    </template>
                    <template #header-sku>
                        <span class="column-header" data-sortable="true" @click="toggleSort('sku')">
                            {{ t('sku') }}
                            <span v-if="sortField === 'sku'">{{ sortOrder === 1 ? '▲' : '▼' }}</span>
                        </span>
                    </template>
                    <template #header-price>
                        <span class="column-header" data-sortable="true" @click="toggleSort('price')">
                            {{ t('product.price') }}
                            <span v-if="sortField === 'price'">{{ sortOrder === 1 ? '▲' : '▼' }}</span>
                        </span>
                    </template>
                    <template #header-stock>
                        <span class="column-header" data-sortable="true" @click="toggleSort('stock')">
                            {{ t('product.stock') }}
                            <span v-if="sortField === 'stock'">{{ sortOrder === 1 ? '▲' : '▼' }}</span>
                        </span>
                    </template>
                    <template #header-variations>
                        <span class="column-header">
                            {{ t('product.variations') }}
                        </span>
                    </template>
                    <template #header-actions>
                        <span class="column-header">{{ t('actions') }}</span>
                    </template>

                    <!-- Column Templates -->
                    <template #column-checkbox="{ data }">
                        <div class="checkbox-cell">
                            <IluriaCheckbox
                                :model-value="isProductSelected(data.id)"
                                :label="''"
                                :disabled="loading"
                                :aria-describedBy="t('product.bulk.selectProduct') + ' ' + data.name"
                                class="product-selection-checkbox"
                                @update:model-value="toggleProductSelection(data.id)"
                            />
                        </div>
                    </template>
                    <template #column-image="{ data }">
                        <div class="product-image-cell clickable-cell" @click="editProduct(data.id)">
                            <div class="product-image-container">
                                <img v-if="getProductImage(data)" :src="getProductImage(data)" alt="Product" class="product-image" />
                                <div v-else class="product-image-placeholder">
                                    <i class="fas fa-image"></i>
                                </div>
                            </div>
                        </div>
                    </template>
                    <template #column-name="{ data }">
                        <div class="product-info clickable-cell" @click="editProduct(data.id)">
                            <h3 class="product-name" :title="data.name">
                                {{ truncateText(data.name, 40) }}
                            </h3>
                            <p class="product-description" v-if="data.description" :title="stripHtml(data.description)">
                                {{ truncateText(stripHtml(data.description), 60) }}
                            </p>
                        </div>
                    </template>
                    <template #column-sku="{ data }">
                        <span class="product-sku clickable-cell" @click="editProduct(data.id)">{{ data.sku || '-' }}</span>
                    </template>
                    <template #column-price="{ data }">
                        <span class="product-price clickable-cell" @click="editProduct(data.id)">{{ formatPriceRange(data) }}</span>
                    </template>
                    <template #column-stock="{ data }">
                        <span class="product-stock clickable-cell" :class="getStockClass(getTotalStock(data))" @click="editProduct(data.id)">
                            {{ getTotalStock(data) }}
                        </span>
                    </template>
                    <template #column-variations="{ data }">
                        <span v-if="hasVariations(data)" class="variation-badge variation-badge-active clickable-cell" @click="editProduct(data.id)">
                            {{ data.variations.length }}
                        </span>
                        <span v-else class="variation-badge variation-badge-inactive clickable-cell" @click="editProduct(data.id)">
                            {{ t('product.noVariations') }}
                        </span>
                    </template>
                    <template #column-actions="{ data }">
                        <div class="action-buttons">
                            <IluriaButton 
                                color="text-primary" 
                                size="small" 
                                :hugeIcon="PencilEdit01Icon" 
                                @click.prevent="editProduct(data.id)"
                                :title="t('product.edit')"
                            />
                            <IluriaButton 
                                color="text-secondary" 
                                size="small" 
                                :hugeIcon="Copy01Icon" 
                                @click.prevent="confirmClone(data)" 
                                title="Clonar" 
                            />
                            <IluriaButton 
                                color="text-danger" 
                                size="small" 
                                :hugeIcon="Delete01Icon" 
                                @click.prevent="confirmDelete(data)"
                                :title="t('product.delete')"
                            />
                        </div>
                    </template>
                    
                    <!-- Empty State -->
                    <template #empty>
                        <div class="empty-state">
                            <div class="empty-icon">
                                <i class="fas fa-box-open"></i>
                            </div>
                            <h3 class="empty-title">{{ t('product.noProductsFound') }}</h3>
                            <p class="empty-description">Comece criando seu primeiro produto</p>
                            <IluriaButton @click="addProduct" :hugeIcon="PlusSignSquareIcon" class="mt-4">
                                {{ t('addProduct') }}
                            </IluriaButton>
                        </div>
                    </template>

                    <!-- Loading State -->
                    <template #loading>
                        <div class="loading-state">
                            <div class="loading-spinner"></div>
                            <span>{{ t('product.loading') }}</span>
                        </div>
                    </template>
                    
                    <!-- Expansion Content -->
                    <template #expansion="{data}">
                        <div class="expansion-content">
                            <!-- Attribute Filters -->
                            <div class="variation-filters" v-if="getAvailableAttributes(data).length > 0">
                                <h4 class="filters-title">{{ t('product.filterByAttributes') }}:</h4>
                                <div class="filter-groups">
                                    <div v-for="attr in getAvailableAttributes(data)" :key="attr.name" class="filter-group">
                                        <span class="filter-label">{{ attr.name }}:</span>
                                        <div class="filter-options">
                                            <button 
                                                v-for="value in attr.values" 
                                                :key="value"
                                                type="button"
                                                class="filter-option"
                                                :class="{ 'active': isAttributeSelected(data.id, attr.name, value) }"
                                                @click="toggleAttributeFilter(data.id, attr.name, value)"
                                            >
                                                {{ value }}
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            
                            <!-- Variations Table -->
                            <IluriaDataTable
                                :value="getFilteredVariations(data)"
                                class="variations-table"
                                :pt="{
                                    root: 'border border-gray-200 rounded-lg overflow-hidden mt-4',
                                    header: 'bg-gray-50',
                                    tbody: 'bg-white'
                                }"
                            >
                                <Column field="image" headerStyle="width: 60px" style="text-align: center">
                                    <template #body="{data: variation}">
                                        <div class="variation-image-container">
                                            <div v-if="getVariationPhoto(variation)" class="variation-image">
                                                <img :src="getVariationPhoto(variation)" alt="Variation" />
                                            </div>
                                            <div v-else-if="variation.attributes?.Cor" 
                                                 class="color-preview" 
                                                 :style="{ backgroundColor: variation.attributes.Cor }">
                                            </div>
                                            <div v-else class="variation-image-placeholder">
                                                <i class="fas fa-image"></i>
                                            </div>
                                        </div>
                                    </template>
                                </Column>
                                
                                <Column field="attributes" :header="t('product.variation')" style="text-align: left">
                                    <template #body="{data: variation}">
                                        <span class="variation-attributes">
                                            {{ formatVariationAttributes(variation.attributes) }}
                                        </span>
                                    </template>
                                </Column>
                                
                                <Column field="price" :header="t('product.price')" headerStyle="width: 120px" style="text-align: right">
                                    <template #body="{data: variation}">
                                        <span class="variation-price">{{ formatPrice(variation.price) }}</span>
                                    </template>
                                </Column>
                                
                                <Column field="stock" :header="t('product.stock')" headerStyle="width: 100px" style="text-align: center">
                                    <template #body="{data: variation}">
                                        <span class="variation-stock" :class="getStockClass(variation.stockQuantity || 0)">
                                            {{ variation.stockQuantity || 0 }}
                                        </span>
                                    </template>
                                </Column>
                            </IluriaDataTable>
                        </div>
                    </template>
                </IluriaDataTable>
            </div>

            <!-- Pagination -->
            <!-- Bulk Action Bar -->
            <IluriaBulkActionBar
                :selected-count="selectedProducts.size"
                :actions="bulkActions"
                :loading="false"
                :disabled="loading"
                entity-name="produto"
                entity-name-plural="produtos"
                @action-click="handleBulkActionClick"
            />

            <div class="pagination-container" v-if="totalPages > 1">
                <IluriaPagination
                    :current-page="currentPage"
                    :total-pages="totalPages"
                    @go-to-page="changePage"
                />
            </div>


        <!-- Confirmation Dialog -->
        <IluriaConfirmationModal
            :is-visible="showConfirmModal"
            :title="confirmModalTitle"
            :message="confirmModalMessage"
            :confirm-text="confirmModalConfirmText"
            :cancel-text="confirmModalCancelText"
            :type="confirmModalType"
            @confirm="handleConfirm"
            @cancel="handleCancel"
        />
    </div>
</template>

<script setup>
import { useI18n } from 'vue-i18n'
import { productsApi } from '@/services/product.service'
import { attributesApi } from '@/services/attributes.service'
import { ref, onMounted, computed } from 'vue'
import IluriaButton from '@/components/iluria/IluriaButton.vue'
import IluriaHeader from '@/components/iluria/IluriaHeader.vue'
import { useRouter } from 'vue-router'
import { Delete01Icon, PencilEdit01Icon, PlusSignSquareIcon, Upload01Icon, Download01Icon } from '@hugeicons-pro/core-stroke-rounded'
import { Copy01Icon } from '@hugeicons-pro/core-bulk-rounded'
import IluriaPagination from '../../components/iluria/IluriaPagination.vue'
import IluriaInputText from '../../components/iluria/form/IluriaInputText.vue'
import IluriaConfirmationModal from '@/components/iluria/modal/IluriaConfirmationModal.vue'
import IluriaDataTable from '@/components/iluria/IluriaDataTable.vue'
import IluriaBulkActionBar from '@/components/iluria/IluriaBulkActionBar.vue'
import IluriaCheckbox from '@/components/iluria/form/IluriaCheckbox.vue'
import Column from 'primevue/column'
import { useToast } from '@/services/toast.service'

const { t } = useI18n()
const router = useRouter()
const toast = useToast()

// Custom buttons for header
const headerCustomButtons = ref([
    {
        text: t('product.import.title'),
        icon: Upload01Icon,
        color: 'secondary',
        variant: 'outline',
        action: 'import'
    },
    {
        text: t('product.export.title'),
        icon: Download01Icon,
        color: 'secondary',
        variant: 'outline',
        action: 'export'
    }
])
// Modal state
const showConfirmModal = ref(false)
const confirmModalTitle = ref('')
const confirmModalMessage = ref('')
const confirmModalConfirmText = ref('Confirmar')
const confirmModalCancelText = ref('Cancelar')
const confirmModalType = ref('info')
const confirmCallback = ref(null)

// Bulk selection state
const selectedProducts = ref(new Set())

const mainTableColumns = computed(() => [
    { field: 'checkbox', headerClass: 'col-checkbox', class: 'col-checkbox' },
    { field: 'image', headerClass: 'col-image', class: 'col-image' },
    { field: 'name', headerClass: 'col-flex', class: 'col-flex' },
    { field: 'sku', headerClass: 'col-sku', class: 'col-sku' },
    { field: 'price', headerClass: 'col-price', class: 'col-price' },
    { field: 'stock', headerClass: 'col-stock', class: 'col-stock' },
    { field: 'variations', headerClass: 'col-variations', class: 'col-variations' },
    { field: 'actions', headerClass: 'col-actions', class: 'col-actions' }
]);

const sortField = ref(null);
const sortOrder = ref(null);

const toggleSort = (field) => {
    if (sortField.value === field) {
        sortOrder.value *= -1;
    } else {
        sortField.value = field;
        sortOrder.value = 1;
    }
};

// Bulk selection functions
const toggleProductSelection = (productId) => {
    if (selectedProducts.value.has(productId)) {
        selectedProducts.value.delete(productId)
    } else {
        selectedProducts.value.add(productId)
    }
    selectedProducts.value = new Set(selectedProducts.value)
}

const clearSelection = () => {
    selectedProducts.value.clear()
    selectedProducts.value = new Set(selectedProducts.value)
}

const isProductSelected = (productId) => {
    return selectedProducts.value.has(productId)
}

// Bulk delete
const confirmBulkDelete = () => {
    const count = selectedProducts.value.size
    const entityKey = count === 1 ? 'product.bulk.product' : 'product.bulk.products'
    const messageKey = count === 1 ? 'product.bulk.confirmDeleteMessageSingle' : 'product.bulk.confirmDeleteMessage'
    
    showConfirmDanger(
        t(messageKey, { count, entity: t(entityKey) }),
        t('product.bulk.confirmDeleteTitle'),
        () => bulkDeleteProducts()
    )
}

const bulkDeleteProducts = async () => {
    if (selectedProducts.value.size === 0) return
    
    try {
        const productIds = Array.from(selectedProducts.value)
        const response = await productsApi.deleteProducts(productIds)
        
        const count = response.deletedCount || selectedProducts.value.size
        const entityKey = count === 1 ? 'product.bulk.product' : 'product.bulk.products'
        const messageKey = count === 1 ? 'product.bulk.deleteSuccessSingle' : 'product.bulk.deleteSuccess'
        
        toast.showSuccess(t(messageKey, { count, entity: t(entityKey) }))
        
        clearSelection()
        loadProducts()
    } catch (error) {
        console.error('Error bulk deleting products:', error)
        toast.showError(t('product.bulk.deleteError', { entity: t('product.bulk.products') }))
    }
}

// Bulk actions for the action bar
const bulkActions = computed(() => [
    {
        text: t('product.bulk.deleteSelected'),
        icon: Delete01Icon,
        color: 'danger',
        variant: 'solid',
        callback: confirmBulkDelete,
        loading: false
    }
])

const handleBulkActionClick = ({ action }) => {
    // O evento é tratado pelo IluriaBulkActionBar que chama action.callback automaticamente
    // Este handler pode ser usado para lógica adicional se necessário
}

const sortedProducts = computed(() => {
    if (!sortField.value) {
        return products.value;
    }
    return [...products.value].sort((a, b) => {
        let valA, valB;

        switch(sortField.value) {
            case 'stock':
                valA = getTotalStock(a);
                valB = getTotalStock(b);
                break;
            case 'price':
                valA = a.price;
                valB = b.price;
                break;
            default:
                valA = a[sortField.value];
                valB = b[sortField.value];
        }

        if (typeof valA === 'string') {
            return valA.localeCompare(valB) * sortOrder.value;
        }
        return (valA - valB) * sortOrder.value;
    });
});

const addProduct = () => {
    router.push('/products/new')
}

// Handle custom button clicks
const handleCustomButtonClick = (index, button) => {
    if (button.action === 'import') {
        router.push('/products/import')
    } else if (button.action === 'export') {
        router.push('/products/export')
    }
}

const products = ref([])
const loading = ref(true)
const currentPage = ref(0)
const totalPages = ref(0)
const totalElements = ref(0)
const expandedRows = ref([])
const attributeFilters = ref({})
const filters = ref({
    name: '',
    onlyInStock: false
})

const loadProducts = async () => {
    loading.value = true
    try {
        const params = {
            page: currentPage.value,
            size: 10,
            name: filters.value.name,
            stockQuantity: filters.value.onlyInStock ? 1 : null
        }

        const response = await productsApi.getProductsWithVariations(params)
        products.value = response.data.content
        totalPages.value = response.data.page.totalPages
        totalElements.value = response.data.page.totalElements

    } catch (error) {
        console.error('Error loading products:', error)
    } finally {
        loading.value = false
    }
}

const changePage = (page) => {
    currentPage.value = page
    loadProducts()
}

const editProduct = (id) => {
    router.push(`/products/${id}`)
}

// Modal control functions
const showConfirmDanger = (message, title, onConfirm) => {
    confirmModalMessage.value = message
    confirmModalTitle.value = title
    confirmModalConfirmText.value = t('delete')
    confirmModalCancelText.value = t('cancel')
    confirmModalType.value = 'error'
    confirmCallback.value = onConfirm
    showConfirmModal.value = true
}

const showConfirm = (message, title, onConfirm) => {
    confirmModalMessage.value = message
    confirmModalTitle.value = title
    confirmModalConfirmText.value = t('confirm')
    confirmModalCancelText.value = t('cancel')
    confirmModalType.value = 'info'
    confirmCallback.value = onConfirm
    showConfirmModal.value = true
}

const handleConfirm = () => {
    if (confirmCallback.value) {
        confirmCallback.value()
    }
    showConfirmModal.value = false
}

const handleCancel = () => {
    showConfirmModal.value = false
}

const confirmDelete = (product) => {
    showConfirmDanger(
        t('product.confirmDelete', { name: product.name }),
        t('product.confirmDeleteTitle'),
        () => deleteProduct(product.id)
    )
}

const deleteProduct = async (id) => {
    try {
        await productsApi.delete(id)
        loadProducts()
    } catch (error) {
        console.error('Error deleting product:', error)
    }
}

let searchTimeout = null
const debouncedSearch = () => {
    clearTimeout(searchTimeout)
    searchTimeout = setTimeout(() => {
        currentPage.value = 0
        loadProducts()
    }, 400)
}

const handleSearch = (searchValue) => {
    filters.value.name = searchValue
    debouncedSearch()
}

const rowClass = (data) => {
    return {
        'has-variation': hasVariations(data)
    };
};

const hasVariations = (product) => {
    return product.variations && product.variations.length > 0
}

const getTotalStock = (product) => {
    if (!product) return 0
    
    if (hasVariations(product)) {
        return product.variations.reduce((total, variation) => {
            return total + (variation.stockQuantity || 0)
        }, 0)
    }
    
    return product.stockQuantityTotal || 0
}

const getStockClass = (stock) => {
    if (stock === 0) return 'stock-empty'
    if (stock < 10) return 'stock-low'
    return 'stock-good'
}

const formatPriceRange = (product) => {
    if (!product) return ''

    if (!hasVariations(product)) {
        return formatPrice(product.price)
    }

    const prices = product.variations.map(v => v.price)
    const minPrice = Math.min(...prices)
    const maxPrice = Math.max(...prices)

    if (minPrice === maxPrice) {
        return formatPrice(minPrice)
    }

    return `${formatPrice(minPrice)} ~ ${formatPrice(maxPrice)}`
}

const formatPrice = (value) => {
    return new Intl.NumberFormat('pt-BR', {
        style: 'currency',
        currency: 'BRL'
    }).format(value)
}

const formatVariationAttributes = (attributes) => {
    if (!attributes) return ''
    return Object.entries(attributes).map(([key, value]) => `${key}: ${value}`).join(', ')
}

const getVariationPhoto = (variation) => {
    if (variation.photos && variation.photos.length > 0) {
        const photo = variation.photos.find(p => p.position === 0) || variation.photos[0];
        if (photo && photo.url) {
            return photo.url;
        }
    }
    return null;
};

const getProductImage = (product) => {
    if (product.variations && product.variations.length > 0) {
        const variation = product.variations.find(v => v.photos && v.photos.length > 0);
        if (variation) {
            return getVariationPhoto(variation);
        }
    }
    
    if (product.photos && product.photos.length > 0) {
        const photo = product.photos.find(p => p.position === 0) || product.photos[0];
        if (photo && photo.url) {
            return photo.url;
        }
    }
    
    return product.imageUrl || null;
};

const getAvailableAttributes = (product) => {
    if (!hasVariations(product)) return []
    
    const attributes = {}
    
    product.variations.forEach(variation => {
        if (variation.attributes) {
            Object.entries(variation.attributes).forEach(([key, value]) => {
                if (!attributes[key]) {
                    attributes[key] = new Set()
                }
                attributes[key].add(value)
            })
        }
    })
    
    return Object.entries(attributes).map(([name, valuesSet]) => ({
        name,
        values: Array.from(valuesSet)
    }))
}

const toggleAttributeFilter = (productId, attrName, value) => {
    if (!attributeFilters.value[productId]) {
        attributeFilters.value[productId] = {}
    }
    
    if (!attributeFilters.value[productId][attrName]) {
        attributeFilters.value[productId][attrName] = []
    }
    
    const index = attributeFilters.value[productId][attrName].indexOf(value)
    
    if (index === -1) {
        attributeFilters.value[productId][attrName].push(value)
    } else {
        attributeFilters.value[productId][attrName].splice(index, 1)
        
        if (attributeFilters.value[productId][attrName].length === 0) {
            delete attributeFilters.value[productId][attrName]
            
            if (Object.keys(attributeFilters.value[productId]).length === 0) {
                delete attributeFilters.value[productId]
            }
        }
    }
}

const isAttributeSelected = (productId, attrName, value) => {
    return attributeFilters.value[productId]?.[attrName]?.includes(value) || false
}

const getFilteredVariations = (product) => {
    if (!hasVariations(product)) return []
    
    if (!attributeFilters.value[product.id] || Object.keys(attributeFilters.value[product.id]).length === 0) {
        return product.variations
    }
    
    return product.variations.filter(variation => {
        if (!variation.attributes) return false
        
        return Object.entries(attributeFilters.value[product.id]).every(([attrName, selectedValues]) => {
            const variationValue = variation.attributes[attrName]
            return selectedValues.includes(variationValue)
        })
    })
}

// Utility functions for text truncation
const truncateText = (text, maxLength) => {
    if (!text) return ''
    if (text.length <= maxLength) return text
    return text.substring(0, maxLength) + '...'
}

const stripHtml = (html) => {
    if (!html) return ''
    const tmp = document.createElement('div')
    tmp.innerHTML = html
    return tmp.textContent || tmp.innerText || ''
}

const confirmClone = (product) => {
    let message = t('product.confirmClone', { name: product.name })
    if (message === 'product.confirmClone') {
        message = `Deseja clonar ${product.name}?`
    }
    let header = t('product.confirmCloneTitle')
    if (header === 'product.confirmCloneTitle') {
        header = 'Confirmar Clonagem'
    }
    showConfirm(
        message,
        header,
        () => cloneProduct(product.id)
    )
}

// Gera um nome único para o clone (Produto X (Cópia), Produto X (Cópia 2), ...)
const generateCloneName = async (originalName) => {
    // Expressão para detectar sufixos de cópia
    const copyRegex = /^(.*) \(Cópia(?: (\d+))?\)$/i;

    // Extrai nome base (sem sufixo de cópia)
    let baseName = originalName;
    const regexMatch = originalName.match(copyRegex);
    if (regexMatch) {
        baseName = regexMatch[1].trim();
    }

    try {
        // Busca produtos que contenham o mesmo nome base
        const resp = await productsApi.getAll({ page: 0, size: 500, name: baseName });
        const list = resp?.data?.content || [];

        let highestCopy = 0;

        list.forEach(p => {
            if (!p?.name) return;

            if (p.name === `${baseName} (Cópia)`) {
                highestCopy = Math.max(highestCopy, 1);
            } else {
                const m = p.name.match(copyRegex);
                if (m && m[2]) {
                    const num = parseInt(m[2]);
                    if (!isNaN(num)) {
                        highestCopy = Math.max(highestCopy, num);
                    }
                }
            }
        });

        if (highestCopy === 0) {
            // Nenhuma cópia existente
            return `${baseName} (Cópia)`;
        }

        // Incrementa número
        return `${baseName} (Cópia ${highestCopy + 1})`;
    } catch (err) {
        // Fallback simples se falhar a checagem
        return `${baseName} (Cópia)`;
    }
};

// Gera um SKU único para o clone (SKU-copia, SKU-copia-2...)
const generateCloneSku = async (originalSku) => {
    if (!originalSku) return undefined;

    const skuRegex = /^(.*)-copia(?:-(\d+))?$/i;
    let baseSku = originalSku;
    const match = originalSku.match(skuRegex);
    if (match) {
        baseSku = match[1].trim();
    }

    try {
        // Testando duplicidade de SKU
        const resp = await productsApi.getAll({ page: 0, size: 500, name: '' });
        const list = resp?.data?.content || [];

        let highestCopy = 0;

        list.forEach(p => {
            if (!p?.sku) return;

            if (p.sku === `${baseSku}-copia`) {
                highestCopy = Math.max(highestCopy, 1);
            } else {
                const m = p.sku.match(skuRegex);
                if (m && m[2]) {
                    const num = parseInt(m[2]);
                    if (!isNaN(num)) {
                        highestCopy = Math.max(highestCopy, num);
                    }
                }
            }
        });

        if (highestCopy === 0) return `${baseSku}-copia`;

        return `${baseSku}-copia-${highestCopy + 1}`;
    } catch (err) {
        return `${baseSku}-copia`;
    }
};

const cloneProduct = async (id) => {
    try {
        loading.value = true

        // Busca produto e respectivos atributos em paralelo
        const [product, prodAttributes] = await Promise.all([
            productsApi.getById(id),
            attributesApi.getProductAttributes(id).catch(() => [])
        ])

        if (!product) return

        // Remove identificadores e metadados que não devem ser duplicados
        const {
            id: _discardId,
            storeId: _discardStoreId,
            createdAt: _discardCreated,
            updatedAt: _discardUpdated,
            variations: originalVariations = [],
            photos: originalPhotos = [],
            ...rest
        } = product

        // Clonar variações, preservando fotos, mas removendo IDs
        const clonedVariations = Array.isArray(originalVariations)
            ? originalVariations.map(({ id: _vId, photos: vPhotos = [], ...vRest }) => ({ ...vRest, photos: vPhotos }))
            : []

        // Gera nome único para o novo produto
        const uniqueCloneName = await generateCloneName(rest.name);

        // Gera SKU único
        const uniqueSku = await generateCloneSku(rest.sku);

        // Monta payload do novo produto (sem as imagens originais)
        const newProductPayload = {
            ...rest,
            name: uniqueCloneName,
            sku: uniqueSku,
            // Mantemos o código de barras e demais informações relevantes
            urlSlug: undefined,
            status: 'DRAFT',
            variations: clonedVariations,
            photos: [] // Inicialmente vazio, as imagens serão clonadas depois
        }

        const response = await productsApi.create(newProductPayload)
        const newProductId = response?.data?.id

        if (!newProductId) {
            throw new Error('Falha ao criar produto clonado')
        }

        // Caso o produto tenha atributos, replicá-los no clone
        if (Array.isArray(prodAttributes) && prodAttributes.length > 0) {
            const attrsPayload = prodAttributes.map(attr => ({
                attributeId: attr.attributeId || attr.attribute?.id || attr.id,
                attributeValues: attr.attributeValues || attr.values || [],
                filter: attr.isFilter ?? attr.filter ?? false
            }))
            try {
                await attributesApi.saveProductAttributes(newProductId, attrsPayload)
            } catch (attrErr) {
                console.error('Erro ao clonar atributos do produto:', attrErr)
            }
        }

        // Clonar as imagens se existirem
        const hasImages = (originalPhotos && originalPhotos.length > 0) ||
                         (originalVariations && originalVariations.some(v => v.photos && v.photos.length > 0))

        if (hasImages) {
            try {
                await productsApi.cloneImages(id, newProductId)
            } catch (imageErr) {
                console.error('Erro ao clonar imagens do produto:', imageErr)
                // Não falha a operação se as imagens não puderem ser clonadas
            }
        }

        toast.showSuccess(t('product.cloneSuccess') || 'Clonado com sucesso!')
        router.push(`/products/${newProductId}`)
    } catch (error) {
        console.error('Error cloning product:', error)
        toast.showError(t('product.cloneError') || 'Erro ao clonar.')
    } finally {
        loading.value = false
    }
}

// Clear selection when products change
const prevProductIds = ref(new Set())
const updateSelection = () => {
    const currentIds = new Set(sortedProducts.value.map(p => p.id))
    const newSelection = new Set()
    
    for (const id of selectedProducts.value) {
        if (currentIds.has(id)) {
            newSelection.add(id)
        }
    }
    
    selectedProducts.value = newSelection
    prevProductIds.value = currentIds
}

// Watch for products changes to update selection
const watchProducts = computed(() => sortedProducts.value.map(p => p.id))
watchProducts.value // trigger reactivity

onMounted(() => {
    loadProducts()
})
</script>

<style scoped>
.product-list-container {
    padding: 24px;
    max-width: 1400px;
    margin: 0 auto;
}

/* Header */
.page-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 32px;
    padding-bottom: 24px;
    border-bottom: 1px solid var(--iluria-color-border);
    transition: border-color 0.3s ease;
}

.header-content h1.page-title {
    font-size: 28px;
    font-weight: 700;
    color: var(--iluria-color-text-primary);
    margin: 0;
    line-height: 1.2;
    transition: color 0.3s ease;
}

.header-content .page-subtitle {
    font-size: 16px;
    color: var(--iluria-color-text-secondary);
    margin: 4px 0 0 0;
    transition: color 0.3s ease;
}

.header-actions {
    display: flex;
    align-items: center;
    gap: 16px;
}

.search-container {
    position: relative;
}

.search-input {
    min-width: 300px;
}

/* Table Styles */
.table-wrapper {
    margin-bottom: 24px;
}

/* Column width definitions */
:deep(.col-checkbox) {
  width: 40px !important;
  min-width: 40px !important;
  max-width: 40px !important;
  text-align: center;
}

:deep(.col-image) {
  width: 60px !important;
  min-width: 60px !important;
  max-width: 60px !important;
  text-align: center;
}

:deep(.col-flex) { width: auto; text-align: left; }
:deep(.col-sku) { width: 150px; text-align: center; }
:deep(.col-price) { width: 140px; text-align: center; }
:deep(.col-stock) { width: 100px; text-align: center; }
:deep(.col-variations) { width: 140px; text-align: center; }
:deep(.col-actions) { width: 140px; text-align: center; }

/* General table styling */
:deep(.product-table) {
  border-radius: 8px;
  overflow: hidden;
  box-shadow: var(--iluria-shadow-sm);
  background: var(--iluria-color-surface) !important;
  border: 1px solid var(--iluria-color-border) !important;
}

:deep(.product-table .p-datatable-table) {
  table-layout: auto;
  width: 100%;
}

:deep(.product-table .p-datatable-thead > tr > th) {
  background: var(--iluria-color-sidebar-bg) !important;
  color: var(--iluria-color-text-primary) !important;
  font-weight: 600;
  font-size: 12px;
  text-transform: uppercase;
  letter-spacing: 0.05em;
  border-bottom: 1px solid var(--iluria-color-border) !important;
  padding: 16px;
  text-align: center;
}

:deep(.product-table .p-datatable-thead > tr > th.col-flex) {
  text-align: left;
}

:deep(.product-table .p-datatable-tbody > tr) {
  border-bottom: 1px solid var(--iluria-color-border) !important;
  background: var(--iluria-color-surface) !important;
}

:deep(.product-table .p-datatable-tbody > tr:hover) {
  background: var(--iluria-color-hover) !important;
}

:deep(.product-table .p-datatable-tbody > tr > td) {
  padding: 16px;
  border: none;
  border-bottom: 1px solid var(--iluria-color-border);
  vertical-align: middle;
  background: inherit !important;
  color: var(--iluria-color-text) !important;
  font-size: 14px;
  text-align: center;
}

:deep(.product-table .p-datatable-tbody > tr > td.col-flex) {
  text-align: left;
}


/* Column header style */
:deep(.product-table th .column-header) {
  display: block;
  width: 100%;
  text-align: inherit;
  cursor: default;
  user-select: none;
}

:deep(.product-table th .column-header[data-sortable="true"]) {
  cursor: pointer;
}

/* Product Image */
.product-image-cell {
    display: flex;
    justify-content: center;
}

.product-image-container {
    width: 48px;
    height: 48px;
    border-radius: 8px;
    overflow: hidden;
    background: var(--iluria-color-sidebar-bg);
    display: flex;
    align-items: center;
    justify-content: center;
    border: 1px solid var(--iluria-color-border);
    transition: all 0.3s ease;
}

.product-image {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.product-image-placeholder {
    color: var(--iluria-color-text-muted);
    font-size: 18px;
    transition: color 0.3s ease;
}

/* Product Info */
.product-info {
    min-width: 0;
    max-width: 100%;
    overflow: hidden;
    text-align: left;
}

.product-name {
    font-size: 14px;
    font-weight: 600;
    color: var(--iluria-color-text-primary);
    margin: 0 0 4px 0;
    line-height: 1.4;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    transition: color 0.3s ease;
}

.product-description {
    font-size: 13px;
    color: var(--iluria-color-text-secondary);
    margin: 0;
    line-height: 1.4;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    transition: color 0.3s ease;
}

/* SKU */
.product-sku {
    font-size: 13px;
    color: var(--iluria-color-text-secondary);
    font-family: monospace;
    transition: color 0.3s ease;
}

/* Price */
.product-price {

    color: #059669;

}

/* Stock */
.product-stock {
    font-size: 14px;
    font-weight: 600;
    padding: 4px 8px;
    border-radius: 12px;
    text-align: center;
}

.stock-good {
    background: #d1fae5;
    color: #059669;
}

.stock-low {
    background: #fef3c7;
    color: #d97706;
}

.stock-empty {
    background: #fee2e2;
    color: #dc2626;
}

/* Variations Badge */
.variation-badge {
    font-size: 12px;
    font-weight: 500;
    padding: 4px 8px;
    border-radius: 12px;
    transition: all 0.3s ease;
    white-space: nowrap;
    display: inline-block;
    min-width: fit-content;
    text-align: center;
}

.variation-badge-active {
    background: var(--iluria-color-primary);
    color: var(--iluria-color-primary-contrast);
}

.variation-badge-inactive {
    background: var(--iluria-color-sidebar-bg);
    color: var(--iluria-color-text-muted);
}

/* Clickable Cell Styles */
.clickable-cell {
    cursor: pointer;
    transition: all 0.2s ease;
    user-select: none;
}

.clickable-cell:hover {
    background-color: rgba(var(--iluria-color-primary-rgb), 0.05);
    border-radius: 4px;
}

.clickable-cell:active {
    background-color: rgba(var(--iluria-color-primary-rgb), 0.1);
    transform: scale(0.98);
}

/* Action Buttons */
.action-buttons {
    display: flex;
    gap: 4px;
    justify-content: center;
}

.action-buttons .btn {
    transition: all 0.2s ease;
}

.action-buttons .btn:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
}

/* Empty State */
.empty-state {
    text-align: center;
    padding: 48px 24px;
    background: var(--iluria-color-surface);
    transition: background-color 0.3s ease;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
}

.empty-icon {
    font-size: 48px;
    color: var(--iluria-color-text-muted);
    margin-bottom: 16px;
    transition: color 0.3s ease;
    display: flex;
    justify-content: center;
    align-items: center;
}

.empty-title {
    font-size: 18px;
    font-weight: 600;
    color: var(--iluria-color-text-primary);
    margin: 0 0 8px 0;
    transition: color 0.3s ease;
    text-align: center;
}

.empty-description {
    font-size: 14px;
    color: var(--iluria-color-text-secondary);
    margin: 0 0 16px 0;
    transition: color 0.3s ease;
    text-align: center;
    line-height: 1.5;
    max-width: 400px;
}

/* Loading State */
.loading-state {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 12px;
    padding: 32px;
    color: var(--iluria-color-text-secondary);
    font-size: 14px;
    background: var(--iluria-color-surface);
    transition: all 0.3s ease;
}

.loading-spinner {
    width: 20px;
    height: 20px;
    border: 2px solid #e5e7eb;
    border-top: 2px solid #3b82f6;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Expansion Content */
.expansion-content {
    padding: 24px;
    background: var(--iluria-color-sidebar-bg);
    border-top: 1px solid var(--iluria-color-border);
    transition: all 0.3s ease;
}

.variation-filters {
    margin-bottom: 24px;
}

.filters-title {
    font-size: 14px;
    font-weight: 600;
    color: var(--iluria-color-text-primary);
    margin: 0 0 12px 0;
    transition: color 0.3s ease;
}

.filter-groups {
    display: flex;
    flex-wrap: wrap;
    gap: 16px;
}

.filter-group {
    display: flex;
    align-items: center;
    gap: 8px;
}

.filter-label {
    font-size: 13px;
    color: var(--iluria-color-text-secondary);
    font-weight: 500;
    min-width: 60px;
    transition: color 0.3s ease;
}

.filter-options {
    display: flex;
    gap: 4px;
}

.filter-option {
    padding: 4px 8px;
    border: 1px solid var(--iluria-color-border);
    border-radius: 6px;
    background: var(--iluria-color-surface);
    color: var(--iluria-color-text-primary);
    font-size: 12px;
    cursor: pointer;
    transition: all 0.2s ease;
}

.filter-option:hover {
    border-color: var(--iluria-color-primary);
    color: var(--iluria-color-primary);
    background: var(--iluria-color-hover);
}

.filter-option.active {
    background: var(--iluria-color-primary);
    border-color: var(--iluria-color-primary);
    color: var(--iluria-color-primary-contrast);
}

/* Variations Table */
:deep(.variations-table) {
    font-size: 13px;
}

:deep(.variations-table .p-datatable-thead > tr > th) {
    padding: 12px 16px;
    font-size: 11px;
}

:deep(.variations-table .p-datatable-tbody > tr > td) {
    padding: 12px 16px;
}

.variation-image-container {
    width: 32px;
    height: 32px;
    border-radius: 6px;
    overflow: hidden;
    background: #f3f4f6;
    display: flex;
    align-items: center;
    justify-content: center;
}

.variation-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.color-preview {
    width: 100%;
    height: 100%;
    border-radius: 6px;
    border: 1px solid #e5e7eb;
}

.variation-image-placeholder {
    color: #9ca3af;
    font-size: 12px;
}

.variation-attributes {
    font-size: 13px;
    color: #374151;
}


.variation-stock {
    font-size: 12px;
    font-weight: 500;
    padding: 2px 6px;
    border-radius: 8px;
}

/* Pagination */
.pagination-container {
    display: flex;
    justify-content: center;
    margin-top: 24px;
}

/* Responsive */
@media (max-width: 1024px) {
    .page-header {
        flex-direction: column;
        align-items: stretch;
        gap: 16px;
    }
    
    .header-actions {
        justify-content: space-between;
    }
    
    .search-input {
        min-width: 200px;
    }
}

@media (max-width: 768px) {
    .product-list-container {
        padding: 16px;
    }
    
    .filter-groups {
        flex-direction: column;
        gap: 12px;
    }
    
    .filter-group {
        flex-direction: column;
        align-items: flex-start;
        gap: 6px;
    }
    
    .search-input {
        min-width: 150px;
    }
    
    /* Mobile truncation adjustments */
    .product-name {
        font-size: 13px;
    }
    
    .product-description {
        font-size: 12px;
    }
    
    :deep(.product-table .p-datatable-tbody > tr > td) {
        padding: 12px 16px;
    }
    
    :deep(.product-table .p-datatable-thead > tr > th) {
        padding: 12px 16px;
        font-size: 11px;
    }
}

/* DataTable row expansion toggle button */
:deep(.p-datatable-row-toggle-button) {
    display: none !important;
}

:deep(.has-variation .p-datatable-row-toggle-button) {
    display: inline-flex !important;
}

/* Compact table cell styling following CustomerListView pattern */
:deep(.product-table .p-datatable-thead > tr > th.col-checkbox),
:deep(.product-table .p-datatable-tbody > tr > td.col-checkbox) {
  width: 40px !important;
  min-width: 40px !important;
  max-width: 40px !important;
  padding: 8px 4px !important;
  text-align: center;
}

:deep(.product-table .p-datatable-thead > tr > th.col-image),
:deep(.product-table .p-datatable-tbody > tr > td.col-image) {
  width: 60px !important;
  min-width: 60px !important;
  max-width: 60px !important;
  padding: 8px 4px !important;
  text-align: center;
}

/* Compact table cell overrides */
:deep(.product-table .p-datatable-tbody > tr > td.col-checkbox) {
  padding: 4px 2px !important;
  width: 40px !important;
  min-width: 40px !important;
  max-width: 40px !important;
}

:deep(.product-table .p-datatable-tbody > tr > td.col-image) {
  padding: 4px 2px !important;
  width: 60px !important;
  min-width: 60px !important;
  max-width: 60px !important;
}

/* Headers compactos */
:deep(.product-table .p-datatable-thead > tr > th.col-checkbox) {
  padding: 8px 2px !important;
  width: 40px !important;
  min-width: 40px !important;
  max-width: 40px !important;
}

:deep(.product-table .p-datatable-thead > tr > th.col-image) {
  padding: 8px 2px !important;
  width: 60px !important;
  min-width: 60px !important;
  max-width: 60px !important;
}

.checkbox-cell {
  width: 40px !important;
  min-width: 40px !important;
  max-width: 40px !important;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0 !important;
  margin: 0 !important;
}

.checkbox-header {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0 !important;
  margin: 0 !important;
}

/* Custom checkbox styling following CustomerListView */
.product-selection-checkbox {
  margin: 0 !important;
  padding: 0 !important;
  width: 16px !important;
  height: 16px !important;
}

.product-selection-checkbox :deep(.iluria-checkbox-wrapper) {
  width: 16px !important;
  margin: 0 !important;
  padding: 0 !important;
}

.product-selection-checkbox :deep(.checkbox-label) {
  display: none !important;
}

.product-selection-checkbox :deep(.iluria-checkbox) {
  margin: 0 !important;
  padding: 0 !important;
  flex-shrink: 0;
  width: 16px !important;
  height: 16px !important;
}
</style>
