import axios from 'axios'
import { API_URLS } from '@/config/api.config'
import userNotificationService from './userNotification.service'
import storeNotificationService from './storeNotification.service'

class NotificationService {
  constructor() {
    this.baseURL = `${API_URLS.STORE_BASE_URL}/email-notification-settings`
    this.userService = userNotificationService
    this.storeService = storeNotificationService
    this.wsConnection = null
  }

  /**
   * Busca apenas a contagem de notificações não lidas (mais eficiente para badge).
   */
  async getUnreadCount(type = 'user', storeId = null) {
    try {
      const baseUrl = type === 'store' 
        ? `${API_URLS.SETTINGS_BASE_URL}/notifications/store/unread`
        : `${API_URLS.SETTINGS_BASE_URL}/notifications/user/unread`
      
      // Request minimal data for count only
      const params = { page: 0, size: 1 }
      
      const response = await axios.get(baseUrl, { params })
      return response.data.unreadCount || 0
    } catch (error) {
      console.error('Error fetching unread count:', error)
      return 0
    }
  }

  /**
   * Busca notificações não lidas do usuário ou loja.
   */
  async getUnreadNotifications(type = 'user', storeId = null, page = 0, pageSize = 10) {
    try {
      const baseUrl = type === 'store' 
        ? `${API_URLS.SETTINGS_BASE_URL}/notifications/store/unread`
        : `${API_URLS.SETTINGS_BASE_URL}/notifications/user/unread`
      
      const params = { page, size: pageSize }
      
      const response = await axios.get(baseUrl, { params })
      return {
        notifications: response.data.notifications || [],
        unreadCount: response.data.unreadCount || 0,
        hasMore: response.data.hasMore || false,
        totalPages: response.data.totalPages || 1
      }
    } catch (error) {
      console.error('Error fetching notifications:', error)
      return {
        notifications: [],
        unreadCount: 0,
        hasMore: false,
        totalPages: 1
      }
    }
  }

  /**
   * Marca todas as notificações como lidas.
   */
  async markAllAsRead(type = 'user', storeId = null) {
    try {
      const baseUrl = type === 'store' 
        ? `${API_URLS.SETTINGS_BASE_URL}/notifications/store/mark-all-read`
        : `${API_URLS.SETTINGS_BASE_URL}/notifications/user/mark-all-read`
      
      
      const response = await axios.post(baseUrl)
      return response.data
    } catch (error) {
      console.error('Error marking all notifications as read:', error)
      throw error
    }
  }

  /**
   * NEW METHOD: Marks all notifications as read without removing them from the list.
   * This only removes unread badges but keeps notifications visible.
   */
  async markAllAsReadWithoutRemoving(type = 'user', storeId = null) {
    try {
      const baseUrl = type === 'store' 
        ? `${API_URLS.SETTINGS_BASE_URL}/notifications/store/mark-all-read-without-removing`
        : `${API_URLS.SETTINGS_BASE_URL}/notifications/user/mark-all-read-without-removing`
      
      const response = await axios.post(baseUrl)
      return response.data
    } catch (error) {
      console.error('Error marking all notifications as read without removing:', error)
      throw error
    }
  }

  /**
   * Marca uma notificação específica como lida.
   */
  async markNotificationAsRead(notificationId, type = 'user', storeId = null) {
    try {
      const url = `${API_URLS.SETTINGS_BASE_URL}/notifications/${notificationId}/mark-read`
      const response = await axios.post(url)
      return response.data
    } catch (error) {
      console.error('Error marking notification as read:', error)
      throw error
    }
  }

  /**
   * NEW METHOD: Marks a notification as read without removing it from the list.
   * This only removes the unread badge but keeps the notification visible.
   */
  async markNotificationAsReadWithoutRemoving(notificationId) {
    try {
      const url = `${API_URLS.SETTINGS_BASE_URL}/notifications/${notificationId}/mark-read-without-removing`
      const response = await axios.post(url)
      return response.data
    } catch (error) {
      console.error('Error marking notification as read without removing:', error)
      throw error
    }
  }

  /**
   * NEW METHOD: Deletes a notification for the current user without affecting other users.
   */
  async deleteNotificationForUser(notificationId) {
    try {
      const url = `${API_URLS.SETTINGS_BASE_URL}/notifications/${notificationId}/delete-for-user`
      const response = await axios.delete(url)
      return response.data
    } catch (error) {
      console.error('Error deleting notification for user:', error)
      throw error
    }
  }

  /**
   * NEW METHOD: Marks a notification as unread for the current user.
   */
  async markNotificationAsUnread(notificationId) {
    try {
      const url = `${API_URLS.SETTINGS_BASE_URL}/notifications/${notificationId}/mark-unread`
      const response = await axios.post(url)
      return response.data
    } catch (error) {
      console.error('Error marking notification as unread:', error)
      throw error
    }
  }

  /**
   * Cancela notificação relacionada a um convite quando o convite é cancelado.
   */
  async cancelNotificationByInvitation(invitationId) {
    try {
      const url = `${API_URLS.SETTINGS_BASE_URL}/notifications/cancel-by-invitation/${invitationId}`
      const response = await axios.delete(url)
      return response.data
    } catch (error) {
      throw error
    }
  }

  // Helper method to determine notification context
  getNotificationContext(currentRoute) {
    if (currentRoute.path.includes('/store/')) {
      const storeId = currentRoute.params.storeId
      return {
        type: 'store',
        storeId: storeId
      }
    }
    
    return {
      type: 'user',
      storeId: null
    }
  }

  /**
   * Busca as configurações de notificações por email do usuário
   * @returns {Promise} Configurações de notificação
   */
  async getEmailSettings() {
    try {
      const response = await axios.get(this.baseURL)
      return response.data
    } catch (error) {
      console.error('Erro ao buscar configurações de email:', error)
      return {
        newSales: true,
        productReviews: false,
        productQuestions: true,
        newsletterSubscriptions: false,
        newCustomerRegistrations: true
      }
    }
  }

  /**
   * Salva as configurações de notificações por email
   * @param {Object} settings - Configurações de notificação
   * @returns {Promise} Resposta da API
   */
  async saveEmailSettings(settings) {
    try {
      const response = await axios.post(this.baseURL, settings)
      return response.data
    } catch (error) {
      console.error('Erro ao salvar configurações de email:', error)
      throw error
    }
  }

  /**
   * Atualiza uma configuração específica (usando o método save)
   * @param {string} settingKey - Chave da configuração
   * @param {boolean} value - Valor da configuração
   * @returns {Promise} Resposta da API
   */
  async updateEmailSetting(settingKey, value) {
    try {
      // Busca configurações atuais
      const currentSettings = await this.getEmailSettings()
      // Atualiza a configuração específica
      const updatedSettings = {
        ...currentSettings,
        [settingKey]: value
      }
      // Salva as configurações atualizadas
      return await this.saveEmailSettings(updatedSettings)
    } catch (error) {
      console.error(`Erro ao atualizar configuração ${settingKey}:`, error)
      throw error
    }
  }

  // ===== New Comprehensive Notification System =====

  // User Notification Methods
  async getUserNotificationSettings() {
    return await this.userService.getUserNotificationSettings()
  }

  async saveUserNotificationSettings(settings) {
    return await this.userService.saveUserNotificationSettings(settings)
  }

  async updateUserNotificationSetting(settingKey, value) {
    return await this.userService.updateNotificationSetting(settingKey, value)
  }

  // Store Notification Methods
  async getStoreNotificationSettings(storeId) {
    return await this.storeService.getStoreNotificationSettings(storeId)
  }

  async updateStoreNotificationSettings(storeId, settings) {
    return await this.storeService.updateStoreNotificationSettings(storeId, settings)
  }

  async getUserNotificationPermissions(storeId) {
    return await this.storeService.getUserNotificationPermissions(storeId)
  }

  async updateUserNotificationPermission(storeId, userId, permission) {
    return await this.storeService.updateUserNotificationPermission(storeId, userId, permission)
  }

  async grantAllPermissions(storeId, userId) {
    return await this.storeService.grantAllPermissions(storeId, userId)
  }

  async revokeAllPermissions(storeId, userId) {
    return await this.storeService.revokeAllPermissions(storeId, userId)
  }

  // Utility Methods
  getUserNotificationTypes() {
    return [
      'stores', 'accountAndSafety', 'systemAndUpdates',
      'collabInvites', 'collabUpdates', 'roleChanges', 'doNotDisturb',
      'startTime', 'endTime', 'newsletter', 'specialOffers', 'feedbackSurveys',
      'securityAlerts', 'accountChanges', 'loginNotifications',
      'systemUpdates', 'maintenanceNotifications', 'featureAnnouncements',
      'promotionalEmails', 'productRecommendations',
      'mentions', 'comments', 'followNotifications',
      'weeklySummary', 'usageReports', 'emailFrequency',
      'quietHoursEnabled', 'quietHoursStart', 'quietHoursEnd', 'quietHoursTimezone'
    ]
  }

  getStoreNotificationTypes() {
    return this.storeService.getStoreNotificationTypes()
  }

  getFrequencyOptions() {
    return this.storeService.getFrequencyOptions()
  }

  // Default Settings
  getUserDefaultSettings() {
    return this.userService.getDefaultSettings()
  }

  getStoreDefaultSettings(storeId) {
    return this.storeService.getDefaultStoreSettings(storeId)
  }

  // Validation Methods
  validateUserSettings(settings) {
    return this.userService.validateAndCleanSettings(settings)
  }

  validateStoreSettings(settings) {
    return this.storeService.validateAndCleanStoreSettings(settings)
  }

  // WebSocket connection for real-time notifications
  connectWebSocket(userId, storeId = null) {
    // TODO: Implement WebSocket connection when backend WebSocket is ready
    try {
      const wsUrl = storeId 
        ? `ws://api/notifications/store/${storeId}/user/${userId}`
        : `ws://api/notifications/user/${userId}`    
      
    } catch (error) {
      console.error('Error connecting to WebSocket:', error)
    }
  }

  disconnectWebSocket() {
    if (this.wsConnection) {
      this.wsConnection.close()
      this.wsConnection = null
    }
  }

  // Error handling wrapper
  async safeExecute(operation, fallback = null) {
    try {
      return await operation()
    } catch (error) {
      console.error('Notification service error:', error)
      return fallback
    }
  }
}

export default new NotificationService() 