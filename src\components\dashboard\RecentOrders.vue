<template>
  <div class="recent-orders">
    <div v-if="loading" class="loading-state">
      <div v-for="i in 5" :key="i" class="order-skeleton"></div>
    </div>
    
    <div v-else-if="!orders.length" class="empty-state">
      <div class="empty-icon">
        <ShoppingCart01Icon class="icon" />
      </div>
      <h3 class="empty-title">{{ $t('dashboard.noOrders') }}</h3>
      <p class="empty-description">{{ $t('dashboard.noOrdersDesc') }}</p>
    </div>
    
    <div v-else class="orders-list">
      <div class="orders-header">
        <span class="header-item">{{ $t('dashboard.order') }}</span>
        <span class="header-item">{{ $t('dashboard.customer') }}</span>
        <span class="header-item">{{ $t('dashboard.value') }}</span>
        <span class="header-item">{{ $t('dashboard.status') }}</span>
      </div>
      
      <div class="orders-body">
        <div 
          v-for="order in orders" 
          :key="order.id"
          class="order-row"
          @click="handleOrderClick(order)"
        >
          <div class="order-item order-id">
            <span class="order-number">#{{ order.id }}</span>
            <span class="order-date">{{ formatDate(order.date) }}</span>
          </div>
          
          <div class="order-item customer-name">
            {{ order.customer }}
          </div>
          
          <div class="order-item order-value">
            R$ {{ formatCurrency(order.value) }}
          </div>
          
          <div class="order-item order-status">
            <span class="status-badge" :class="getStatusClass(order.status)">
              <component :is="getStatusIcon(order.status)" class="status-icon" />
              {{ getStatusText(order.status) }}
            </span>
          </div>
        </div>
      </div>
      
      <div class="orders-footer">
        <IluriaButton 
          variant="ghost" 
          color="dark"
          size="small"
          @click="viewAllOrders"
        >
          {{ $t('dashboard.viewAllOrders') }}
        </IluriaButton>
      </div>
    </div>
  </div>
</template>

<script setup>
import { computed } from 'vue'
import { useRouter } from 'vue-router'
import { useI18n } from 'vue-i18n'
import IluriaButton from '@/components/iluria/IluriaButton.vue'
import {
  ShoppingCart01Icon,
  CheckmarkCircle01Icon,
  ClockIcon,
  TruckIcon,
  CancelCircleIcon
} from '@hugeicons-pro/core-stroke-rounded'

const props = defineProps({
  orders: {
    type: Array,
    default: () => []
  },
  loading: {
    type: Boolean,
    default: false
  }
})

const router = useRouter()
const { t } = useI18n()

const formatDate = (dateString) => {
  const date = new Date(dateString)
  return date.toLocaleDateString('pt-BR', { 
    day: '2-digit', 
    month: '2-digit',
    year: '2-digit'
  })
}

const formatCurrency = (value) => {
  return new Intl.NumberFormat('pt-BR', {
    minimumFractionDigits: 2,
    maximumFractionDigits: 2
  }).format(value)
}

const getStatusClass = (status) => {
  const classes = {
    'paid': 'status-paid',
    'pending': 'status-pending',
    'shipped': 'status-shipped',
    'cancelled': 'status-cancelled'
  }
  return classes[status] || 'status-pending'
}

const getStatusIcon = (status) => {
  const icons = {
    'paid': CheckmarkCircle01Icon,
    'pending': ClockIcon,
    'shipped': TruckIcon,
    'cancelled': CancelCircleIcon
  }
  return icons[status] || ClockIcon
}

const getStatusText = (status) => {
  const texts = {
    'paid': t('dashboard.status.paid'),
    'pending': t('dashboard.status.pending'),
    'shipped': t('dashboard.status.shipped'),
    'cancelled': t('dashboard.status.cancelled')
  }
  return texts[status] || status
}

const handleOrderClick = (order) => {
  router.push(`/orders/${order.id}`)
}

const viewAllOrders = () => {
  router.push('/orders')
}
</script>

<style scoped>
.recent-orders {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.loading-state {
  display: flex;
  flex-direction: column;
  gap: 12px;
  padding: 8px 0;
}

.order-skeleton {
  height: 48px;
  background: linear-gradient(90deg, #f3f4f6 25%, #e5e7eb 50%, #f3f4f6 75%);
  background-size: 200% 100%;
  animation: loading 1.5s infinite;
  border-radius: 8px;
}

@keyframes loading {
  0% { background-position: 200% 0; }
  100% { background-position: -200% 0; }
}

.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  padding: 32px 16px;
  text-align: center;
}

.empty-icon {
  width: 48px;
  height: 48px;
  background: rgba(107, 114, 128, 0.1);
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 16px;
}

.empty-icon .icon {
  width: 24px;
  height: 24px;
  color: var(--iluria-color-text-secondary);
}

.empty-title {
  font-size: 1rem;
  font-weight: 600;
  color: var(--iluria-color-text-primary);
  margin: 0 0 8px 0;
}

.empty-description {
  font-size: 0.875rem;
  color: var(--iluria-color-text-secondary);
  margin: 0;
}

.orders-list {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.orders-header {
  display: grid;
  grid-template-columns: 2fr 2fr 1.5fr 1.5fr;
  gap: 16px;
  padding: 12px 16px;
  background: var(--iluria-color-sidebar-bg);
  border-radius: 8px 8px 0 0;
  border-bottom: 1px solid var(--iluria-color-border);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.header-item {
  font-size: 0.75rem;
  font-weight: 600;
  color: var(--iluria-color-text-secondary);
  text-transform: uppercase;
  letter-spacing: 0.05em;
  transition: color 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.orders-body {
  flex: 1;
  overflow-y: auto;
}

.order-row {
  display: grid;
  grid-template-columns: 2fr 2fr 1.5fr 1.5fr;
  gap: 16px;
  padding: 16px;
  border-bottom: 1px solid var(--iluria-color-border);
  cursor: pointer;
  transition: all 0.2s ease;
}

.order-row:hover {
  background: var(--iluria-color-hover);
}

.order-row:last-child {
  border-bottom: none;
}

.order-item {
  display: flex;
  align-items: center;
  min-width: 0;
}

.order-id {
  flex-direction: column;
  align-items: flex-start;
  gap: 2px;
}

.order-number {
  font-size: 0.875rem;
  font-weight: 600;
  color: var(--iluria-color-text-primary);
  transition: color 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.order-date {
  font-size: 0.75rem;
  color: var(--iluria-color-text-secondary);
  transition: color 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.customer-name {
  font-size: 0.875rem;
  color: var(--iluria-color-text-primary);
  font-weight: 500;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  transition: color 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.order-value {
  font-size: 0.875rem;
  font-weight: 600;
  color: var(--iluria-color-text-primary);
  transition: color 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.status-badge {
  display: inline-flex;
  align-items: center;
  gap: 4px;
  padding: 4px 8px;
  border-radius: 6px;
  font-size: 0.75rem;
  font-weight: 500;
  white-space: nowrap;
}

.status-icon {
  width: 12px;
  height: 12px;
}

.status-paid {
  background: rgba(16, 185, 129, 0.1);
  color: var(--iluria-color-success);
}

.status-pending {
  background: rgba(245, 158, 11, 0.1);
  color: var(--iluria-color-warning);
}

.status-shipped {
  background: rgba(59, 130, 246, 0.1);
  color: var(--iluria-color-info);
}

.status-cancelled {
  background: rgba(239, 68, 68, 0.1);
  color: var(--iluria-color-error);
}

.orders-footer {
  padding: 16px;
  border-top: 1px solid var(--iluria-color-border);
  display: flex;
  justify-content: center;
  transition: border-color 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

@media (max-width: 768px) {
  .orders-header,
  .order-row {
    grid-template-columns: 1fr;
    gap: 8px;
  }
  
  .orders-header {
    display: none;
  }
  
  .order-row {
    padding: 12px;
    border: 1px solid #e5e7eb;
    border-radius: 8px;
    margin-bottom: 8px;
    
  }
  
  .order-item {
    justify-content: space-between;
  }
  
  .order-item::before {
    content: attr(data-label);
    font-size: 0.75rem;
    font-weight: 600;
    color: #6b7280;
    text-transform: uppercase;
    letter-spacing: 0.05em;
  }
  
  .order-id::before {
    content: "Pedido";
  }
  
  .customer-name::before {
    content: "Cliente";
  }
  
  .order-value::before {
    content: "Valor";
  }
  
  .order-status::before {
    content: "Status";
  }
  
  .order-id {
    flex-direction: row;
    justify-content: space-between;
    align-items: center;
  }
}
</style> 
