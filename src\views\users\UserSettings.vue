<template>
  <div class="user-settings-page">
    <!-- User NavBar -->
    <UserNavBar type="user" :showMobileMenu="true" />
    
    <!-- Main Content -->
    <div class="settings-container">
      <!-- Sidebar -->
      <UserSidebar 
        :activeSection="activeSection"
        @section-change="setActiveSection"
      />
      
      <!-- Content Area -->
      <div class="settings-content" @click="handleContentClick">
        <component :is="currentComponent" />
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, watch, onUnmounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { useMobileUserMenu } from '@/composables/useMobileUserMenu'
import UserNavBar from '@/components/layout/UserNavBar.vue'
import UserSidebar from '@/components/user/UserSidebar.vue'
import ProfileSettings from '@/components/user/ProfileSettings.vue'
import SessionsAndLogs from '@/components/user/SessionsAndLogs.vue'
import TwoFactorAuth from '@/components/user/TwoFactorAuth.vue'
import PasswordChanger from '@/components/user/PasswordChanger.vue'
import EmailSettings from '@/components/user/EmailSettings.vue'
import NotificationSettings from '@/components/user/NotificationSettings.vue'
import PreferencesSettings from '@/components/user/PreferencesSettings.vue'
import AccountSettings from '@/components/user/AccountSettings.vue'
import PaymentMethods from '@/components/user/PaymentMethods.vue'
import Subscriptions from '@/components/user/Subscriptions.vue'
import CollaborationInvites from '@/components/user/CollaborationInvites.vue'
import MyStores from '@/components/user/MyStores.vue'

// Router
const route = useRoute()
const router = useRouter()

// Mobile menu composable
const { closeMobileMenu } = useMobileUserMenu()

// State
const activeSection = ref('profile')

// Component mapping
const componentMap = {
  profile: ProfileSettings,
  sessions: SessionsAndLogs,
  twoFactor: TwoFactorAuth,
  password: PasswordChanger,
  contacts: EmailSettings,
  paymentMethods: PaymentMethods,
  subscriptions: Subscriptions,
  notifications: NotificationSettings,
  preferences: PreferencesSettings,
  account: AccountSettings,
  invites: CollaborationInvites,
  myStores: MyStores
}

// Computed
const currentComponent = computed(() => {
  return componentMap[activeSection.value] || ProfileSettings
})

// Methods
const setActiveSection = (section) => {
  activeSection.value = section
  
  // Update URL without navigation
  const currentPath = route.path
  const newUrl = `${currentPath}?section=${section}`
  router.replace(newUrl)
}

const initializeFromQuery = () => {
  const section = route.query.section
  if (section && componentMap[section]) {
    activeSection.value = section
  } else {
    activeSection.value = 'profile'
  }
}

const handleContentClick = () => {
  // Fechar menu mobile quando clicar no conteúdo (só no mobile)
  if (window.innerWidth < 768) {
    closeMobileMenu()
  }
}

// Lifecycle
onMounted(() => {
  initializeFromQuery()
})

onUnmounted(() => {
  // Garantir que o menu seja fechado ao sair da página
  closeMobileMenu()
})

// Watch for query changes
watch(
  () => route.query.section,
  (newSection) => {
    if (newSection && componentMap[newSection]) {
      activeSection.value = newSection
    }
  },
  { immediate: true }
)
</script>

<style scoped>
.user-settings-page {
  min-height: 100vh;
  background-color: var(--iluria-color-body-bg);
  display: flex;
  flex-direction: column;
}

.settings-container {
  flex: 1;
  display: flex;
  background: var(--iluria-color-bg);
  min-height: calc(100vh - 80px); /* Adjust based on navbar height */
}

.settings-content {
  flex: 1;
  padding: 1.5rem;
  overflow-y: auto;
  background: var(--iluria-color-bg);
}

/* Mobile responsiveness */
@media (max-width: 768px) {
  .settings-container {
    flex-direction: row; /* Manter flex-direction row para suportar o overlay */
    min-height: calc(100vh - 80px);
    position: relative;
  }
  
  .settings-content {
    flex: 1;
    width: 100%;
    padding: 1rem;
    /* Garantir que o conteúdo ocupe toda a largura no mobile */
    min-width: 0;
  }
}

@media (max-width: 480px) {
  .settings-content {
    padding: 0.75rem;
  }
}

/* Smooth transitions */
.settings-content {
  transition: all 0.3s ease;
}

/* Custom scrollbar for content area */
.settings-content::-webkit-scrollbar {
  width: 6px;
}

.settings-content::-webkit-scrollbar-track {
  background: transparent;
}

.settings-content::-webkit-scrollbar-thumb {
  background: var(--iluria-color-border);
  border-radius: 3px;
}

.settings-content::-webkit-scrollbar-thumb:hover {
  background: var(--iluria-color-border-hover);
}

/* ViewContainer customization for this page */
.settings-content :deep(.view-container .container-header) {
  padding: 16px 20px;
}

.settings-content :deep(.view-container .container-content) {
  padding: 12px;
}

.settings-content :deep(.view-container .container-content.no-header) {
  padding: 16px 12px;
}

/* Mobile adjustments */
@media (max-width: 768px) {
  .settings-content :deep(.view-container .container-header) {
    padding: 12px 16px;
    gap: 12px;
  }

  .settings-content :deep(.view-container .container-content) {
    padding: 12px;
  }

  .settings-content :deep(.view-container .container-content.no-header) {
    padding: 12px;
  }
}
</style>