<template>
  <IluriaModal 
    v-model="showModal" 
    :title="modalTitle"
    :subtitle="modalDescription"
    :icon="LabelIcon"
    icon-color="blue"
    @save="handleSave"
    @cancel="handleCancel"
    :save-label="saving ? 'Salvando...' : (isEditing ? 'Salvar' : 'Criar')"
    cancel-label="Cancelar"
    :saving="saving"
    :dialog-style="{ 
      width: '700px', 
      maxWidth: '90vw',
      background: 'var(--iluria-color-container-bg)',
      backgroundColor: 'var(--iluria-color-container-bg)',
      color: 'var(--iluria-color-text-primary)'
    }"
    :content-style="{
      background: 'var(--iluria-color-container-bg)',
      backgroundColor: 'var(--iluria-color-container-bg)',
      color: 'var(--iluria-color-text-primary)'
    }"
  >
    <div class="space-y-6">

      <!-- Formulário -->
      <div class="space-y-6">
        <!-- Nome do atributo -->
        <div class="space-y-3" v-if="!isValuesOnlyMode">
                      <label class="block mb-2">
              <span class="text-sm font-semibold text-[var(--iluria-color-text-primary)]">Nome do Atributo</span>
            </label>
          <IluriaInputText
            id="attribute-name"
            name="attributeName"
            v-model="currentAttribute.name"
            placeholder="Ex: Cor, Tamanho, Material, Marca..."
            required
          />
                      <p class="form-hint">
              Este será o nome do atributo que aparecerá nos filtros e na página do produto
            </p>
        </div>

        <!-- Categoria -->
        <div class="space-y-3" v-if="!fixedCategoryId && !isValuesOnlyMode">
          <CategoryDropdown
            v-model="currentAttribute.categoryId"
            :categories="categories"
            label="Categoria"
            placeholder="Selecione uma categoria"
            :showAllOption="true"
            allOptionText="Todas as categorias"
            allOptionValue="ALL"
            required
          />
          <p class="form-hint">
            O atributo será específico para esta categoria e suas subcategorias, ou para todas se selecionado "Todas as categorias"
          </p>
        </div>

        <!-- Categoria fixa (quando vem do produto) -->
        <div v-if="fixedCategoryId && !isValuesOnlyMode" class="space-y-3">
                      <label class="block mb-2">
             <span class="text-sm font-semibold text-[var(--iluria-color-text-primary)]">Categoria</span>
            </label>
          <div class="disabled-field" style="background: var(--iluria-color-surface); border: 1px solid var(--iluria-color-border);">
            <div class="flex items-center space-x-3">
              <svg class="w-5 h-5 text-[var(--color-text-light)]" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                 <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"/>
              </svg>
              <span class="text-sm font-medium text-[var(--iluria-color-text-primary)]">
                {{ fixedCategoryName }}
              </span>
            </div>
            <svg class="w-5 h-5 text-[var(--color-text-light)]" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
              <path fill-rule="evenodd" d="M10 1a4.5 4.5 0 00-4.5 4.5V9H5a2 2 0 00-2 2v6a2 2 0 002 2h10a2 2 0 002-2v-6a2 2 0 00-2-2h-.5V5.5A4.5 4.5 0 0010 1zm3 8V5.5a3 3 0 10-6 0V9h6z" clip-rule="evenodd" />
            </svg>
          </div>
          <p class="form-hint">
            O atributo será criado para a categoria do produto, que não pode ser alterada aqui.
          </p>
        </div>

        <!-- Status -->
        <div v-if="!isValuesOnlyMode">
          <!-- Seletor normal (gerenciador de atributos) -->
          <div v-if="!fixedCategoryId">
            <div class="status-toggle">
              <span class="text-sm font-semibold text-gray-800">Atributo ativo</span>
              <IluriaToggleSwitch
                id="active-switch"
                v-model="currentAttribute.active"
              />
            </div>
            <p class="form-hint mt-2">
              Atributos ativos podem ser usados nos produtos e aparecem nos filtros.
            </p>
          </div>

          <!-- Seletor travado (contexto do produto) -->
          <div v-else>
            <div class="status-toggle disabled">
              <span class="text-sm font-semibold text-[var(--iluria-color-text-primary)]">Atributo ativo</span>
              <div class="flex items-center space-x-3">
                <div class="w-10 h-6 flex items-center rounded-full p-1" style="background-color: #059669; opacity: 0.7;">
                  <div class="w-4 h-4 rounded-full shadow-md transform translate-x-4" style="background: var(--iluria-color-container-bg);"></div>
                </div>
                <svg class="w-5 h-5 text-[var(--iluria-color-text-secondary)]" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                  <path fill-rule="evenodd" d="M10 1a4.5 4.5 0 00-4.5 4.5V9H5a2 2 0 00-2 2v6a2 2 0 002 2h10a2 2 0 002-2v-6a2 2 0 00-2-2h-.5V5.5A4.5 4.5 0 0010 1zm3 8V5.5a3 3 0 10-6 0V9h6z" clip-rule="evenodd" />
                </svg>
              </div>
            </div>
            <p class="form-hint mt-2">
              Atributos criados na página do produto são sempre ativos.
            </p>
          </div>
        </div>

        <!-- Valores -->
        <div class="space-y-3">
          <label class="block mb-2">
            <span class="text-sm font-semibold text-[var(--iluria-color-text-primary)]">Valores</span>
            <span v-if="isValuesOnlyMode || !isEditing" class="text-xs font-normal text-[var(--iluria-color-text-secondary)] ml-2">(opcional)</span>
          </label>
          
          <!-- Input para adicionar novo valor -->
          <div class="space-y-3">
            <div class="flex items-center gap-3">
              <IluriaInputText
                id="new-value-input"
                name="newValue"
                v-model="newValueInput"
                @keydown.enter.prevent="addValue"
                placeholder="Digite um valor e pressione Enter"
                input-class="flex-1"
              />
              <IluriaButton
                color="dark"
                :huge-icon="Add01Icon"
                @click="addValue"
                :disabled="!newValueInput.trim()"
                class="shrink-0"
              >
                Adicionar
              </IluriaButton>
            </div>
            
            <p class="form-hint">
              Adicione valores um por vez. Você pode editar e remover valores depois.
            </p>
          </div>


          <!-- Lista de valores -->
          <div v-if="currentAttribute.values && currentAttribute.values.length > 0">
            <div class="flex items-center justify-between">
              <label class="block mb-2">
                <span class="text-sm font-semibold text-[var(--iluria-color-text-primary)]">Valores Adicionados ({{ filteredValues.length }}/{{ currentAttribute.values.length }})</span>
              </label>
              
              <!-- Caixa de pesquisa -->
              <div class="relative" v-if="currentAttribute.values.length > 3">
                <IluriaInputText
                  id="search-values"
                  name="searchValues"
                  v-model="searchQuery"
                  placeholder="Pesquisar valores..."
                  input-class="search-input"
                />
              </div>
            </div>
            
            <div class="p-4 rounded-lg max-h-48 overflow-y-auto" style="background: var(--iluria-color-surface); border: 1px solid var(--iluria-color-border);">
              <!-- Aviso quando há pesquisa ativa -->
              <div v-if="searchQuery.trim() && filteredValues.length < currentAttribute.values.length" class="mb-3 p-2 rounded text-xs transition-all duration-200" style="background: var(--iluria-color-container-bg); border: 1px solid var(--iluria-color-border); color: var(--iluria-color-text-primary);">
                Mostrando {{ filteredValues.length }} de {{ currentAttribute.values.length }} valores. 
                <button @click="searchQuery = ''" class="underline hover:no-underline">Limpar pesquisa</button>
              </div>
              
              <div class="space-y-2">
                <div
                  v-for="item in filteredValues"
                  :key="item.originalIndex"
                  class="flex items-center justify-between p-2 rounded transition-all duration-200"
                  style="background: var(--iluria-color-container-bg); border: 1px solid var(--iluria-color-border);"
                  @mouseenter="$event.target.style.boxShadow = 'var(--iluria-shadow-sm)'"
                  @mouseleave="$event.target.style.boxShadow = 'none'"
                >
                  <div class="flex-1">
                    <input
                      v-if="editingValueIndex === item.originalIndex"
                      type="text"
                      v-model="editingValueText"
                      @keydown.enter.prevent="saveValueEdit"
                      @keydown.escape.prevent="cancelValueEdit"
                      @blur="saveValueEdit"
                      class="value-edit-input"
                      ref="editInput"
                    />
                    <span v-else class="text-sm text-[var(--iluria-color-text-primary)]">{{ item.value }}</span>
                  </div>
                  
                  <div class="flex items-center gap-1 ml-2">
                    <button
                      v-if="editingValueIndex !== item.originalIndex"
                      type="button"
                      @click="startEditValue(item.originalIndex, item.value)"
                      class="action-btn edit-btn"
                      title="Editar valor"
                    >
                      <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"/>
                      </svg>
                    </button>
                    
                    <button
                      v-if="editingValueIndex === item.originalIndex"
                      type="button"
                      @click="saveValueEdit"
                      class="action-btn save-btn"
                      title="Salvar"
                    >
                      <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"/>
                      </svg>
                    </button>
                    
                    <button
                      v-if="editingValueIndex === item.originalIndex"
                      type="button"
                      @click="cancelValueEdit"
                      class="action-btn cancel-btn"
                      title="Cancelar"
                    >
                      <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"/>
                      </svg>
                    </button>
                    
                    <button
                      v-if="editingValueIndex !== item.originalIndex"
                      type="button"
                      @click="removeValue(item.originalIndex)"
                      class="action-btn delete-btn"
                      title="Remover valor"
                    >
                      <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"/>
                      </svg>
                    </button>
                  </div>
                </div>
                
                <!-- Estado vazio da pesquisa -->
                <div v-if="searchQuery.trim() && filteredValues.length === 0" class="text-center py-6">
                  <svg class="w-8 h-8 mx-auto text-[var(--iluria-color-text-secondary)] mb-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"/>
                  </svg>
                  <p class="text-sm text-[var(--iluria-color-text-secondary)]">Nenhum valor encontrado</p>
                  <button @click="searchQuery = ''" class="text-xs underline mt-1 transition-all duration-200" style="color: var(--iluria-color-primary, #3b82f6);">
                    Limpar pesquisa
                  </button>
                </div>
              </div>
            </div>
          </div>
          
          <!-- Estado vazio dos valores -->
          <div v-else class="text-center py-6 rounded-lg" style="background: var(--iluria-color-surface); box-shadow: var(--iluria-shadow-sm);">
            <svg class="w-10 h-10 mx-auto text-[var(--iluria-color-text-secondary)] mb-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"/>
            </svg>
            <p class="text-sm font-medium text-[var(--iluria-color-text-primary)]">Nenhum valor adicionado</p>
            <p class="text-xs text-[var(--iluria-color-text-secondary)] mt-1">
              Use o campo acima para adicionar o primeiro valor
            </p>
          </div>
        </div>
      </div>
    </div>
  </IluriaModal>
</template>

<script setup>
import { ref, computed, watch, nextTick } from 'vue';
import { useI18n } from 'vue-i18n';
import { LabelIcon, Add01Icon } from '@hugeicons-pro/core-stroke-rounded';
import IluriaModal from '@/components/iluria/IluriaModal.vue';
import IluriaInputText from '@/components/iluria/form/IluriaInputText.vue';
import CategoryDropdown from '@/components/iluria/CategoryDropdown.vue';
import IluriaToggleSwitch from '@/components/iluria/IluriaToggleSwitch.vue';
import IluriaButton from '@/components/iluria/IluriaButton.vue';

// Emits
const emit = defineEmits(['update:visible', 'save', 'cancel']);

// Props
const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  isEditing: {
    type: Boolean,
    default: false
  },
  attribute: {
    type: Object,
    default: () => ({ name: '', categoryId: null, active: true, values: [] })
  },
  categories: {
    type: Array,
    default: () => []
  },
  saving: {
    type: Boolean,
    default: false
  },
  mode: {
    type: String,
    default: 'attribute', // 'attribute' ou 'value'
    validator: (value) => ['attribute', 'value'].includes(value)
  },
  fixedCategoryId: {
    type: String,
    default: null
  },
  fixedCategoryName: {
    type: String,
    default: ''
  },
  attributeNameForValuesMode: {
    type: String,
    default: ''
  }
});

const { t } = useI18n();

// Estado local
const showModal = ref(props.visible);
const currentAttribute = ref({});
const newValueInput = ref('');
const searchQuery = ref('');
const editingValueIndex = ref(null);
const editingValueText = ref('');
const editInput = ref(null);

// Computed
const isValuesOnlyMode = computed(() => props.mode === 'value');

const modalTitle = computed(() => {
  if (isValuesOnlyMode.value) {
    return `Gerenciar Valores de "${props.attributeNameForValuesMode}"`;
  }
  return props.isEditing ? 'Editar Atributo' : 'Criar Novo Atributo';
});

const modalDescription = computed(() => {
  if (isValuesOnlyMode.value) {
    return 'Adicione, edite ou remova os valores para este atributo.';
  }
  
  let description = props.isEditing ? 'Atualize as informações do atributo.' : 'Configure um novo atributo para organizar seus produtos.';
  
  if (props.fixedCategoryName && !isValuesOnlyMode.value) {
    description += ` Para a categoria: ${props.fixedCategoryName}`;
  }
  
  return description;
});

const filteredValues = computed(() => {
  if (!currentAttribute.value.values) return [];
  
  const items = currentAttribute.value.values.map((value, index) => ({ value, originalIndex: index }));

  if (!searchQuery.value.trim()) {
    return items;
  }
  return items.filter(item =>
    item.value.toLowerCase().includes(searchQuery.value.toLowerCase())
  );
});

// Funções de manipulação de valores
const addValue = () => {
  const value = newValueInput.value.trim();
  if (value) {
    const valuesArray = currentAttribute.value.values || [];
    const valueExists = valuesArray.some(
      existingValue => existingValue.toLowerCase() === value.toLowerCase()
    );

    if (!valueExists) {
      if (!currentAttribute.value.values) {
        currentAttribute.value.values = [];
      }
      currentAttribute.value.values.push(value);
      newValueInput.value = '';
    }
  }
};

const removeValue = (index) => {
  if (currentAttribute.value.values && index >= 0 && index < currentAttribute.value.values.length) {
    currentAttribute.value.values.splice(index, 1);
  }
};

const startEditValue = (index, value) => {
  editingValueIndex.value = index;
  editingValueText.value = value;
  nextTick(() => {
    if (editInput.value && Array.isArray(editInput.value) && editInput.value[0]) {
      editInput.value[0].focus();
    } else if (editInput.value) {
      editInput.value.focus();
    }
  });
};

const saveValueEdit = () => {
  const index = editingValueIndex.value;
  if (index === null) return;

  const newValue = editingValueText.value.trim();
  
  const existingIndex = (currentAttribute.value.values || []).findIndex(
    (v, i) => i !== index && v.toLowerCase() === newValue.toLowerCase()
  );

  if (newValue && existingIndex === -1) {
    currentAttribute.value.values[index] = newValue;
  }
  
  cancelValueEdit();
};

const cancelValueEdit = () => {
  editingValueIndex.value = null;
  editingValueText.value = '';
};

// Handlers do Modal
const handleSave = () => {
  // Em modo de valores, o componente pai só precisa da lista de valores
  if (isValuesOnlyMode.value) {
    emit('save', { values: currentAttribute.value.values });
    return;
  }

  // Validação para modo de atributo
  if (!currentAttribute.value.name) {
    console.error("O nome do atributo é obrigatório."); // Substituir por toast
    return;
  }
  if (!currentAttribute.value.categoryId && !props.fixedCategoryId) {
    console.error("A categoria é obrigatória."); // Substituir por toast
    return;
  }

  // Garante que o ID da categoria fixa seja usado se presente
  if (props.fixedCategoryId) {
    currentAttribute.value.categoryId = props.fixedCategoryId;
  }
  
  // No modo de criação, os valores são passados como initialValues
  if (!props.isEditing) {
    const attributeData = { ...currentAttribute.value, initialValues: currentAttribute.value.values };
    delete attributeData.values; // Limpa para evitar confusão no backend
    emit('save', attributeData);
  } else {
    emit('save', currentAttribute.value);
  }
};

const handleCancel = () => {
  emit('update:visible', false);
  emit('cancel');
};

// Watchers
watch(() => props.visible, (newVal) => {
  showModal.value = newVal;
  if (newVal) {
    // Clona o objeto do prop para evitar mutações diretas
    currentAttribute.value = JSON.parse(JSON.stringify(props.attribute));

    // Garante que `values` seja sempre um array
    if (!currentAttribute.value.values) {
      currentAttribute.value.values = [];
    }
    
    // Reseta inputs e estado de edição
    newValueInput.value = '';
    searchQuery.value = '';
    cancelValueEdit();
  }
});

watch(showModal, (newVal) => {
  if (!newVal) {
    emit('update:visible', false);
  }
});
</script>

<style scoped>
/* Estilo para campo desabilitado */
.disabled-field {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0.75rem 1rem;
  border-radius: 0.5rem;
  opacity: 0.7;
}

/* Estilo para toggle desabilitado */
.status-toggle {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.status-toggle.disabled {
  opacity: 0.7;
}

/* Estilo para input de edição de valor */
.value-edit-input {
  width: 100%;
  padding: 0.25rem 0.5rem;
  border: 1px solid var(--iluria-color-border);
  border-radius: 0.25rem;
  background: var(--iluria-color-container-bg);
  color: var(--iluria-color-text-primary);
  font-size: 0.875rem;
  transition: all 0.2s ease;
}

.value-edit-input:focus {
  outline: none;
  border-color: var(--iluria-color-primary, #3b82f6);
  box-shadow: var(--iluria-shadow-sm);
}

/* Estilo para botões de ação dos valores */
.action-btn {
  padding: 0.25rem;
  border-radius: 0.25rem;
  border: none;
  background: none;
  cursor: pointer;
  transition: all 0.2s ease;
  outline: none !important;
}

.action-btn:focus {
  outline: none !important;
  box-shadow: none !important;
}

.edit-btn {
  color: var(--iluria-color-text-secondary);
}

.edit-btn:hover {
  color: var(--iluria-color-primary, #3b82f6);
  background: var(--iluria-color-surface);
}

.save-btn {
  color: #059669;
}

.save-btn:hover {
  background: var(--iluria-color-surface);
}

.cancel-btn {
  color: var(--iluria-color-text-secondary);
}

.cancel-btn:hover {
  color: var(--iluria-color-text-primary);
  background: var(--iluria-color-surface);
}

.delete-btn {
  color: var(--iluria-color-text-secondary);
}

.delete-btn:hover {
  color: #dc2626;
  background: var(--iluria-color-surface);
}

/* Estilo para hint de formulário */
.form-hint {
  color: var(--iluria-color-text-secondary);
  font-size: 0.875rem;
  line-height: 1.25rem;
}

/* Estilo para input de pesquisa */
.search-input {
  width: 200px;
  padding: 0.5rem;
  border: 1px solid var(--iluria-color-border);
  border-radius: 0.375rem;
  background: var(--iluria-color-container-bg);
  color: var(--iluria-color-text-primary);
  font-size: 0.875rem;
  transition: all 0.2s ease;
}

.search-input:focus {
  outline: none;
  border-color: var(--iluria-color-primary, #3b82f6);
  box-shadow: var(--iluria-shadow-sm);
}
/* Forçar tema correto em todos os elementos do modal */
:deep(.iluria-modal) {
  background: var(--iluria-color-container-bg) !important;
  color: var(--iluria-color-text-primary) !important;
}

:deep(.iluria-modal *) {
  background-color: inherit !important;
}

:deep(.iluria-modal .bg-white),
:deep(.iluria-modal [style*="background-color: white"]),
:deep(.iluria-modal [style*="background-color: #ffffff"]),
:deep(.iluria-modal [style*="background: white"]),
:deep(.iluria-modal [style*="background: #ffffff"]) {
  background: var(--iluria-color-container-bg) !important;
  background-color: var(--iluria-color-container-bg) !important;
}
  </style>
  
