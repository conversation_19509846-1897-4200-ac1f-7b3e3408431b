/**
 * Base de dados das telas do sistema para busca rápida
 * 
 * Este arquivo mapeia todas as telas disponíveis no sistema, incluindo:
 * - Rotas automáticas extraídas do router
 * - Configurações manuais para casos especiais
 * - Metadados para busca e categorização
 * - Permissões necessárias para acesso a cada tela
 */

import { PERMISSION_CODES } from '@/constants/permissions';

import {
  InvoiceIcon,
  DiscountTag01Icon,
  DollarSquareIcon,
  GiftIcon,
  UserIcon,
  CouponPercentIcon,
  AnalysisTextLinkIcon,
  PackageAddIcon,
  DashboardSquare03Icon,
  SearchList01Icon,
  FilterIcon,
  Bookmark02Icon,
  TapeMeasureIcon,
  ArrangeIcon,
  LeftToRightListBulletIcon,
  MessageUser02Icon,
  MessageMultiple01Icon,
  MailLove01Icon,
  Target02Icon,
  StoreLocation02Icon,
  ShippingTruck01Icon,
  InternetIcon,
  File01Icon,
  Mailbox01Icon,
  Payment02Icon,
  UserGroupIcon,
  Notification01Icon,
  CardExchange02Icon,
  BeachIcon,
  PaintBoardIcon,
  HierarchyFilesIcon,
  Image02Icon,
  LicenseDraftIcon,
  HugeiconsIcon
} from '@hugeicons-pro/core-stroke-rounded';

/**
 * Categorias das telas para organização
 */
export const screenCategories = {
  sales: {
    name: 'Vendas',
    icon: InvoiceIcon,
    color: '#4A90E2'
  },
  products: {
    name: 'Produtos', 
    icon: PackageAddIcon,
    color: '#7ED321'
  },
  marketing: {
    name: 'Marketing',
    icon: Target02Icon,
    color: '#8B5CF6'
  },
  settings: {
    name: 'Configurações',
    icon: StoreLocation02Icon,
    color: '#F5A623'
  },
  layout: {
    name: 'Layout',
    icon: PaintBoardIcon,
    color: '#417505'
  },
  customers: {
    name: 'Clientes',
    icon: UserIcon,
    color: '#D0021B'
  }
};

/**
 * Mapeamento completo das telas do sistema
 */
export const screens = {
  // === HOME ===
  '/': {
    title: 'Dashboard',
    description: 'Visão geral da loja e métricas principais',
    category: 'sales',
    icon: DashboardSquare03Icon,
    keywords: ['dashboard', 'inicio', 'home', 'visão geral', 'metricas'],
    priority: 'high',
    requiredPermissions: [PERMISSION_CODES.STORE_VIEW]
  },

  // === VENDAS ===
  '/orders': {
    title: 'Pedidos',
    description: 'Lista de pedidos realizados na loja',
    category: 'sales',
    icon: InvoiceIcon,
    keywords: ['pedidos', 'orders', 'vendas', 'compras'],
    priority: 'high',
    requiredPermissions: [PERMISSION_CODES.ORDER_VIEW]
  },
  '/orders/new': {
    title: 'Novo Pedido',
    description: 'Criar um novo pedido manualmente',
    category: 'sales',
    icon: InvoiceIcon,
    keywords: ['novo pedido', 'criar pedido', 'adicionar pedido'],
    requiredPermissions: [PERMISSION_CODES.ORDER_EDIT]
  },
  '/customer-list': {
    title: 'Clientes',
    description: 'Lista de clientes cadastrados na loja',
    category: 'customers',
    icon: UserIcon,
    keywords: ['clientes', 'customers', 'usuarios', 'compradores'],
    priority: 'high',
    requiredPermissions: [PERMISSION_CODES.CUSTOMER_VIEW]
  },
  '/customers/new': {
    title: 'Novo Cliente',
    description: 'Cadastrar um novo cliente',
    category: 'customers',
    icon: UserIcon,
    keywords: ['novo cliente', 'cadastrar cliente', 'adicionar cliente'],
    requiredPermissions: [PERMISSION_CODES.CUSTOMER_EDIT]
  },
  '/customer/coupon-manager': {
    title: 'Cupons de Desconto',
    description: 'Criar e gerenciar cupons de desconto',
    category: 'sales',
    icon: DiscountTag01Icon,
    keywords: ['cupons', 'desconto', 'promocoes', 'voucher'],
    priority: 'medium',
    requiredPermissions: [PERMISSION_CODES.MARKETING_DISCOUNTS]
  },
  '/settings/minimum-order': {
    title: 'Pedido Mínimo',
    description: 'Configurar valor mínimo de pedido',
    category: 'settings',
    icon: DollarSquareIcon,
    keywords: ['pedido minimo', 'valor minimo', 'configuracao'],
    requiredPermissions: [PERMISSION_CODES.STORE_SETTINGS]
  },
  '/promotions': {
    title: 'Promoções',
    description: 'Gerenciar promoções e descontos da loja',
    category: 'sales',
    icon: CouponPercentIcon,
    keywords: ['promocoes', 'descontos', 'ofertas', 'campanhas'],
    requiredPermissions: [PERMISSION_CODES.PROMOTION_VIEW]
  },

  // === PRODUTOS ===
  '/products': {
    title: 'Lista de Produtos',
    description: 'Produtos disponíveis na loja',
    category: 'products',
    icon: SearchList01Icon,
    keywords: ['produtos', 'lista produtos', 'catalogo', 'items'],
    priority: 'high',
    requiredPermissions: [PERMISSION_CODES.PRODUCT_VIEW]
  },
  '/products/new': {
    title: 'Novo Produto',
    description: 'Criar novos produtos físicos ou digitais',
    category: 'products',
    icon: PackageAddIcon,
    keywords: ['novo produto', 'criar produto', 'adicionar produto', 'cadastrar produto'],
    priority: 'high',
    requiredPermissions: [PERMISSION_CODES.PRODUCT_CREATE]
  },
  '/product/category-manager': {
    title: 'Categorias',
    description: 'Gerenciar categorias dos produtos',
    category: 'products',
    icon: LeftToRightListBulletIcon,
    keywords: ['categorias', 'classificacao', 'grupos', 'organizacao'],
    requiredPermissions: [PERMISSION_CODES.PRODUCT_CATEGORY_MANAGE]
  },
  '/product/attributes-manager': {
    title: 'Atributos & Filtros',
    description: 'Gerenciar atributos e filtros dos produtos',
    category: 'products',
    icon: FilterIcon,
    keywords: ['atributos', 'filtros', 'caracteristicas', 'propriedades'],
    requiredPermissions: [PERMISSION_CODES.PRODUCT_EDIT]
  },
  '/product/collection/collection-product-list': {
    title: 'Coleções',
    description: 'Cadastrar coleções de produtos',
    category: 'products',
    icon: DashboardSquare03Icon,
    keywords: ['colecoes', 'grupos produtos', 'conjuntos']
  },
  '/products/combinado': {
    title: 'Produto Combinado',
    description: 'Combine produtos para criar produto único',
    category: 'products',
    icon: GiftIcon,
    keywords: ['produto combinado', 'combo', 'kit', 'pacote']
  },
  '/products/questions-answers': {
    title: 'Perguntas e Respostas',
    description: 'Gerenciar perguntas dos produtos',
    category: 'products',
    icon: MessageUser02Icon,
    keywords: ['perguntas', 'respostas', 'duvidas', 'faq']
  },
  '/product/label/label-initial': {
    title: 'Etiquetas',
    description: 'Gerenciar etiquetas dos produtos',
    category: 'products',
    icon: Bookmark02Icon,
    keywords: ['etiquetas', 'labels', 'tags', 'marcacoes']
  },
  '/measurement-tables': {
    title: 'Tabelas de Medidas',
    description: 'Criar tabelas de medidas para produtos',
    category: 'products',
    icon: TapeMeasureIcon,
    keywords: ['medidas', 'tamanhos', 'dimensoes', 'tabela medidas']
  },
  '/products/orders': {
    title: 'Organizar Produtos',
    description: 'Definir ordem de exibição dos produtos',
    category: 'products',
    icon: ArrangeIcon,
    keywords: ['organizar produtos', 'ordem', 'posicao', 'classificacao']
  },
  '/products/gift-card': {
    title: 'Cartões Presente',
    description: 'Gerenciar cartões presente da loja',
    category: 'products',
    icon: GiftIcon,
    keywords: ['cartao presente', 'gift card', 'voucher', 'presente']
  },

  // === MARKETING ===
  '/marketing/community': {
    title: 'Comunidade',
    description: 'Gerenciar comunidade de clientes',
    category: 'marketing',
    icon: MessageMultiple01Icon,
    keywords: ['comunidade', 'forum', 'interacao', 'engajamento']
  },
  '/blog': {
    title: 'Blog',
    description: 'Gerenciar postagens do blog',
    category: 'marketing',
    icon: LicenseDraftIcon,
    keywords: ['blog', 'artigos', 'posts', 'conteudo']
  },
  '/marketing/newsletter': {
    title: 'Newsletter',
    description: 'Gerenciar inscrições e envios',
    category: 'marketing',
    icon: MailLove01Icon,
    keywords: ['newsletter', 'email marketing', 'mailing', 'inscricoes']
  },
  '/settings/store-seo': {
    title: 'SEO da Página Principal',
    description: 'Configurações de SEO da loja',
    category: 'marketing',
    icon: Target02Icon,
    keywords: ['seo', 'otimizacao', 'google', 'busca', 'metatags']
  },

  // === CONFIGURAÇÕES ===
  '/settings': {
    title: 'Dados da Loja',
    description: 'Informações gerais da loja',
    category: 'settings',
    icon: StoreLocation02Icon,
    keywords: ['dados loja', 'informacoes', 'configuracoes gerais'],
    priority: 'high',
    requiredPermissions: [PERMISSION_CODES.STORE_SETTINGS]
  },
  '/settings/shipping': {
    title: 'Frete',
    description: 'Correios, entrega local e configurações',
    category: 'settings',
    icon: ShippingTruck01Icon,
    keywords: ['frete', 'entrega', 'correios', 'shipping', 'envio'],
    priority: 'medium',
    requiredPermissions: [PERMISSION_CODES.SHIPPING_CONFIG]
  },
  '/settings/domain-manager': {
    title: 'Domínios',
    description: 'Configurações de domínios da loja',
    category: 'settings',
    icon: InternetIcon,
    keywords: ['dominios', 'dns', 'url', 'endereco']
  },
  '/pages': {
    title: 'Páginas',
    description: 'Criar e editar páginas personalizadas',
    category: 'settings',
    icon: File01Icon,
    keywords: ['paginas', 'conteudo', 'cms', 'estaticas']
  },
  '/settings/origin-cep': {
    title: 'CEP da Loja',
    description: 'Definir CEP de origem para frete',
    category: 'settings',
    icon: Mailbox01Icon,
    keywords: ['cep', 'origem', 'endereco', 'localizacao']
  },
  '/settings/payment-methods': {
    title: 'Meios de Pagamento',
    description: 'Gerenciar formas de pagamento',
    category: 'settings',
    icon: Payment02Icon,
    keywords: ['pagamento', 'cartao', 'pix', 'boleto', 'gateway'],
    priority: 'medium'
  },
  '/settings/social-media': {
    title: 'Redes Sociais',
    description: 'Configurar Facebook, Instagram, etc',
    category: 'settings',
    icon: UserGroupIcon,
    keywords: ['redes sociais', 'facebook', 'instagram', 'twitter', 'social']
  },
  '/settings/email-notifications': {
    title: 'Notificações por Email',
    description: 'Configurar notificações de email',
    category: 'settings',
    icon: Notification01Icon,
    keywords: ['notificacoes', 'email', 'alertas', 'avisos']
  },
  '/settings/url-redirect': {
    title: 'Redirecionamentos',
    description: 'Gerenciar redirecionamentos de URLs',
    category: 'settings',
    icon: CardExchange02Icon,
    keywords: ['redirect', 'redirecionamento', 'url', '301', '302']
  },
  '/settings/store-mode': {
    title: 'Férias e Manutenção',
    description: 'Ativar modos de manutenção ou férias',
    category: 'settings',
    icon: BeachIcon,
    keywords: ['ferias', 'manutencao', 'modo', 'temporario', 'pausar']
  },
  '/settings/brand-assets': {
    title: 'Logo & Favicon',
    description: 'Configurar logo e favicon da loja',
    category: 'layout',
    icon: Image02Icon,
    keywords: ['logo', 'favicon', 'marca', 'identidade', 'visual']
  },

  // === LAYOUT ===
  '/themes': {
    title: 'Layout',
    description: 'Gerenciar temas e personalização',
    category: 'layout',
    icon: PaintBoardIcon,
    keywords: ['layout', 'tema', 'design', 'cores', 'personalizacao'],
    priority: 'medium'
  },
  '/filemanager': {
    title: 'Gerenciamento de Arquivos',
    description: 'Gerenciar arquivos e pastas do layout',
    category: 'layout',
    icon: HierarchyFilesIcon,
    keywords: ['arquivos', 'pastas', 'midia', 'imagens', 'files']
  }
};

/**
 * Função para buscar telas baseada em query
 * @param {string} query - Termo de busca
 * @param {string} category - Categoria para filtrar (opcional)
 * @returns {Array} Lista de telas encontradas
 */
export function searchScreens(query, category = null) {
  if (!query || query.length < 2) return [];
  
  const searchTerm = query.toLowerCase().trim();
  const results = [];
  
  Object.entries(screens).forEach(([path, screen]) => {
    // Filtrar por categoria se especificada
    if (category && screen.category !== category) return;
    
    // Calcular pontuação de relevância
    let score = 0;
    
    // Busca no título (peso maior)
    if (screen.title.toLowerCase().includes(searchTerm)) {
      score += 100;
      if (screen.title.toLowerCase().startsWith(searchTerm)) {
        score += 50; // Bonus para matches no início
      }
    }
    
    // Busca na descrição
    if (screen.description.toLowerCase().includes(searchTerm)) {
      score += 50;
    }
    
    // Busca nas keywords - verifica se alguma keyword contém o termo de busca
    const keywordMatch = screen.keywords.some(keyword => 
      keyword.toLowerCase().includes(searchTerm)
    );
    if (keywordMatch) {
      score += 75;
    }
    
    // Busca no path
    if (path.toLowerCase().includes(searchTerm)) {
      score += 25;
    }
    
    // Bonus para telas prioritárias APENAS se houve algum match
    if (score > 0) {
      if (screen.priority === 'high') score += 20;
      if (screen.priority === 'medium') score += 10;
    }
    
    if (score > 0) {
      results.push({
        ...screen,
        path,
        score
      });
    }
  });
  
  // Ordenar por relevância
  return results.sort((a, b) => b.score - a.score);
}

/**
 * Função para obter telas por categoria
 * @param {string} category - Categoria desejada
 * @returns {Array} Lista de telas da categoria
 */
export function getScreensByCategory(category) {
  return Object.entries(screens)
    .filter(([_, screen]) => screen.category === category)
    .map(([path, screen]) => ({ ...screen, path }))
    .sort((a, b) => {
      // Ordenar por prioridade e depois por título
      const priorityOrder = { high: 3, medium: 2, low: 1 };
      const aPriority = priorityOrder[a.priority] || 0;
      const bPriority = priorityOrder[b.priority] || 0;
      
      if (aPriority !== bPriority) {
        return bPriority - aPriority;
      }
      
      return a.title.localeCompare(b.title);
    });
}

/**
 * Função para obter telas mais acessadas (baseado em localStorage)
 * @returns {Array} Lista de telas recentes
 */
export function getRecentScreens() {
  try {
    const recent = JSON.parse(localStorage.getItem('iluria_recent_screens') || '[]');
    return recent
      .map(path => ({ ...screens[path], path }))
      .filter(screen => screen.title) // Filtrar telas que ainda existem
      .slice(0, 5); // Últimas 5
  } catch {
    return [];
  }
}

/**
 * Função para adicionar tela ao histórico de recentes
 * @param {string} path - Path da tela visitada
 */
export function addToRecentScreens(path) {
  try {
    let recent = JSON.parse(localStorage.getItem('iluria_recent_screens') || '[]');
    
    // Remover se já existe
    recent = recent.filter(item => item !== path);
    
    // Adicionar no início
    recent.unshift(path);
    
    // Manter apenas os últimos 10
    recent = recent.slice(0, 10);
    
    localStorage.setItem('iluria_recent_screens', JSON.stringify(recent));
  } catch {
    // Ignorar erros de localStorage
  }
}

/**
 * Verifica se o usuário tem permissão para acessar uma tela
 * @param {Object} screen - Objeto da tela com requiredPermissions
 * @param {Function} hasPermissionFn - Função para verificar permissão
 * @returns {boolean} Se tem permissão para acessar
 */
export function hasScreenAccess(screen, hasPermissionFn) {
  // Se não tem permissões definidas, assume acesso liberado
  if (!screen.requiredPermissions || screen.requiredPermissions.length === 0) {
    return true;
  }

  // Verifica se tem pelo menos uma das permissões necessárias (OR)
  return screen.requiredPermissions.some(permission => hasPermissionFn(permission));
}

/**
 * Filtra screens baseado nas permissões do usuário
 * @param {Object} screensObj - Objeto com todas as telas
 * @param {Function} hasPermissionFn - Função para verificar permissão
 * @returns {Object} Objeto filtrado com apenas telas acessíveis
 */
export function filterScreensByPermissions(screensObj, hasPermissionFn) {
  const filteredScreens = {};
  
  Object.entries(screensObj).forEach(([path, screen]) => {
    if (hasScreenAccess(screen, hasPermissionFn)) {
      filteredScreens[path] = screen;
    }
  });
  
  return filteredScreens;
}

/**
 * Busca telas baseada em query E permissões do usuário
 * @param {string} query - Termo de busca
 * @param {Function} hasPermissionFn - Função para verificar permissão
 * @param {string} category - Categoria para filtrar (opcional)
 * @returns {Array} Lista de telas encontradas que o usuário pode acessar
 */
export function searchScreensWithPermissions(query, hasPermissionFn, category = null) {
  if (!query || query.length < 2) return [];
  
  // Primeiro, filtra por permissões
  const accessibleScreens = filterScreensByPermissions(screens, hasPermissionFn);
  
  // Em seguida, aplica a busca normal
  const searchTerm = query.toLowerCase().trim();
  const results = [];
  
  Object.entries(accessibleScreens).forEach(([path, screen]) => {
    // Filtrar por categoria se especificada
    if (category && screen.category !== category) return;
    
    // Calcular pontuação de relevância
    let score = 0;
    
    // Busca no título (peso maior)
    if (screen.title.toLowerCase().includes(searchTerm)) {
      score += 100;
      if (screen.title.toLowerCase().startsWith(searchTerm)) {
        score += 50; // Bonus para matches no início
      }
    }
    
    // Busca na descrição
    if (screen.description.toLowerCase().includes(searchTerm)) {
      score += 50;
    }
    
    // Busca nas keywords - verifica se alguma keyword contém o termo de busca
    const keywordMatch = screen.keywords.some(keyword => 
      keyword.toLowerCase().includes(searchTerm)
    );
    if (keywordMatch) {
      score += 75;
    }
    
    // Busca no path
    if (path.toLowerCase().includes(searchTerm)) {
      score += 25;
    }
    
    // Bonus para telas prioritárias APENAS se houve algum match
    if (score > 0) {
      if (screen.priority === 'high') score += 20;
      if (screen.priority === 'medium') score += 10;
    }
    
    if (score > 0) {
      results.push({
        ...screen,
        path,
        score
      });
    }
  });
  
  // Ordenar por relevância
  return results.sort((a, b) => b.score - a.score);
}

/**
 * Obtém telas recentes filtradas por permissões
 * @param {Function} hasPermissionFn - Função para verificar permissão
 * @returns {Array} Lista de telas recentes que o usuário pode acessar
 */
export function getRecentScreensWithPermissions(hasPermissionFn) {
  try {
    const recent = JSON.parse(localStorage.getItem('iluria_recent_screens') || '[]');
    return recent
      .map(path => ({ ...screens[path], path }))
      .filter(screen => screen.title) // Filtrar telas que ainda existem
      .filter(screen => hasScreenAccess(screen, hasPermissionFn)) // Filtrar por permissões
      .slice(0, 5); // Últimas 5
  } catch {
    return [];
  }
}